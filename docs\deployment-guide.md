# CanTV钉钉后端服务部署指南

## 概述

本文档介绍如何使用阿里云云效和 Docker Compose 进行 CanTV 钉钉后端服务的 CI/CD 部署。

## 目录结构

```
├── .flow.yml                    # 云效流水线配置文件
├── docker-compose.yml          # 开发环境 Docker Compose 配置
├── docker-compose.prod.yml     # 生产环境 Docker Compose 配置
├── nginx/                      # Nginx 配置
│   ├── nginx.conf              # Nginx 主配置文件
│   ├── ssl/                    # SSL 证书目录
│   └── logs/                   # Nginx 日志目录
├── .env.example                # 环境变量示例文件
├── .env.dev                    # 开发环境变量
├── .env.prod                   # 生产环境变量
└── scripts/                    # 部署脚本
    ├── deploy.sh               # Docker Compose 部署脚本
    └── codeup-deploy.sh        # 云效专用部署脚本
```

## 云效流水线配置

### 1. 环境变量设置

在云效项目中设置以下环境变量：

#### Docker Registry 相关
- `DOCKER_USERNAME`: 阿里云容器镜像服务用户名
- `DOCKER_PASSWORD`: 阿里云容器镜像服务密码
- `DOCKER_REGISTRY`: registry.cn-hangzhou.aliyuncs.com

#### 服务器相关
- `DEV_SERVER_IP`: 开发服务器IP地址
- `PROD_SERVER_IP`: 生产服务器IP地址
- `SSH_PRIVATE_KEY`: SSH私钥（用于连接服务器）

#### 钉钉通知相关
- `DINGTALK_WEBHOOK`: 钉钉机器人 Webhook 地址

#### 应用配置相关
- `DATABASE_URL`: 数据库连接字符串
- `DATABASE_URL_DEV`: 开发环境数据库连接字符串
- `DINGTALK_APP_KEY`: 钉钉应用 Key
- `DINGTALK_APP_SECRET`: 钉钉应用 Secret
- `DINGTALK_CORP_ID`: 钉钉企业 ID
- `DINGTALK_AGENT_ID`: 钉钉应用 Agent ID
- `JWT_SECRET`: JWT 密钥

### 2. 流水线触发条件

- **开发环境**: 推送到 `develop` 分支时自动部署
- **生产环境**: 推送到 `main` 分支时自动部署
- **功能分支**: 推送到 `feature/*` 分支时只进行构建和测试

### 3. 流水线阶段

1. **准备阶段**: 代码检查、依赖安装、TypeScript 编译
2. **测试阶段**: 运行单元测试和 API 测试
3. **构建阶段**: 构建 Docker 镜像并推送到镜像仓库
4. **部署阶段**: 使用 SSH 连接到服务器，通过 Docker Compose 部署应用

## 部署环境

### 开发环境
- **服务器**: 开发服务器
- **端口**: 3001
- **域名**: `dev-api.cantv-ding.com`
- **SSL**: 可选
- **数据库**: 开发数据库

### 生产环境
- **服务器**: 生产服务器
- **端口**: 3000
- **域名**: `api.cantv-ding.com`
- **SSL**: 启用 HTTPS
- **数据库**: 生产数据库
- **负载均衡**: Nginx 反向代理

## 手动部署

### 使用 Docker Compose 部署

```bash
# 部署到开发环境
docker-compose -f docker-compose.yml --env-file .env.dev up -d

# 部署到生产环境
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### 使用部署脚本

```bash
# 部署到开发环境
./scripts/deploy.sh dev latest

# 部署到生产环境
./scripts/deploy.sh prod v1.0.0
```

### 服务器准备

#### 1. 安装 Docker 和 Docker Compose
```bash
# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 创建部署目录
```bash
sudo mkdir -p /opt/cantv-ding
sudo chown $USER:$USER /opt/cantv-ding
cd /opt/cantv-ding
```

#### 3. 配置环境变量
```bash
# 复制环境变量文件
cp .env.example .env.prod
# 编辑配置文件，填入实际值
nano .env.prod
```

## 监控和日志

### 健康检查
- **端点**: `/api/health`
- **检查间隔**: 每10秒
- **超时时间**: 5秒

### 日志查看
```bash
# 查看应用日志
kubectl logs -f deployment/cantv-ding-backend -n cantv-ding-prod

# 查看开发环境日志
kubectl logs -f deployment/cantv-ding-backend-dev -n cantv-ding-dev
```

### 应用状态检查
```bash
# 查看 Pod 状态
kubectl get pods -n cantv-ding-prod -l app=cantv-ding-backend

# 查看服务状态
kubectl get svc -n cantv-ding-prod

# 查看 Ingress 状态
kubectl get ingress -n cantv-ding-prod
```

## 回滚操作

### 使用 kubectl 回滚
```bash
# 查看部署历史
kubectl rollout history deployment/cantv-ding-backend -n cantv-ding-prod

# 回滚到上一个版本
kubectl rollout undo deployment/cantv-ding-backend -n cantv-ding-prod

# 回滚到指定版本
kubectl rollout undo deployment/cantv-ding-backend -n cantv-ding-prod --to-revision=2
```

### 使用 Helm 回滚
```bash
# 查看发布历史
helm history cantv-ding-backend -n cantv-ding-prod

# 回滚到上一个版本
helm rollback cantv-ding-backend -n cantv-ding-prod

# 回滚到指定版本
helm rollback cantv-ding-backend 2 -n cantv-ding-prod
```

## 故障排查

### 常见问题

1. **镜像拉取失败**
   - 检查镜像拉取密钥是否正确配置
   - 验证镜像标签是否存在

2. **Pod 启动失败**
   - 检查环境变量配置
   - 查看 Pod 日志排查错误

3. **健康检查失败**
   - 确认应用是否正常启动
   - 检查健康检查端点是否可访问

4. **数据库连接失败**
   - 验证数据库连接字符串
   - 检查网络策略和防火墙设置

### 调试命令

```bash
# 进入 Pod 进行调试
kubectl exec -it deployment/cantv-ding-backend -n cantv-ding-prod -- /bin/sh

# 查看 Pod 详细信息
kubectl describe pod <pod-name> -n cantv-ding-prod

# 查看事件
kubectl get events -n cantv-ding-prod --sort-by='.lastTimestamp'
```

## 安全注意事项

1. **密钥管理**: 所有敏感信息都存储在 Kubernetes Secret 中
2. **网络策略**: 配置适当的网络策略限制 Pod 间通信
3. **RBAC**: 使用最小权限原则配置服务账户
4. **镜像安全**: 定期扫描镜像漏洞
5. **SSL/TLS**: 生产环境强制使用 HTTPS

## 性能优化

1. **资源配置**: 根据实际负载调整 CPU 和内存限制
2. **副本数**: 根据流量情况调整副本数量
3. **自动扩缩容**: 配置 HPA 实现自动扩缩容
4. **缓存策略**: 合理配置应用缓存

## 联系方式

如有问题，请联系：
- 邮箱: <EMAIL>
- 项目地址: https://github.com/your-org/cantv-ding-backend
