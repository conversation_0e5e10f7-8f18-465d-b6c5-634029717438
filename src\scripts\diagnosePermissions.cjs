const { PrismaClient } = require('@prisma/client');

async function diagnosePermissions() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 开始诊断权限系统...');
    console.log('='.repeat(50));

    // 1. 检查数据库连接
    console.log('1️⃣ 检查数据库连接...');
    try {
      await prisma.$connect();
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      return;
    }

    // 2. 检查表是否存在
    console.log('\n2️⃣ 检查数据库表...');
    const tables = ['Role', 'Permission', 'RolePermission', 'UserRole', 'DepartmentRole'];
    
    for (const table of tables) {
      try {
        const count = await prisma[table.toLowerCase()].count();
        console.log(`✅ ${table} 表存在，记录数: ${count}`);
      } catch (error) {
        console.error(`❌ ${table} 表检查失败:`, error.message);
      }
    }

    // 3. 检查角色数据
    console.log('\n3️⃣ 检查角色数据...');
    try {
      const roles = await prisma.role.findMany({
        orderBy: { createdAt: 'asc' },
      });
      
      if (roles.length === 0) {
        console.log('⚠️ 没有找到任何角色');
      } else {
        console.log(`✅ 找到 ${roles.length} 个角色:`);
        roles.forEach(role => {
          console.log(`  - ${role.displayName} (${role.name}) - ${role.isSystem ? '系统角色' : '自定义角色'} - ${role.isActive ? '激活' : '禁用'}`);
        });
      }
    } catch (error) {
      console.error('❌ 检查角色数据失败:', error.message);
    }

    // 4. 检查权限数据
    console.log('\n4️⃣ 检查权限数据...');
    try {
      const permissions = await prisma.permission.findMany({
        orderBy: { module: 'asc' },
      });
      
      if (permissions.length === 0) {
        console.log('⚠️ 没有找到任何权限');
      } else {
        console.log(`✅ 找到 ${permissions.length} 个权限:`);
        
        // 按模块分组显示
        const moduleGroups = {};
        permissions.forEach(permission => {
          if (!moduleGroups[permission.module]) {
            moduleGroups[permission.module] = [];
          }
          moduleGroups[permission.module].push(permission);
        });

        Object.keys(moduleGroups).forEach(module => {
          console.log(`  📦 ${module} 模块 (${moduleGroups[module].length} 个权限):`);
          moduleGroups[module].forEach(permission => {
            console.log(`    - ${permission.displayName} (${permission.name})`);
          });
        });
      }
    } catch (error) {
      console.error('❌ 检查权限数据失败:', error.message);
    }

    // 5. 检查角色权限关联
    console.log('\n5️⃣ 检查角色权限关联...');
    try {
      const rolePermissions = await prisma.rolePermission.findMany({
        include: {
          role: true,
          permission: true,
        },
      });
      
      if (rolePermissions.length === 0) {
        console.log('⚠️ 没有找到任何角色权限关联');
      } else {
        console.log(`✅ 找到 ${rolePermissions.length} 个角色权限关联:`);
        
        // 按角色分组显示
        const roleGroups = {};
        rolePermissions.forEach(rp => {
          if (!roleGroups[rp.role.name]) {
            roleGroups[rp.role.name] = {
              role: rp.role,
              permissions: []
            };
          }
          roleGroups[rp.role.name].permissions.push(rp.permission);
        });

        Object.keys(roleGroups).forEach(roleName => {
          const group = roleGroups[roleName];
          console.log(`  👤 ${group.role.displayName} (${group.permissions.length} 个权限):`);
          group.permissions.forEach(permission => {
            console.log(`    - ${permission.displayName} (${permission.name})`);
          });
        });
      }
    } catch (error) {
      console.error('❌ 检查角色权限关联失败:', error.message);
    }

    // 6. 检查用户角色分配
    console.log('\n6️⃣ 检查用户角色分配...');
    try {
      const userRoles = await prisma.userRole.findMany({
        include: {
          role: true,
        },
      });
      
      if (userRoles.length === 0) {
        console.log('⚠️ 没有找到任何用户角色分配');
      } else {
        console.log(`✅ 找到 ${userRoles.length} 个用户角色分配:`);
        userRoles.forEach(ur => {
          const expiry = ur.expiresAt ? ` (过期时间: ${ur.expiresAt.toISOString()})` : ' (永不过期)';
          console.log(`  - 用户 ${ur.userid} -> 角色 ${ur.role.displayName}${expiry}`);
        });
      }
    } catch (error) {
      console.error('❌ 检查用户角色分配失败:', error.message);
    }

    // 7. 检查部门角色分配
    console.log('\n7️⃣ 检查部门角色分配...');
    try {
      const deptRoles = await prisma.departmentRole.findMany({
        include: {
          role: true,
        },
      });
      
      if (deptRoles.length === 0) {
        console.log('⚠️ 没有找到任何部门角色分配');
      } else {
        console.log(`✅ 找到 ${deptRoles.length} 个部门角色分配:`);
        deptRoles.forEach(dr => {
          const expiry = dr.expiresAt ? ` (过期时间: ${dr.expiresAt.toISOString()})` : ' (永不过期)';
          console.log(`  - 部门 ${dr.deptId} -> 角色 ${dr.role.displayName}${expiry}`);
        });
      }
    } catch (error) {
      console.error('❌ 检查部门角色分配失败:', error.message);
    }

    // 8. 检查用户数据
    console.log('\n8️⃣ 检查用户数据...');
    try {
      const userCount = await prisma.user.count();
      console.log(`✅ 找到 ${userCount} 个用户`);
      
      if (userCount > 0) {
        const activeUsers = await prisma.user.count({ where: { isActive: true } });
        console.log(`  - 激活用户: ${activeUsers}`);
        console.log(`  - 禁用用户: ${userCount - activeUsers}`);
      }
    } catch (error) {
      console.error('❌ 检查用户数据失败:', error.message);
    }

    // 9. 检查部门数据
    console.log('\n9️⃣ 检查部门数据...');
    try {
      const deptCount = await prisma.department.count();
      console.log(`✅ 找到 ${deptCount} 个部门`);
    } catch (error) {
      console.error('❌ 检查部门数据失败:', error.message);
    }

    console.log('\n' + '='.repeat(50));
    console.log('🎉 权限系统诊断完成！');

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行诊断
diagnosePermissions()
  .then(() => {
    console.log('✅ 诊断完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 诊断失败:', error);
    process.exit(1);
  });
