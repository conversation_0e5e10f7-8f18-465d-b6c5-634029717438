# Docker 部署指南

## 🐳 Docker 环境变量问题解决方案

### 问题描述
在Docker容器中运行应用时，可能会遇到以下错误：
```
ZodError: [
  {
    "code": "invalid_type",
    "expected": "string",
    "received": "undefined",
    "path": ["DINGTALK_APP_KEY"],
    "message": "Required"
  }
]
```

### 解决方案

Docker会自动使用您现有的 `.env` 文件，无需额外配置！

1. **确保 `.env` 文件存在**：
   ```bash
   # 检查 .env 文件
   ls -la .env
   ```

2. **直接构建和运行**：
   ```bash
   # 构建镜像
   docker build -t cantv-ding-backend .

   # 运行容器
   docker run -d -p 3000:3000 cantv-ding-backend
   ```

就这么简单！Docker会自动复制您的 `.env` 文件到容器中。

## 🚀 快速部署

只需要两个命令：

```bash
# 1. 构建镜像
docker build -t cantv-ding-backend .

# 2. 运行容器
docker run -d -p 3000:3000 cantv-ding-backend

# 3. 验证部署
curl http://localhost:3000/api/health
```

Docker会自动使用您的 `.env` 文件，包含所有钉钉配置！

## 📝 环境变量说明

### 必需的环境变量
- `NODE_ENV`: 运行环境 (development/production)
- `PORT`: 服务端口 (默认: 3000)
- `HOST`: 监听地址 (默认: 0.0.0.0)

### 可选的环境变量
- `DINGTALK_APP_KEY`: 钉钉应用Key
- `DINGTALK_APP_SECRET`: 钉钉应用Secret
- `DINGTALK_CORP_ID`: 钉钉企业ID
- `DINGTALK_AGENT_ID`: 钉钉应用ID
- `JWT_SECRET`: JWT密钥
- `DATABASE_URL`: 数据库连接字符串

## 🔧 故障排除

### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :3000

# 使用不同端口
docker run -d -p 3001:3000 cantv-ding-backend
```

### 2. 钉钉功能不可用
如果没有配置钉钉环境变量，钉钉相关功能将不可用，但应用仍可正常启动。需要钉钉功能时，请配置相应的环境变量。

### 3. 查看容器日志
```bash
docker logs container_name
```

## 📋 生产环境建议

1. **使用具体的镜像标签**而不是latest
2. **配置健康检查**
3. **使用外部数据库**
4. **配置日志收集**
5. **设置资源限制**
6. **使用secrets管理敏感信息**

## 🔐 安全注意事项

1. 不要在镜像中硬编码敏感信息
2. 使用环境变量或secrets传递配置
3. 定期更新基础镜像
4. 使用非root用户运行应用
5. 配置适当的网络策略
