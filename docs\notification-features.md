# 钉钉消息通知功能

本文档介绍项目管理系统中的钉钉消息通知功能，包括功能特性、使用方法和配置说明。

## 功能概述

系统集成了钉钉消息通知功能，在关键业务节点自动发送通知消息，提高工作效率和信息传达的及时性。

### 主要功能

1. **项目创建通知** - 项目创建时自动通知执行PM
2. **周预算超额预警** - 周预算超过项目成本10%时通知项目创建人
3. **自定义通知** - 支持发送自定义消息给指定用户
4. **批量通知** - 支持同时向多个用户发送通知

## 通知场景

### 1. 项目创建通知

**触发条件**: 新项目创建成功后

**通知对象**: 项目的执行PM

**通知内容**:
- 项目名称
- 所属品牌
- 项目预算
- 项目周期
- 项目创建人

**消息模板**:
```
📋 **新项目分配通知**

**项目名称：** 春季营销推广项目
**所属品牌：** 某知名品牌
**项目预算：** ¥500,000
**项目周期：** 2024-03-01 至 2024-05-31
**项目创建人：** 李四

您已被指定为该项目的执行PM，请及时关注项目进展并做好相关准备工作。

如有疑问，请联系项目创建人。
```

### 2. 周预算超额预警

**触发条件**: 创建或更新周预算时，合同金额超过项目总成本的10%

**通知对象**: 项目创建人

**通知内容**:
- 项目名称
- 所属品牌
- 周预算标题
- 合同金额
- 项目总成本
- 超额比例

**消息模板**:
```
⚠️ **周预算超额预警**

**项目名称：** 春季营销推广项目
**所属品牌：** 某知名品牌
**周预算标题：** 第一周达人合作预算
**合同金额：** ¥80,000
**项目总成本：** ¥350,000
**超额比例：** 22.9%

该周预算金额已超过项目配置成本的10%，请注意控制预算支出，确保项目成本在合理范围内。

如需调整预算，请及时与相关人员沟通确认。
```

## 技术实现

### 核心服务类

#### DingTalkNotificationService

主要的通知服务类，提供以下方法：

```typescript
// 发送项目创建通知
sendProjectCreatedNotification(executorPMUserId: string, data: ProjectCreatedNotificationData): Promise<boolean>

// 发送周预算超额通知
sendWeeklyBudgetExceededNotification(creatorUserId: string, data: WeeklyBudgetExceededNotificationData): Promise<boolean>

// 发送自定义通知
sendCustomNotification(userIds: string[], title: string, content: string, messageType?: 'text' | 'markdown'): Promise<boolean>

// 批量发送通知
sendBatchNotification(userIds: string[], template: NotificationTemplate): Promise<boolean>
```

### 集成点

#### 项目服务集成

在 `ProjectService.createProject()` 方法中：
```typescript
// 发送项目创建通知给执行PM
await this.sendProjectCreatedNotification(project, createdBy);
```

#### 周预算服务集成

在 `ProjectService.createWeeklyBudget()` 和 `ProjectService.updateWeeklyBudget()` 方法中：
```typescript
// 异步检查周预算是否超过项目成本10%
this.checkWeeklyBudgetExceeded(weeklyBudget, projectId).catch(error => {
  console.error('检查周预算超额失败:', error);
});
```

## 配置说明

### 环境变量

确保以下钉钉相关环境变量已正确配置：

```env
# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_AGENT_ID=your_agent_id

# 钉钉回调配置
DINGTALK_CALLBACK_URL=your_callback_url
DINGTALK_AES_KEY=your_aes_key
DINGTALK_TOKEN=your_token
```

### 用户权限

- 确保发送通知的用户具有钉钉应用的使用权限
- 确保接收通知的用户在钉钉组织架构中存在
- 确保应用具有发送工作通知的权限

## 测试和验证

### 运行测试

```bash
# 运行通知功能测试
node scripts/test-notifications.js

# 运行通知功能演示
node scripts/demo-notifications.js
```

### 测试场景

1. **项目创建通知测试** - 验证项目创建时是否正确发送通知
2. **周预算超额通知测试** - 验证周预算超额时是否正确发送预警
3. **用户验证测试** - 验证用户存在性检查功能
4. **集成测试** - 验证完整的业务流程集成

## 监控和日志

### 日志记录

系统会记录以下通知相关日志：

```
✅ 项目创建通知发送成功: 春季营销推广项目 -> 张三
❌ 项目创建通知发送失败: 春季营销推广项目 -> 张三
⚠️ 无法获取执行PM用户信息: test-pm-001
✅ 周预算超额通知发送成功: 第一周达人合作预算 -> 李四
```

### 错误处理

- 网络错误：自动重试机制
- 用户不存在：记录警告日志，跳过发送
- 权限错误：记录错误日志，返回失败状态
- 配置错误：记录错误日志，系统启动时检查

## 扩展功能

### 添加新的通知场景

1. 在 `dingtalk.ts` 类型文件中添加新的通知数据接口
2. 在 `DingTalkNotificationService` 中添加新的通知方法
3. 在相应的业务服务中集成通知调用
4. 添加相应的测试用例

### 自定义消息模板

可以通过修改 `DingTalkNotificationService` 中的模板方法来自定义消息格式：

```typescript
private getProjectCreatedTemplate(data: ProjectCreatedNotificationData): NotificationTemplate {
  // 自定义消息模板
}
```

## 注意事项

1. **异步处理**: 通知发送采用异步方式，不会阻塞主业务流程
2. **错误容忍**: 通知发送失败不会影响业务操作的成功
3. **用户验证**: 发送前会验证用户是否存在，避免无效通知
4. **日志记录**: 所有通知操作都会记录详细日志，便于问题排查
5. **性能考虑**: 批量通知时建议控制用户数量，避免影响系统性能
