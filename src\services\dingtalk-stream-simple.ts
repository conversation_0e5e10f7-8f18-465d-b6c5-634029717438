/**
 * 钉钉Stream推送服务 - 基于官方SDK实现
 * 参考: https://github.com/open-dingtalk/dingtalk-stream-sdk-nodejs
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { ApprovalService } from './approval.js';

// 钉钉API常量
const GET_TOKEN_URL = 'https://oapi.dingtalk.com/gettoken';
const GATEWAY_URL = 'https://api.dingtalk.com/v1.0/gateway/connections/open';

// 消息接口定义
export interface DWClientDownStream {
  specVersion: string;
  type: string;
  headers: {
    appId: string;
    connectionId: string;
    contentType: string;
    messageId: string;
    time: string;
    topic: string;
    eventType?: string;
    eventBornTime?: string;
    eventId?: string;
    eventCorpId?: string;
    eventUnifiedAppId?: string;
  };
  data: string;
}

// 审批数据接口定义
export interface ApprovalEventData {
  processInstanceId: string;
  result: string;
  type: string;
  staffId: string;
  createTime: number;
  finishTime?: number;
  corpId: string;
  [key: string]: any;
}

export class DingTalkStreamSimple extends EventEmitter {
  private approvalService: ApprovalService;
  private ws: WebSocket | null = null;
  private isConnected = false;
  private clientId: string;
  private clientSecret: string;
  private accessToken: string = '';
  private dwUrl: string = '';
  private userDisconnect = false;
  private reconnectInterval = 5000;
  private autoReconnect = true;

  constructor() {
    super();
    this.approvalService = new ApprovalService();
    this.clientId = process.env.DINGTALK_APP_KEY || '';
    this.clientSecret = process.env.DINGTALK_APP_SECRET || '';

    if (!this.clientId || !this.clientSecret) {
      throw new Error('缺少钉钉应用配置信息: DINGTALK_APP_KEY 或 DINGTALK_APP_SECRET');
    }
  }

  /**
   * 启动Stream服务
   */
  async start(): Promise<void> {
    try {
      console.log('🚀 启动简化版钉钉Stream服务...');
      
      if (!this.clientId || !this.clientSecret) {
        throw new Error('缺少钉钉应用配置信息');
      }

      // 1. 获取访问令牌
      console.log('🔑 获取访问令牌...');
      await this.getAccessToken();
      console.log('✅ 访问令牌获取成功');

      // 2. 获取Stream连接信息
      console.log('📡 获取Stream连接信息...');
      await this.getEndpoint();
      console.log('✅ Stream连接信息获取成功');

      // 3. 建立WebSocket连接
      console.log('🔗 建立WebSocket连接...');
      await this.connectWebSocket();
      console.log('✅ WebSocket连接建立成功');

      console.log('✅ 钉钉Stream服务启动成功');
    } catch (error) {
      console.error('❌ 启动钉钉Stream服务失败:', error);
      throw error;
    }
  }

  /**
   * 停止Stream服务
   */
  async stop(): Promise<void> {
    console.log('🛑 停止钉钉Stream服务...');
    
    this.isConnected = false;
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    console.log('✅ 钉钉Stream服务已停止');
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<void> {
    try {
      const url = `${GET_TOKEN_URL}?appkey=${this.clientId}&appsecret=${this.clientSecret}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`获取访问令牌失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.errcode !== 0) {
        throw new Error(`获取访问令牌失败: ${data.errmsg}`);
      }

      this.accessToken = data.access_token;
      console.log('✅ 访问令牌获取成功');
    } catch (error) {
      console.error('❌ 获取访问令牌失败:', error);
      throw error;
    }
  }

  /**
   * 获取Stream端点
   */
  private async getEndpoint(): Promise<void> {
    try {
      const requestBody = {
        clientId: this.clientId,
        clientSecret: this.clientSecret,
        ua: 'DingTalkStream/1.0.0',
        subscriptions: [
          {
            type: 'CALLBACK',
            topic: '*'
          }
        ]
      };

      console.log('📤 请求Stream端点:', {
        clientId: this.clientId.substring(0, 8) + '...',
        clientSecret: '***',
        subscriptions: requestBody.subscriptions
      });

      const response = await fetch(GATEWAY_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'  // 官方SDK中的关键头部
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📥 响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ 响应内容:', errorText);
        throw new Error(`获取Stream端点失败: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('📥 响应数据:', data);

      if (!data.endpoint || !data.ticket) {
        throw new Error('响应中缺少endpoint或ticket字段');
      }

      // 构建WebSocket URL
      this.dwUrl = `${data.endpoint}?ticket=${data.ticket}`;
      console.log('✅ Stream端点构建成功');
    } catch (error) {
      console.error('❌ 获取Stream端点失败:', error);
      throw error;
    }
  }

  /**
   * 建立WebSocket连接
   */
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('🔗 连接到WebSocket端点:', this.dwUrl);

      this.ws = new WebSocket(this.dwUrl);

      // 连接超时处理
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket连接超时'));
      }, 10000);

      this.ws.on('open', () => {
        clearTimeout(timeout);
        this.isConnected = true;
        console.log('✅ WebSocket连接已建立');
        this.setupEventHandlers();
        resolve();
      });

      this.ws.on('error', (error) => {
        clearTimeout(timeout);
        console.error('❌ WebSocket连接错误:', error);
        reject(error);
      });

      this.ws.on('close', (code, reason) => {
        this.isConnected = false;
        console.log(`🔌 WebSocket连接已关闭: ${code} ${reason.toString()}`);
      });
    });
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        const message = data.toString();
        this.handleMessage(message);
      } catch (error) {
        console.error('❌ 处理消息失败:', error);
      }
    });

    this.ws.on('ping', () => {
      console.log('💓 收到ping');
      if (this.ws) {
        this.ws.pong();
      }
    });
  }

  /**
   * 处理收到的消息 - 基于官方SDK实现
   */
  private handleMessage(data: string): void {
    try {
      console.log('� 收到原始消息:', data.substring(0, 200));

      const message = JSON.parse(data) as DWClientDownStream;
      console.log('📨 解析后的消息:', {
        type: message.type,
        topic: message.headers?.topic,
        eventType: message.headers?.topic,
        messageId: message.headers?.messageId
      });

      // 根据消息类型处理
      switch (message.type) {
        case 'SYSTEM':
          this.onSystem(message);
          break;
        case 'EVENT':
          this.onEvent(message);
          break;
        case 'CALLBACK':
          this.onCallback(message);
          break;
        default:
          console.log(`❓ 未处理的消息类型: ${message.type}`);
      }
    } catch (error) {
      console.error('❌ 处理消息失败:', error);
    }
  }

  private sendResponse(headers: any, data: any): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ WebSocket未连接，无法发送响应');
      return;
    }

    try {
      const response = {
        code: 200,
        headers,
        data: data
      };

      this.ws.send(JSON.stringify(response));
      console.log('📤 发送响应:', response);
    } catch (error) {
      console.error('❌ 发送响应失败:', error);
    }
  }

  /**
   * 处理系统消息
   */
  private onSystem(message: DWClientDownStream): void {
    const topic = message.headers.topic;
    console.log('� 处理系统消息:', topic);

    switch (topic) {
      case 'CONNECTED':
        console.log('✅ 连接已建立');
        break;
      case 'REGISTERED':
        console.log('✅ 注册成功');
        this.isConnected = true;
        break;
      case 'ping':
        console.log('� 收到ping，发送pong');
        this.sendResponse(message.headers, message.data);
        break;
      case 'KEEPALIVE':
        console.log('💓 保活消息');
        break;
      default:
        console.log(`❓ 未处理的系统消息: ${topic}`);
    }
  }

  /**
   * 处理事件消息
   */
  private onEvent(message: DWClientDownStream): void {
    console.log('📋 处理事件消息:', message.headers.eventType);

    // 发送ACK确认
    this.sendResponse(message.headers.messageId, { status: 'SUCCESS' });

    // 触发事件
    this.emit('event', message);
  }

  /**
   * 处理回调消息
   */
  private async onCallback(message: DWClientDownStream): Promise<void> {
    console.log('📞 处理回调消息:', message.headers.topic);

    // 发送ACK确认
    this.sendResponse(message.headers.messageId, 'OK');

    // 根据事件类型处理
    if (message.headers.eventType === 'bpms_instance_change') {
      console.log('📋 收到审批状态变更事件');

      try {
        // 解析审批数据
        const approvalData = JSON.parse(message.data);
        console.log('📋 审批数据:', approvalData);

        // 调用审批服务处理状态变更
        const result = await this.handleApprovalStatusChange(approvalData);
        console.log('✅ 审批状态变更处理结果:', result);

        // 触发审批变更事件
        this.emit('approval_change', {
          originalData: approvalData,
          processResult: result
        });
      } catch (error) {
        console.error('❌ 处理审批状态变更失败:', error);
        this.emit('approval_error', {
          error: error,
          originalData: message.data
        });
      }
    }

    // 触发通用回调事件
    this.emit('callback', message);
    this.emit(message.headers.topic, message);
  }

  /**
   * 处理审批状态变更
   */
  private async handleApprovalStatusChange(approvalData: any): Promise<any> {
    try {
      console.log('🔄 开始处理审批状态变更...');

      // 构建审批状态变更参数
      const changeParams = {
        processInstanceId: approvalData.processInstanceId || '',
        result: approvalData.result || 'unknown',
        type: approvalData.type || 'unknown',
        staffId: approvalData.staffId || '',
        createTime: approvalData.createTime || Date.now(),
        finishTime: approvalData.finishTime || null,
        corpId: approvalData.corpId || ''
      };

      console.log('📋 审批变更参数:', changeParams);

      // 调用审批服务处理
      const result = await this.approvalService.handleApprovalStatusChange(changeParams);

      console.log('✅ 审批服务处理完成:', result);
      return result;
    } catch (error) {
      console.error('❌ 审批状态变更处理失败:', error);
      throw error;
    }
  }

  /**
   * 发送ACK确认
   */
  private sendAck(messageId: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ WebSocket未连接，无法发送ACK');
      return;
    }

    try {
      const ackMessage = {
        code: 200,
        headers: {
          messageId: messageId
        },
        message: 'OK'
      };

      this.ws.send(JSON.stringify(ackMessage));
      console.log('📤 发送ACK确认:', messageId);
    } catch (error) {
      console.error('❌ 发送ACK确认失败:', error);
    }
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    readyState?: number;
  } {
    return {
      isConnected: this.isConnected,
      readyState: this.ws?.readyState
    };
  }

  /**
   * 手动处理审批数据（供外部调用）
   */
  public async processApprovalData(approvalData: ApprovalEventData): Promise<any> {
    try {
      console.log('🔧 手动处理审批数据:', approvalData);
      return await this.handleApprovalStatusChange(approvalData);
    } catch (error) {
      console.error('❌ 手动处理审批数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取审批服务实例（供外部访问）
   */
  public getApprovalService(): ApprovalService {
    return this.approvalService;
  }
}
