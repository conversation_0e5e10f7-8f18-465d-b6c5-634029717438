<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉微H5应用示例</title>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
    <script src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 12px 12px 0 0;
            margin: -20px -20px 30px -20px;
        }

        .header h1 {
            color: white;
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            margin: 0;
        }

        .nav-menu {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.9);
            color: #1890ff;
            font-weight: bold;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .status.loading {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }

        .status.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .status.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .user-info {
            background-color: #fafafa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }

        .user-info h3 {
            margin-top: 0;
            color: #262626;
        }

        .user-info p {
            margin: 8px 0;
            color: #595959;
        }

        .btn {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }

        .btn:hover {
            background-color: #40a9ff;
        }

        .btn:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }

        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔧 钉钉微H5应用示例</h1>
            <p>演示钉钉API集成和用户认证功能</p>

            <!-- 导航菜单 -->
            <div class="nav-menu">
                <a href="index.html" class="nav-link active">🏠 首页</a>
                <a href="dingtalk-design-demo.html" class="nav-link">🎨 完整功能</a>
                <a href="dingtalk-login-demo.html" class="nav-link">🔐 免登录演示</a>
                <a href="test-api.html" class="nav-link">🧪 API测试</a>
                <a href="signature-test.html" class="nav-link">🔑 签名测试</a>
                <a href="official-login-demo.html" class="nav-link">📱 官方演示</a>
                <a href="project-management.html" class="nav-link">📊 项目管理</a>
                <a href="http://172.16.20.144:3000/" class="nav-link">📊 项目管理</a>
            </div>
        </div>

        <div id="status" class="status loading">
            正在初始化钉钉环境...
        </div>

        <div id="actions" style="display: none;">
            <button class="btn" onclick="getUserInfo()">获取用户信息</button>
            <button class="btn" onclick="getDepartments()">获取部门列表</button>
            <button class="btn" onclick="getJSAPISignature()">获取JSAPI签名</button>
        </div>

        <div id="userInfo" class="user-info" style="display: none;">
            <h3>👤 用户信息</h3>
            <div id="userDetails"></div>
        </div>

        <div id="departments" class="user-info" style="display: none;">
            <h3>🏢 部门列表</h3>
            <div id="departmentList"></div>
        </div>

        <div id="jsapiInfo" class="user-info" style="display: none;">
            <h3>🔐 JSAPI签名</h3>
            <div id="jsapiDetails"></div>
        </div>
    </div>

    <script>
        let isReady = false;
        let isDingTalkEnv = false;

        // 检查是否在钉钉环境中
        function checkDingTalkEnvironment() {
            // 检查是否有钉钉JSAPI
            if (typeof dd !== 'undefined') {
                isDingTalkEnv = true;
                initDingTalk();
            } else {
                // 等待JSAPI加载
                let retryCount = 0;
                const maxRetries = 10;
                const checkInterval = setInterval(() => {
                    retryCount++;
                    if (typeof dd !== 'undefined') {
                        clearInterval(checkInterval);
                        isDingTalkEnv = true;
                        initDingTalk();
                    } else if (retryCount >= maxRetries) {
                        clearInterval(checkInterval);
                        updateStatus('error', '❌ 未检测到钉钉环境，请在钉钉客户端中打开此页面');
                        showMockActions();
                    }
                }, 500);
            }
        }

        // 初始化钉钉环境
        function initDingTalk() {
            // 钉钉环境初始化
            dd.ready(() => {
                isReady = true;
                updateStatus('success', '✅ 钉钉环境初始化成功！可以开始使用功能。');
                document.getElementById('actions').style.display = 'block';
            });

            dd.error((err) => {
                updateStatus('error', `❌ 钉钉环境初始化失败: ${err.message || err}`);
            });
        }

        // 显示模拟操作按钮（用于非钉钉环境测试）
        function showMockActions() {
            document.getElementById('actions').style.display = 'block';
            // 添加提示信息
            const actionsDiv = document.getElementById('actions');
            const mockNotice = document.createElement('div');
            mockNotice.className = 'status error';
            mockNotice.style.marginBottom = '15px';
            mockNotice.textContent = '⚠️ 当前为模拟模式，某些功能可能无法正常工作';
            actionsDiv.insertBefore(mockNotice, actionsDiv.firstChild);
        }

        // 页面加载完成后检查环境
        window.addEventListener('load', () => {
            setTimeout(checkDingTalkEnvironment, 1000);
        });

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        // 获取用户信息
        function getUserInfo() {
            if (!isDingTalkEnv) {
                updateStatus('error', '❌ 此功能需要在钉钉环境中使用');
                // 显示模拟数据
                displayUserInfo({
                    userid: 'mock_user_001',
                    name: '模拟用户',
                    mobile: '138****8888',
                    email: '<EMAIL>',
                    position: '软件工程师',
                    job_number: 'EMP001',
                    department: [1, 2]
                });
                return;
            }

            if (!isReady) {
                updateStatus('error', '钉钉环境未就绪');
                return;
            }

            updateStatus('loading', '正在获取免登码...');

            dd.runtime.permission.requestAuthCode({
                redirection: "none",
                onSuccess: async (result) => {
                    updateStatus('loading', '正在获取用户信息...');

                    try {
                        const response = await fetch('/api/auth/user-info', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                authCode: result.code
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            updateStatus('success', '✅ 用户信息获取成功！');
                            displayUserInfo(data.data);
                        } else {
                            updateStatus('error', `❌ 获取用户信息失败: ${data.message}`);
                        }
                    } catch (error) {
                        updateStatus('error', `❌ 请求失败: ${error.message}`);
                    }
                },
                onFail: (err) => {
                    updateStatus('error', `❌ 获取免登码失败: ${err.message || err}`);
                }
            });
        }

        // 显示用户信息
        function displayUserInfo(userInfo) {
            const userInfoEl = document.getElementById('userInfo');
            const userDetailsEl = document.getElementById('userDetails');

            userDetailsEl.innerHTML = `
                <p><strong>用户ID:</strong> ${userInfo.userid || 'N/A'}</p>
                <p><strong>姓名:</strong> ${userInfo.name || 'N/A'}</p>
                <p><strong>手机号:</strong> ${userInfo.mobile || 'N/A'}</p>
                <p><strong>邮箱:</strong> ${userInfo.email || 'N/A'}</p>
                <p><strong>职位:</strong> ${userInfo.position || 'N/A'}</p>
                <p><strong>工号:</strong> ${userInfo.job_number || 'N/A'}</p>
                <p><strong>部门ID:</strong> ${userInfo.department ? userInfo.department.join(', ') : 'N/A'}</p>
                <div class="code">${JSON.stringify(userInfo, null, 2)}</div>
            `;

            userInfoEl.style.display = 'block';
        }

        // 获取部门列表
        async function getDepartments() {
            if (!isDingTalkEnv) {
                updateStatus('success', '✅ 部门列表获取成功！（模拟数据）');
                // 显示模拟数据
                displayDepartments([
                    { dept_id: 1, name: '总部', parent_id: 0, order: 1 },
                    { dept_id: 2, name: '技术部', parent_id: 1, order: 1 },
                    { dept_id: 3, name: '产品部', parent_id: 1, order: 2 },
                    { dept_id: 4, name: '市场部', parent_id: 1, order: 3 }
                ]);
                return;
            }

            updateStatus('loading', '正在获取部门列表...');

            try {
                const response = await fetch('/api/auth/departments');
                const data = await response.json();

                if (data.success) {
                    updateStatus('success', '✅ 部门列表获取成功！');
                    displayDepartments(data.data);
                } else {
                    updateStatus('error', `❌ 获取部门列表失败: ${data.message}`);
                }
            } catch (error) {
                updateStatus('error', `❌ 请求失败: ${error.message}`);
            }
        }

        // 显示部门列表
        function displayDepartments(departments) {
            const departmentsEl = document.getElementById('departments');
            const departmentListEl = document.getElementById('departmentList');

            if (departments && departments.length > 0) {
                const departmentHtml = departments.map(dept => `
                    <p><strong>${dept.name}</strong> (ID: ${dept.dept_id}, 父部门: ${dept.parent_id})</p>
                `).join('');

                departmentListEl.innerHTML = departmentHtml +
                    `<div class="code">${JSON.stringify(departments, null, 2)}</div>`;
            } else {
                departmentListEl.innerHTML = '<p>暂无部门数据</p>';
            }

            departmentsEl.style.display = 'block';
        }

        // 获取JSAPI签名
        async function getJSAPISignature() {
            if (!isDingTalkEnv) {
                updateStatus('success', '✅ JSAPI签名获取成功！（模拟数据）');
                // 显示模拟数据
                displayJSAPISignature({
                    corpId: 'mock_corp_id',
                    agentId: 'mock_agent_id',
                    timeStamp: Date.now(),
                    nonceStr: 'mock_nonce_str',
                    signature: 'mock_signature_hash'
                });
                return;
            }

            updateStatus('loading', '正在获取JSAPI签名...');

            try {
                const currentUrl = encodeURIComponent(window.location.href);
                const response = await fetch(`/api/auth/jsapi-signature?url=${currentUrl}`);
                const data = await response.json();

                if (data.success) {
                    updateStatus('success', '✅ JSAPI签名获取成功！');
                    displayJSAPISignature(data.data);
                } else {
                    updateStatus('error', `❌ 获取JSAPI签名失败: ${data.message}`);
                }
            } catch (error) {
                updateStatus('error', `❌ 请求失败: ${error.message}`);
            }
        }

        // 显示JSAPI签名
        function displayJSAPISignature(signature) {
            const jsapiInfoEl = document.getElementById('jsapiInfo');
            const jsapiDetailsEl = document.getElementById('jsapiDetails');

            jsapiDetailsEl.innerHTML = `
                <p><strong>企业ID:</strong> ${signature.corpId}</p>
                <p><strong>应用ID:</strong> ${signature.agentId}</p>
                <p><strong>时间戳:</strong> ${signature.timeStamp}</p>
                <p><strong>随机字符串:</strong> ${signature.nonceStr}</p>
                <p><strong>签名:</strong> ${signature.signature}</p>
                <div class="code">${JSON.stringify(signature, null, 2)}</div>
            `;

            jsapiInfoEl.style.display = 'block';
        }
    </script>
</body>

</html>