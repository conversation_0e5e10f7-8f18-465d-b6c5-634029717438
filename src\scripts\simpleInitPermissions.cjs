const { PrismaClient } = require('@prisma/client');

// 系统权限定义
const SYSTEM_PERMISSIONS = [
  // 用户管理权限
  { name: 'user.read', displayName: '查看用户', description: '查看用户信息', module: 'user', action: 'read', isSystem: true },
  { name: 'user.create', displayName: '创建用户', description: '创建新用户', module: 'user', action: 'create', isSystem: true },
  { name: 'user.update', displayName: '更新用户', description: '更新用户信息', module: 'user', action: 'update', isSystem: true },
  { name: 'user.delete', displayName: '删除用户', description: '删除用户', module: 'user', action: 'delete', isSystem: true },

  // 角色管理权限
  { name: 'role.read', displayName: '查看角色', description: '查看角色信息', module: 'role', action: 'read', isSystem: true },
  { name: 'role.create', displayName: '创建角色', description: '创建新角色', module: 'role', action: 'create', isSystem: true },
  { name: 'role.update', displayName: '更新角色', description: '更新角色信息', module: 'role', action: 'update', isSystem: true },
  { name: 'role.delete', displayName: '删除角色', description: '删除角色', module: 'role', action: 'delete', isSystem: true },
  { name: 'role.assign', displayName: '分配角色', description: '为用户或部门分配角色', module: 'role', action: 'assign', isSystem: true },

  // 权限管理权限
  { name: 'permission.read', displayName: '查看权限', description: '查看权限信息', module: 'permission', action: 'read', isSystem: true },
  { name: 'permission.create', displayName: '创建权限', description: '创建新权限', module: 'permission', action: 'create', isSystem: true },
  { name: 'permission.update', displayName: '更新权限', description: '更新权限信息', module: 'permission', action: 'update', isSystem: true },
  { name: 'permission.delete', displayName: '删除权限', description: '删除权限', module: 'permission', action: 'delete', isSystem: true },

  // 项目管理权限
  { name: 'project.read', displayName: '查看项目', description: '查看项目信息', module: 'project', action: 'read', isSystem: true },
  { name: 'project.create', displayName: '创建项目', description: '创建新项目', module: 'project', action: 'create', isSystem: true },
  { name: 'project.update', displayName: '更新项目', description: '更新项目信息', module: 'project', action: 'update', isSystem: true },
  { name: 'project.delete', displayName: '删除项目', description: '删除项目', module: 'project', action: 'delete', isSystem: true },
  { name: 'project.approve', displayName: '审批项目', description: '审批项目申请', module: 'project', action: 'approve', isSystem: true },

  // 品牌管理权限
  { name: 'brand.read', displayName: '查看品牌', description: '查看品牌信息', module: 'brand', action: 'read', isSystem: true },
  { name: 'brand.create', displayName: '创建品牌', description: '创建新品牌', module: 'brand', action: 'create', isSystem: true },
  { name: 'brand.update', displayName: '更新品牌', description: '更新品牌信息', module: 'brand', action: 'update', isSystem: true },
  { name: 'brand.delete', displayName: '删除品牌', description: '删除品牌', module: 'brand', action: 'delete', isSystem: true },

  // 财务管理权限
  { name: 'finance.read', displayName: '查看财务', description: '查看财务信息', module: 'finance', action: 'read', isSystem: true },
  { name: 'finance.create', displayName: '创建财务记录', description: '创建财务记录', module: 'finance', action: 'create', isSystem: true },
  { name: 'finance.update', displayName: '更新财务记录', description: '更新财务记录', module: 'finance', action: 'update', isSystem: true },
  { name: 'finance.delete', displayName: '删除财务记录', description: '删除财务记录', module: 'finance', action: 'delete', isSystem: true },
  { name: 'finance.approve', displayName: '财务审批', description: '审批财务申请', module: 'finance', action: 'approve', isSystem: true },

  // 系统管理权限
  { name: 'system.config', displayName: '系统配置', description: '管理系统配置', module: 'system', action: 'config', isSystem: true },
  { name: 'system.log', displayName: '系统日志', description: '查看系统日志', module: 'system', action: 'log', isSystem: true },
  { name: 'system.backup', displayName: '系统备份', description: '执行系统备份', module: 'system', action: 'backup', isSystem: true },
];

// 系统角色定义
const SYSTEM_ROLES = [
  {
    name: 'super_admin',
    displayName: '超级管理员',
    description: '拥有系统所有权限的超级管理员',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'admin',
    displayName: '管理员',
    description: '系统管理员，拥有大部分管理权限',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'project_manager',
    displayName: '项目经理',
    description: '项目管理人员，负责项目的创建和管理',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'user',
    displayName: '普通用户',
    description: '普通用户，拥有基本的查看权限',
    isSystem: true,
    createdBy: 'system',
  },
];

// 角色权限映射
const ROLE_PERMISSIONS = {
  super_admin: SYSTEM_PERMISSIONS.map(p => p.name), // 超级管理员拥有所有权限
  admin: [
    'user.read', 'user.create', 'user.update',
    'role.read', 'role.create', 'role.update', 'role.assign',
    'permission.read',
    'project.read', 'project.create', 'project.update', 'project.delete',
    'brand.read', 'brand.create', 'brand.update', 'brand.delete',
    'system.config', 'system.log',
  ],
  project_manager: [
    'project.read', 'project.create', 'project.update',
    'brand.read',
    'user.read',
  ],
  user: [
    'project.read',
    'brand.read',
    'user.read',
  ],
};

async function initializePermissions() {
  const prisma = new PrismaClient();

  try {
    console.log('🚀 开始初始化系统权限和角色...');

    // 1. 创建系统权限
    console.log('📋 创建系统权限...');
    const permissions = [];
    for (const permissionData of SYSTEM_PERMISSIONS) {
      try {
        const permission = await prisma.permission.create({
          data: permissionData,
        });
        permissions.push(permission);
        console.log(`✅ 创建权限: ${permission.displayName}`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`⚠️  权限已存在: ${permissionData.displayName}`);
          const existing = await prisma.permission.findUnique({
            where: { name: permissionData.name },
          });
          if (existing) {
            permissions.push(existing);
          }
        } else {
          throw error;
        }
      }
    }

    // 2. 创建系统角色
    console.log('👥 创建系统角色...');
    const roles = [];
    for (const roleData of SYSTEM_ROLES) {
      try {
        const role = await prisma.role.create({
          data: roleData,
        });
        roles.push(role);
        console.log(`✅ 创建角色: ${role.displayName}`);
      } catch (error) {
        if (error.code === 'P2002') {
          console.log(`⚠️  角色已存在: ${roleData.displayName}`);
          const existing = await prisma.role.findUnique({
            where: { name: roleData.name },
          });
          if (existing) {
            roles.push(existing);
          }
        } else {
          throw error;
        }
      }
    }

    // 3. 为角色分配权限
    console.log('🔗 为角色分配权限...');
    for (const role of roles) {
      const permissionNames = ROLE_PERMISSIONS[role.name];
      if (permissionNames && permissionNames.length > 0) {
        // 删除现有权限
        await prisma.rolePermission.deleteMany({
          where: { roleId: role.id },
        });

        // 获取权限ID
        const rolePermissions = await prisma.permission.findMany({
          where: { name: { in: permissionNames } },
          select: { id: true },
        });

        const permissionIds = rolePermissions.map(p => p.id);

        // 添加新权限
        if (permissionIds.length > 0) {
          await prisma.rolePermission.createMany({
            data: permissionIds.map(permissionId => ({
              roleId: role.id,
              permissionId,
              createdBy: 'system',
            })),
          });
        }

        console.log(`✅ 为角色 ${role.displayName} 分配了 ${permissionIds.length} 个权限`);
      }
    }

    console.log('🎉 系统权限和角色初始化完成！');
  } catch (error) {
    console.error('❌ 初始化系统权限和角色失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行初始化
initializePermissions()
  .then(() => {
    console.log('✅ 初始化完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 初始化失败:', error);
    process.exit(1);
  });
