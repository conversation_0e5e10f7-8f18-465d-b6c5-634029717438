<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSAPI签名测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 12px 12px 0 0;
            margin: -20px -20px 20px -20px;
        }

        .header h1 {
            color: white;
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            margin: 0;
        }

        .nav-menu {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.9);
            color: #1890ff;
            font-weight: bold;
        }

        h1 {
            color: #333;
            text-align: center;
        }

        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .test-section h3 {
            color: #1890ff;
            margin-top: 0;
        }

        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #40a9ff;
        }

        .result {
            background: #f6f6f6;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            border-color: #52c41a;
            background: #f6ffed;
        }

        .error {
            border-color: #ff4d4f;
            background: #fff2f0;
        }

        .url-info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }

        .url-info h4 {
            color: #1890ff;
            margin-top: 0;
        }

        .url-display {
            word-break: break-all;
            background: #f0f0f0;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .comparison-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
        }

        .comparison-item h4 {
            margin-top: 0;
            color: #1890ff;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔐 JSAPI签名测试工具</h1>
            <p>测试钉钉JSAPI签名生成和URL处理</p>

            <!-- 导航菜单 -->
            <div class="nav-menu">
                <a href="index.html" class="nav-link">🏠 首页</a>
                <a href="dingtalk-design-demo.html" class="nav-link">🎨 完整功能</a>
                <a href="dingtalk-login-demo.html" class="nav-link">🔐 免登录演示</a>
                <a href="test-api.html" class="nav-link">🧪 API测试</a>
                <a href="signature-test.html" class="nav-link active">🔑 签名测试</a>
                <a href="official-login-demo.html" class="nav-link">📱 官方演示</a>
            </div>
        </div>

        <div class="url-info">
            <h4>📍 当前页面URL信息</h4>
            <div>
                <strong>原始URL:</strong>
                <div class="url-display" id="originalUrl"></div>
            </div>
            <div>
                <strong>清理后URL:</strong>
                <div class="url-display" id="cleanedUrl"></div>
            </div>
            <div>
                <strong>编码后URL:</strong>
                <div class="url-display" id="encodedUrl"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>1. URL清理测试</h3>
            <p>测试移除钉钉调试参数的功能</p>
            <button onclick="testUrlCleaning()">测试URL清理</button>
            <div id="urlResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. JSAPI签名测试</h3>
            <p>使用清理后的URL获取JSAPI签名</p>
            <button onclick="testJSAPISignature()">测试JSAPI签名</button>
            <div id="signatureResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. 增强版签名测试</h3>
            <p>使用真实ticket的增强版JSAPI签名</p>
            <button onclick="testEnhancedSignature()">测试增强版签名</button>
            <div id="enhancedResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 签名对比分析</h2>
        <div class="comparison" id="comparison" style="display: none;">
            <div class="comparison-item">
                <h4>标准签名</h4>
                <div id="standardSignature"></div>
            </div>
            <div class="comparison-item">
                <h4>增强版签名</h4>
                <div id="enhancedSignature"></div>
            </div>
        </div>
    </div>

    <script>
        // 清理URL，移除钉钉调试参数
        function cleanUrl(url) {
            try {
                const urlObj = new URL(url);

                // 移除钉钉调试相关的参数
                const debugParams = [
                    'dd_debug_h5',
                    'dd_debug_v1',
                    'dd_debug_os',
                    'dd_debug_v2',
                    'dd_debug_unifiedAppId',
                    'dd_debug_token',
                    'dd_debug_uuid',
                    'dd_debug_pid'
                ];

                debugParams.forEach(param => {
                    urlObj.searchParams.delete(param);
                });

                return urlObj.toString();
            } catch (error) {
                console.warn('URL清理失败:', error);
                return url;
            }
        }

        // 通用API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 显示结果
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';

            if (result.success) {
                element.className = 'result success';
                element.textContent = JSON.stringify(result.data, null, 2);
            } else {
                element.className = 'result error';
                element.textContent = `错误: ${result.error || result.data?.message || '未知错误'}`;
            }
        }

        // 测试URL清理
        function testUrlCleaning() {
            const original = window.location.href;
            const cleaned = cleanUrl(original);
            const encoded = encodeURIComponent(cleaned);

            const result = {
                success: true,
                data: {
                    original: original,
                    cleaned: cleaned,
                    encoded: encoded,
                    removedParams: original !== cleaned,
                    urlLength: {
                        original: original.length,
                        cleaned: cleaned.length,
                        encoded: encoded.length
                    }
                }
            };

            showResult('urlResult', result);
        }

        // 测试JSAPI签名
        async function testJSAPISignature() {
            const cleanedUrl = cleanUrl(window.location.href);
            const encodedUrl = encodeURIComponent(cleanedUrl);
            const result = await apiCall(`/api/auth/jsapi-signature?url=${encodedUrl}`);
            showResult('signatureResult', result);

            if (result.success) {
                document.getElementById('standardSignature').innerHTML = `
                    <p><strong>签名:</strong> ${result.data.data.signature}</p>
                    <p><strong>时间戳:</strong> ${result.data.data.timeStamp}</p>
                    <p><strong>随机字符串:</strong> ${result.data.data.nonceStr}</p>
                `;
                document.getElementById('comparison').style.display = 'grid';
            }
        }

        // 测试增强版签名
        async function testEnhancedSignature() {
            const cleanedUrl = cleanUrl(window.location.href);
            const encodedUrl = encodeURIComponent(cleanedUrl);
            const result = await apiCall(`/api/app/jsapi-signature/enhanced?url=${encodedUrl}`);
            showResult('enhancedResult', result);

            if (result.success) {
                document.getElementById('enhancedSignature').innerHTML = `
                    <p><strong>签名:</strong> ${result.data.data.signature}</p>
                    <p><strong>时间戳:</strong> ${result.data.data.timeStamp}</p>
                    <p><strong>随机字符串:</strong> ${result.data.data.nonceStr}</p>
                `;
                document.getElementById('comparison').style.display = 'grid';
            }
        }

        // 页面加载完成后显示URL信息
        window.addEventListener('load', () => {
            const original = window.location.href;
            const cleaned = cleanUrl(original);
            const encoded = encodeURIComponent(cleaned);

            document.getElementById('originalUrl').textContent = original;
            document.getElementById('cleanedUrl').textContent = cleaned;
            document.getElementById('encodedUrl').textContent = encoded;

            console.log('URL信息:', {
                original,
                cleaned,
                encoded,
                hasDebugParams: original !== cleaned
            });
        });
    </script>
</body>

</html>