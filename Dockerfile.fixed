# 修复版 Dockerfile - 解决 Prisma 客户端问题
# 多阶段构建

# 第一阶段：构建阶段
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

WORKDIR /app

# 1) 配置 npm 和 pnpm
RUN npm config set registry=https://registry.npmmirror.com \
  && npm install -g pnpm --registry=https://registry.npmmirror.com

# 2) 复制依赖文件并安装所有依赖（包括 devDependencies）
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

# 3) 复制 Prisma schema 并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 4) 复制源代码并构建
COPY src ./src/
COPY tsconfig.json ./
COPY .env.prod ./
RUN pnpm run build

# 第二阶段：运行阶段
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

# 1) 安装系统依赖
RUN apk update \
  && apk add --no-cache tzdata curl

WORKDIR /app

# 2) 配置 npm 和 pnpm，安装生产依赖
COPY package.json pnpm-lock.yaml ./
RUN npm config set registry=https://registry.npmmirror.com \
  && npm install -g pnpm --registry=https://registry.npmmirror.com \
  && pnpm install --prod

# 3) 复制 Prisma schema 并重新生成客户端（运行时需要）
COPY prisma ./prisma/
RUN pnpm db:generate

# 4) 复制构建产物
COPY --from=builder /app/dist ./dist

# 5) 复制启动脚本和环境变量
COPY start.sh ./
COPY .env.prod ./

# 6) 创建非 root 用户并修正权限
RUN addgroup -S nodejs \
  && adduser -S -G nodejs nodejs \
  && chown -R nodejs:nodejs /app \
  && chmod +x start.sh

# 7) 切换到非 root 用户
USER nodejs

# 8) 暴露端口
EXPOSE 3000

# 9) 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 10) 启动应用
CMD ["./start.sh"]
