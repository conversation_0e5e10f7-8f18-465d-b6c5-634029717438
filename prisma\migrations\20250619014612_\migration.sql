/*
  Warnings:

  - The values [DRAFT] on the enum `weekly_budget_status` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "weekly_budget_status_new" AS ENUM ('CREATED', 'APPROVED', 'EXECUTING', 'COMPLETED', 'CANCELLED');
ALTER TABLE "weekly_budgets" ALTER COLUMN "status" DROP DEFAULT;
ALTER TABLE "weekly_budgets" ALTER COLUMN "status" TYPE "weekly_budget_status_new" USING ("status"::text::"weekly_budget_status_new");
ALTER TYPE "weekly_budget_status" RENAME TO "weekly_budget_status_old";
ALTER TYPE "weekly_budget_status_new" RENAME TO "weekly_budget_status";
DROP TYPE "weekly_budget_status_old";
ALTER TABLE "weekly_budgets" ALTER COLUMN "status" SET DEFAULT 'CREATED';
COMMIT;

-- AlterTable
ALTER TABLE "weekly_budgets" ALTER COLUMN "status" SET DEFAULT 'CREATED';
