# 达人返点业务逻辑说明

## 概述

达人返点（`estimatedInfluencerRebate`）是项目财务计算中的重要组成部分。虽然在数据结构中位于 `cost` 对象内，但从业务逻辑上来说，**达人返点实际上是收入性质**，应该增加项目的总收益。

## 业务背景

### 什么是达人返点？

达人返点是指与达人（KOL/网红）合作时，达人根据合作协议向品牌方返还的费用。这通常基于：

1. **销售业绩**：根据实际销售额的一定比例返点
2. **合作协议**：预先约定的返点金额或比例
3. **长期合作**：基于长期合作关系的优惠返点

### 为什么是收入性质？

- ✅ **减少实际成本**：返点直接减少了与达人合作的实际成本
- ✅ **增加项目收益**：返点金额可以视为项目的额外收入
- ✅ **提高利润率**：返点直接提升项目的毛利率

## 当前数据结构

### 数据库Schema
```sql
-- 项目表中的相关字段
estimatedInfluencerRebate DECIMAL(10,2) NOT NULL DEFAULT 0.00 -- 预估达人返点
```

### TypeScript接口
```typescript
interface ProjectCost {
  influencerCost: number;           // 达人成本
  adCost: number;                  // 投流成本
  otherCost: number;               // 其他成本
  estimatedInfluencerRebate: number; // 预估达人返点（注意：这是收入性质）
}
```

## 利润计算公式

### 正确的计算逻辑
```typescript
// 当前实现（正确）
const totalCost = cost.influencerCost + cost.adCost + cost.otherCost;
const profit = budget.planningBudget - totalCost + cost.estimatedInfluencerRebate;
```

### 公式解释
```
项目利润 = 项目规划预算 - (达人成本 + 投流成本 + 其他成本) + 达人返点

其中：
- 项目规划预算：客户支付的总金额
- 达人成本：支付给达人的费用
- 投流成本：广告投放费用
- 其他成本：其他项目相关费用
- 达人返点：达人返还的费用（收入性质，增加利润）
```

### 示例计算
```
假设项目：
- 规划预算：100,000元
- 达人成本：40,000元
- 投流成本：30,000元
- 其他成本：10,000元
- 达人返点：5,000元

计算过程：
总成本 = 40,000 + 30,000 + 10,000 = 80,000元
项目利润 = 100,000 - 80,000 + 5,000 = 25,000元
毛利率 = (25,000 / 100,000) × 100% = 25%
```

## 财务报表中的处理

### 成本分析
在财务报表中，达人返点的处理：

1. **成本计算**：不包含达人返点
   ```typescript
   const purchaseCost = project.cost.influencerCost + project.cost.adCost + project.cost.otherCost;
   ```

2. **返点单独显示**：
   ```typescript
   const rebate = project.cost.estimatedInfluencerRebate;
   ```

3. **净成本计算**：
   ```typescript
   const netCost = purchaseCost - rebate;
   ```

### 报表展示
```
项目财务明细：
├── 收入
│   ├── 项目规划预算：100,000元
│   └── 达人返点：5,000元
├── 成本
│   ├── 达人成本：40,000元
│   ├── 投流成本：30,000元
│   └── 其他成本：10,000元
└── 利润：25,000元（毛利率25%）
```

## 业务场景

### 场景1：预估返点
在项目创建时，基于历史数据和合作协议预估返点金额：
```typescript
const estimatedRebate = influencerCost * 0.125; // 假设返点率12.5%
```

### 场景2：实际返点
项目执行过程中，根据实际业绩调整返点：
```typescript
// 更新实际返点金额
await updateProject({
  id: projectId,
  cost: {
    ...existingCost,
    estimatedInfluencerRebate: actualRebateAmount
  }
});
```

### 场景3：返点结算
项目完成后，确认最终返点金额并进行财务结算。

## 注意事项

### 1. 数据一致性
- 确保返点金额不超过达人成本
- 返点金额应为正数（负数表示额外支付）

### 2. 财务合规
- 返点需要有明确的合同依据
- 需要完整的财务凭证和记录

### 3. 税务处理
- 返点可能涉及税务处理
- 需要根据税法规定进行相应处理

## 未来优化建议

### 1. 数据结构优化
考虑将达人返点从 `cost` 对象中分离出来：
```typescript
interface Project {
  cost: ProjectCost;     // 纯成本信息
  income: ProjectIncome; // 收入信息（包括返点）
  profit: ProjectProfit; // 利润信息
}
```

### 2. 返点类型细分
```typescript
interface InfluencerRebate {
  type: 'performance' | 'agreement' | 'loyalty'; // 返点类型
  amount: number;                                // 返点金额
  rate?: number;                                 // 返点比例
  basis?: 'sales' | 'cost' | 'fixed';          // 计算基础
}
```

### 3. 实时计算
实现基于实际业绩的动态返点计算。

## 总结

达人返点虽然在数据结构中位于成本对象内，但其业务性质是**收入**，在利润计算中应该**增加**项目收益。当前的计算逻辑是正确的，但在语义上可以通过注释和文档来明确这一点，避免理解上的混淆。

这种设计既保持了数据结构的简洁性，又正确反映了业务逻辑，是一个实用的解决方案。
