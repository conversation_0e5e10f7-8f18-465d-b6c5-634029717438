# Docker 构建问题修复脚本
param(
    [switch]$UseSimple,
    [switch]$UseFixed,
    [switch]$CleanAll,
    [string]$Tag = "latest"
)

Write-Host "🔧 Docker 构建问题修复脚本" -ForegroundColor Blue

# 检查 Docker 是否运行
try {
    docker version | Out-Null
    Write-Host "✅ Docker 正在运行" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker 未运行，请启动 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 清理选项
if ($CleanAll) {
    Write-Host "🧹 清理所有 Docker 缓存和镜像..." -ForegroundColor Yellow
    docker system prune -a -f
    docker builder prune -a -f
}

# 选择 Dockerfile
$DockerfilePath = if ($UseFixed) {
    "Dockerfile.fixed"
} elseif ($UseSimple) {
    "Dockerfile.simple"
} else {
    "Dockerfile"
}
Write-Host "📄 使用 Dockerfile: $DockerfilePath" -ForegroundColor Cyan

# 检查必要文件
$RequiredFiles = @("package.json", "tsconfig.json", "start.sh", ".env.prod")
foreach ($file in $RequiredFiles) {
    if (!(Test-Path $file)) {
        Write-Host "❌ 缺少必要文件: $file" -ForegroundColor Red
        exit 1
    }
}
Write-Host "✅ 所有必要文件存在" -ForegroundColor Green

# 检查 pnpm-lock.yaml
if (!(Test-Path "pnpm-lock.yaml")) {
    Write-Host "⚠️  pnpm-lock.yaml 不存在，将在构建时生成" -ForegroundColor Yellow
}

# 构建镜像
Write-Host "🔨 开始构建 Docker 镜像..." -ForegroundColor Blue
$BuildCommand = "docker build -f $DockerfilePath -t cantv-ding-backend:$Tag ."

Write-Host "执行命令: $BuildCommand" -ForegroundColor Gray

try {
    # 启用 BuildKit
    $env:DOCKER_BUILDKIT = "1"
    
    # 执行构建
    Invoke-Expression $BuildCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "🎉 构建成功！" -ForegroundColor Green
        
        # 显示镜像信息
        Write-Host "📊 镜像信息:" -ForegroundColor Blue
        docker images "cantv-ding-backend:$Tag" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        
        # 提供运行命令
        Write-Host "🚀 运行命令:" -ForegroundColor Blue
        Write-Host "docker run -d -p 3000:3000 --name cantv-ding cantv-ding-backend:$Tag" -ForegroundColor Yellow
        
    } else {
        Write-Host "❌ 构建失败！" -ForegroundColor Red
        Write-Host "💡 尝试以下解决方案:" -ForegroundColor Yellow
        Write-Host "1. 使用修复版 Dockerfile: .\fix-docker-build.ps1 -UseFixed" -ForegroundColor White
        Write-Host "2. 使用简化版 Dockerfile: .\fix-docker-build.ps1 -UseSimple" -ForegroundColor White
        Write-Host "3. 清理所有缓存重新构建: .\fix-docker-build.ps1 -CleanAll" -ForegroundColor White
        Write-Host "4. 检查网络连接和 Docker 资源" -ForegroundColor White
        exit 1
    }
    
} catch {
    Write-Host "❌ 构建过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 脚本执行完成" -ForegroundColor Green
