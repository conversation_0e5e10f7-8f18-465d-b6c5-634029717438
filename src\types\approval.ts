// 审批相关类型定义

export interface ApprovalInstance {
  id: string;
  processInstanceId: string;
  processCode: string;
  businessId?: string;
  title: string;
  originatorUserId: string;
  status: ApprovalStatus;
  result?: string;
  createTime: Date;
  finishTime?: Date;
  approvalAmount: number;
  actualAmount?: number;
  reason?: string;
  remark?: string;
  weeklyBudgetId: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ApprovalStatus {
  NONE = 'NONE',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED'
}

// 付款方式枚举
export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',      // 银行转账
  ONLINE_PAYMENT = 'online_payment',    // 网银支付
  CHECK = 'check',                      // 支票
  CASH = 'cash',                        // 现金
  OTHER = 'other'                       // 其他
}

// 合同签署主体枚举
export enum ContractEntity {
  COMPANY_A = 'company_a',              // 公司A
  COMPANY_B = 'company_b',              // 公司B
  SUBSIDIARY = 'subsidiary',            // 子公司
  OTHER = 'other'                       // 其他
}

// 发起审批请求
export interface CreateApprovalRequest {
  weeklyBudgetId: string;
  totalAmount: number;                  // 付款总额
  paymentReason: string;                // 付款事由
  contractEntity: ContractEntity;       // 合同签署主体
  expectedPaymentDate: string;          // 期望付款时间
  department: number;                   // 申请部门
  paymentMethod: PaymentMethod;         // 付款方式
  receivingAccount: {                   // 收款账号信息
    accountName: string;                // 账户名称
    accountNumber: string;              // 账号
    bankName: string;                   // 开户银行
    bankCode?: string;                  // 银行代码
  };
  relatedApprovalId?: string;           // 关联审批单
  invoiceFiles?: string[];              // 发票文件URL列表
  attachments?: string[];               // 附件URL列表
  remark?: string;                      // 备注
  attachmentFiles?: any[];              // 附件文件列表（用于上传到钉钉）
  attachmentMediaIds?: string[];        // 钉钉媒体ID列表（已上传到钉钉的文件）

  // 兼容旧版本字段
  approvalAmount?: number;              // 审批金额（兼容）
  reason?: string;                      // 原因（兼容）
}

// 钉钉审批表单字段
export interface ApprovalFormField {
  name: string;
  value: object | string | number;
  componentType?: string;
}

// 钉钉发起审批实例请求
export interface DingTalkCreateApprovalRequest {
  process_code: string;
  originator_user_id: string;
  dept_id: number;
  approvers?: string[];
  cc_list?: string[];
  cc_position?: string;
  form_component_values: ApprovalFormField[];
  target_select_actioners?: string[];
  business_id?: string;
}

// 钉钉审批实例响应
export interface DingTalkApprovalResponse {
  errcode: number;
  errmsg: string;
  process_instance_id?: string;
}

// 钉钉审批实例详情
export interface DingTalkApprovalDetail {
  errcode: number;
  errmsg: string;
  process_instance?: {
    process_instance_id: string;
    title: string;
    create_time: string;
    finish_time?: string;
    originator_userid: string;
    status: string;
    result: string;
    business_id?: string;
    form_component_values: ApprovalFormField[];
    operation_records?: ApprovalOperationRecord[];
    tasks?: ApprovalTask[];
  };
}

// 审批操作记录
export interface ApprovalOperationRecord {
  userid: string;
  date: string;
  operation_type: string;
  operation_result: string;
  remark?: string;
}

// 审批任务
export interface ApprovalTask {
  userid: string;
  task_status: string;
  task_result?: string;
  create_time: string;
  finish_time?: string;
  task_id: string;
}

// 审批回调事件
export interface ApprovalCallbackEvent {
  EventType: string;
  processInstanceId: string;
  processCode: string;
  corpId: string;
  createTime: number;
  title: string;
  type: string;
  url?: string;
  result?: string;
  staffId: string;
}

// 审批统计信息
export interface ApprovalStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  cancelled: number;
  totalAmount: number;
  approvedAmount: number;
  pendingAmount: number;
}

// 审批列表查询参数
export interface ApprovalQueryParams {
  page?: number;
  pageSize?: number;
  weeklyBudgetId?: string;
  status?: ApprovalStatus;
  originatorUserId?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 审批列表响应
export interface ApprovalListResponse {
  approvals: ApprovalInstance[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 更新审批状态请求
export interface UpdateApprovalStatusRequest {
  processInstanceId: string;
  status: ApprovalStatus;
  result?: string;
  finishTime?: Date;
  actualAmount?: number;
  remark?: string;
}

// 审批流程配置
export interface ApprovalProcessConfig {
  processCode: string;
  processName: string;
  description?: string;
  formFields: ApprovalFormFieldConfig[];
  approvers?: string[];
  ccList?: string[];
  isEnabled: boolean;
}

// 审批表单字段配置
export interface ApprovalFormFieldConfig {
  name: string;
  label: string;
  type: 'TextField' | 'NumberField' | 'MoneyField' | 'DateField' | 'TextareaField' | 'SelectField';
  required: boolean;
  placeholder?: string;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

// 对公付款审批表单数据
export interface PaymentApprovalFormData {
  title: string;                    // 审批标题
  applicant: string;                // 申请人
  applicantName: string;            // 申请人姓名
  department: number;               // 申请部门
  relatedApprovalId?: string;       // 关联审批单
  projectId: string;                // 所属项目ID
  projectName: string;              // 所属项目名称
  paymentReason: string;            // 付款事由
  contractEntity: string;           // 合同签署主体
  totalAmount: number;              // 付款总额
  expectedPaymentDate: string;      // 期望付款时间
  paymentMethod: string;            // 付款方式
  receivingAccount: {               // 收款账号信息
    accountName: string;            // 账户名称
    accountNumber: string;          // 账号
    bankName: string;               // 开户银行
    bankCode?: string;              // 银行代码
  };
  invoiceFiles?: string[];          // 发票文件URL列表
  attachments?: string[];           // 附件URL列表
  attachmentFiles?: any[];          // 附件文件列表（用于上传到钉钉）
  attachmentMediaIds?: string[];    // 钉钉媒体ID列表（已上传到钉钉的文件）
  remark?: string;                  // 备注

  // 原有字段保留用于兼容
  supplierName?: string;            // 供应商名称
  serviceType?: string;             // 服务类型
  serviceContent?: string;          // 服务内容
  contractAmount?: number;          // 合同金额
  weekStartDate?: string;           // 周开始日期
  weekEndDate?: string;             // 周结束日期
  taxRate?: string;                 // 税率
}
