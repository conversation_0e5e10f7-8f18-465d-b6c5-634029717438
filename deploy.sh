#!/bin/bash

# 钉钉微H5应用部署脚本

set -e

echo "🚀 开始部署钉钉微H5应用..."

# 检查环境变量
if [ ! -f .env ]; then
    echo "❌ 错误: .env 文件不存在，请先配置环境变量"
    exit 1
fi

# 检查必要的环境变量
source .env
if [ -z "$DINGTALK_APP_KEY" ] || [ -z "$DINGTALK_APP_SECRET" ] || [ -z "$DINGTALK_CORP_ID" ]; then
    echo "❌ 错误: 请在 .env 文件中配置钉钉应用信息"
    exit 1
fi

echo "📦 安装依赖..."
npm ci

echo "🔨 构建应用..."
npm run build

echo "🧪 运行测试..."
# npm test

echo "📁 创建日志目录..."
mkdir -p logs

echo "🐳 使用Docker部署..."
if command -v docker-compose &> /dev/null; then
    echo "使用 docker-compose 部署..."
    docker-compose down
    docker-compose up -d --build
elif command -v docker &> /dev/null; then
    echo "使用 docker 部署..."
    docker build -t cantv-ding-api .
    docker stop cantv-ding-api || true
    docker rm cantv-ding-api || true
    docker run -d \
        --name cantv-ding-api \
        -p 3000:3000 \
        --env-file .env \
        -v $(pwd)/logs:/app/logs \
        --restart unless-stopped \
        cantv-ding-api
else
    echo "⚠️  Docker 未安装，使用 PM2 部署..."
    if command -v pm2 &> /dev/null; then
        pm2 stop ecosystem.config.js || true
        pm2 start ecosystem.config.js
        pm2 save
    else
        echo "❌ 错误: 请安装 Docker 或 PM2"
        exit 1
    fi
fi

echo "⏳ 等待服务启动..."
sleep 10

echo "🔍 检查服务状态..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ 部署成功！服务正在运行"
    echo "📍 访问地址: http://localhost:3000"
    echo "📚 API文档: http://localhost:3000/api/health"
    echo "🎯 示例页面: http://localhost:3000/index.html"
else
    echo "❌ 部署失败！服务未正常启动"
    exit 1
fi

echo "🎉 部署完成！"
