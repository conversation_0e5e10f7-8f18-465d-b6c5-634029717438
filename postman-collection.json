{"info": {"name": "钉钉微H5应用API", "description": "项目管理系统API接口集合，包含供应商管理、周预算管理和钉钉对公付款审批功能", "version": "1.3.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "认证管理", "item": [{"name": "钉钉登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"authCode\": \"your_auth_code\",\n  \"corpId\": \"your_corp_id\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/dingtalk", "host": ["{{baseUrl}}"], "path": ["auth", "<PERSON><PERSON><PERSON>"]}}}]}, {"name": "供应商管理", "item": [{"name": "创建供应商", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"优质达人供应商\",\n  \"shortName\": \"优质达人\",\n  \"code\": \"SUP001\",\n  \"contactPerson\": \"张经理\",\n  \"contactPhone\": \"***********\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"address\": \"北京市朝阳区xxx路xxx号\",\n  \"taxNumber\": \"91110000123456789X\",\n  \"bankAccount\": \"1234567890123456789\",\n  \"bankName\": \"中国银行北京分行\",\n  \"legalPerson\": \"张三\",\n  \"serviceTypes\": [\"influencer\", \"advertising\"],\n  \"preferredTaxRate\": \"special_6\",\n  \"creditLimit\": 1000000,\n  \"paymentTerms\": \"月结30天\",\n  \"rating\": 5,\n  \"notes\": \"优质供应商，合作愉快\"\n}"}, "url": {"raw": "{{baseUrl}}/suppliers", "host": ["{{baseUrl}}"], "path": ["suppliers"]}}}, {"name": "获取供应商列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/suppliers?page=1&pageSize=20", "host": ["{{baseUrl}}"], "path": ["suppliers"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "20"}]}}}, {"name": "获取单个供应商", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/suppliers/:supplierId", "host": ["{{baseUrl}}"], "path": ["suppliers", ":supplierId"], "variable": [{"key": "supplierId", "value": ""}]}}}, {"name": "更新供应商", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 4,\n  \"notes\": \"合作良好，但需要提升响应速度\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/suppliers/:supplierId", "host": ["{{baseUrl}}"], "path": ["suppliers", ":supplierId"], "variable": [{"key": "supplierId", "value": ""}]}}}, {"name": "删除供应商", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/suppliers/:supplierId", "host": ["{{baseUrl}}"], "path": ["suppliers", ":supplierId"], "variable": [{"key": "supplierId", "value": ""}]}}}, {"name": "获取供应商统计", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/suppliers/stats", "host": ["{{baseUrl}}"], "path": ["suppliers", "stats"]}}}]}, {"name": "周预算管理", "item": [{"name": "创建周预算", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"第1周达人投放预算\",\n  \"weekStartDate\": \"2024-01-01\",\n  \"weekEndDate\": \"2024-01-07\",\n  \"serviceType\": \"influencer\",\n  \"serviceContent\": \"小红书达人投放，包含图文和视频内容\",\n  \"remarks\": \"重点关注美妆类达人\",\n  \"contractAmount\": 50000,\n  \"taxRate\": \"special_6\",\n  \"supplierId\": \"supplier_id\"\n}"}, "url": {"raw": "{{baseUrl}}/projects/:projectId/weekly-budgets", "host": ["{{baseUrl}}"], "path": ["projects", ":projectId", "weekly-budgets"], "variable": [{"key": "projectId", "value": ""}]}}}, {"name": "批量创建周预算", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"startDate\": \"2024-01-08\",\n  \"endDate\": \"2024-01-28\",\n  \"serviceType\": \"advertising\",\n  \"defaultContractAmount\": 30000,\n  \"defaultTaxRate\": \"special_3\"\n}"}, "url": {"raw": "{{baseUrl}}/projects/:projectId/weekly-budgets/batch", "host": ["{{baseUrl}}"], "path": ["projects", ":projectId", "weekly-budgets", "batch"], "variable": [{"key": "projectId", "value": ""}]}}}, {"name": "获取周预算列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/weekly-budgets?page=1&pageSize=20", "host": ["{{baseUrl}}"], "path": ["weekly-budgets"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "20"}, {"key": "projectId", "value": "", "disabled": true}, {"key": "serviceType", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}]}}}, {"name": "获取单个周预算", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/weekly-budgets/:budgetId", "host": ["{{baseUrl}}"], "path": ["weekly-budgets", ":budgetId"], "variable": [{"key": "budgetId", "value": ""}]}}}, {"name": "更新周预算", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"budget_id\",\n  \"status\": \"approved\",\n  \"paidAmount\": 25000,\n  \"remarks\": \"已支付50%预付款\"\n}"}, "url": {"raw": "{{baseUrl}}/weekly-budgets/:budgetId", "host": ["{{baseUrl}}"], "path": ["weekly-budgets", ":budgetId"], "variable": [{"key": "budgetId", "value": ""}]}}}, {"name": "删除周预算", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/weekly-budgets/:budgetId", "host": ["{{baseUrl}}"], "path": ["weekly-budgets", ":budgetId"], "variable": [{"key": "budgetId", "value": ""}]}}}, {"name": "获取周预算统计", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/weekly-budgets/stats", "host": ["{{baseUrl}}"], "path": ["weekly-budgets", "stats"]}}}]}, {"name": "测试接口（无认证）", "item": [{"name": "创建供应商（测试）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"测试供应商\",\n  \"shortName\": \"测试\",\n  \"code\": \"TEST001\",\n  \"contactPerson\": \"测试联系人\",\n  \"contactPhone\": \"***********\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"serviceTypes\": [\"influencer\"],\n  \"preferredTaxRate\": \"special_6\",\n  \"rating\": 5\n}"}, "url": {"raw": "{{baseUrl}}/test/suppliers", "host": ["{{baseUrl}}"], "path": ["test", "suppliers"]}}}, {"name": "获取供应商列表（测试）", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/test/suppliers", "host": ["{{baseUrl}}"], "path": ["test", "suppliers"]}}}, {"name": "创建周预算（测试）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"测试周预算\",\n  \"weekStartDate\": \"2024-01-01\",\n  \"weekEndDate\": \"2024-01-07\",\n  \"serviceType\": \"influencer\",\n  \"serviceContent\": \"测试服务内容\",\n  \"contractAmount\": 10000,\n  \"taxRate\": \"special_6\"\n}"}, "url": {"raw": "{{baseUrl}}/test/projects/:projectId/weekly-budgets", "host": ["{{baseUrl}}"], "path": ["test", "projects", ":projectId", "weekly-budgets"], "variable": [{"key": "projectId", "value": "cmbrjp1dg0000kr94pussgpos", "description": "使用现有项目ID"}]}}}, {"name": "获取周预算列表（测试）", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/test/weekly-budgets", "host": ["{{baseUrl}}"], "path": ["test", "weekly-budgets"]}}}, {"name": "批量创建周预算（测试）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"startDate\": \"2024-02-01\",\n  \"endDate\": \"2024-02-21\",\n  \"serviceType\": \"advertising\",\n  \"defaultContractAmount\": 20000,\n  \"defaultTaxRate\": \"special_3\"\n}"}, "url": {"raw": "{{baseUrl}}/test/projects/:projectId/weekly-budgets/batch", "host": ["{{baseUrl}}"], "path": ["test", "projects", ":projectId", "weekly-budgets", "batch"], "variable": [{"key": "projectId", "value": "cmbrjp1dg0000kr94pussgpos", "description": "使用现有项目ID"}]}}}, {"name": "获取项目列表（测试）", "request": {"method": "GET", "url": {"raw": "{{baseUrl}}/test/projects", "host": ["{{baseUrl}}"], "path": ["test", "projects"]}}}]}]}