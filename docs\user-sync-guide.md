# 用户同步功能使用指南

## 概述

用户同步功能解决了项目管理系统中用户信息查询效率低下的问题。通过将钉钉用户信息同步到本地数据库，系统可以快速查询用户详情，而不需要每次都调用钉钉API。

## 功能特性

### 1. 自动同步
- 系统启动后自动开始定时同步
- 默认每小时同步一次
- 只同步过期的用户信息（超过24小时）
- 支持配置同步间隔

### 2. 手动同步
- 支持同步指定用户列表
- 支持全量用户同步
- 支持强制同步（忽略时间限制）

### 3. 智能缓存
- 优先从本地数据库获取用户信息
- 自动检查数据是否过期
- 按需同步过期数据

### 4. 错误处理
- 自动重试机制
- 详细的错误日志
- 同步失败不影响系统正常运行

## 数据库设计

### User 表结构
```sql
CREATE TABLE "users" (
    "userid" VARCHAR(50) NOT NULL,           -- 钉钉用户ID（主键）
    "unionid" VARCHAR(100),                  -- 员工在当前开发者企业账号范围内的唯一标识
    "name" VARCHAR(100) NOT NULL,            -- 用户姓名
    "avatar" VARCHAR(500),                   -- 头像URL
    "stateCode" VARCHAR(10),                 -- 国际电话区号
    "managerUserid" VARCHAR(50),             -- 直属主管userid
    "mobile" VARCHAR(20),                    -- 手机号
    "hideMobile" BOOLEAN DEFAULT false,      -- 是否隐藏手机号
    "telephone" VARCHAR(20),                 -- 分机号
    "jobNumber" VARCHAR(50),                 -- 工号
    "title" VARCHAR(100),                    -- 职位
    "email" VARCHAR(100),                    -- 邮箱
    "workPlace" VARCHAR(200),                -- 办公地点
    "remark" TEXT,                           -- 备注
    "loginId" VARCHAR(100),                  -- 专属帐号登录名
    "exclusiveAccountType" VARCHAR(20),      -- 专属帐号类型
    "exclusiveAccount" BOOLEAN DEFAULT false, -- 是否专属帐号
    "deptIdList" INTEGER[],                  -- 所属部门ID列表
    "extension" TEXT,                        -- 扩展属性
    "hiredDate" TIMESTAMPTZ,                 -- 入职时间
    "active" BOOLEAN DEFAULT true,           -- 是否激活了钉钉
    "realAuthed" BOOLEAN DEFAULT false,      -- 是否完成了实名认证
    "orgEmail" VARCHAR(100),                 -- 企业邮箱
    "orgEmailType" VARCHAR(50),              -- 企业邮箱类型
    "senior" BOOLEAN DEFAULT false,          -- 是否为企业的高管
    "admin" BOOLEAN DEFAULT false,           -- 是否为企业的管理员
    "boss" BOOLEAN DEFAULT false,            -- 是否为企业的老板
    "lastSyncAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 最后同步时间
    "isActive" BOOLEAN NOT NULL DEFAULT true, -- 是否在系统中激活

    CONSTRAINT "users_pkey" PRIMARY KEY ("userid")
);

-- 索引
CREATE INDEX "users_name_idx" ON "users"("name");
CREATE INDEX "users_isActive_idx" ON "users"("isActive");
CREATE INDEX "users_mobile_idx" ON "users"("mobile");
CREATE INDEX "users_email_idx" ON "users"("email");
CREATE INDEX "users_managerUserid_idx" ON "users"("managerUserid");
```

## API 接口

### 1. 同步指定用户信息
```http
POST /api/users/sync
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "userIds": ["user001", "user002", "user003"],
  "force": false  // 是否强制同步
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "syncedUsers": 2,
    "failedUsers": 1,
    "errors": [
      {
        "userid": "user003",
        "error": "用户不存在或已被删除"
      }
    ],
    "totalUsers": 3
  },
  "message": "用户同步完成: 成功 2, 失败 1"
}
```

### 2. 同步所有用户信息
```http
POST /api/users/sync-all
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "force": false
}
```

### 3. 批量获取用户信息
```http
POST /api/users/batch
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "userIds": ["user001", "user002", "user003"]
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "userid": "user001",
        "name": "张三",
        "avatar": "https://example.com/avatar1.jpg",
        "department": "技术部"
      },
      {
        "userid": "user002",
        "name": "李四",
        "avatar": "https://example.com/avatar2.jpg",
        "department": "产品部"
      }
    ],
    "total": 2
  },
  "message": "获取用户信息成功"
}
```

### 4. 获取单个用户信息
```http
GET /api/users/{userid}
Authorization: Bearer your-jwt-token
```

### 5. 获取本地用户列表（分页）
```http
GET /api/users?page=1&pageSize=20&keyword=张三&isActive=true
Authorization: Bearer your-jwt-token
```

### 6. 获取用户同步状态统计
```http
GET /api/users/sync/stats
Authorization: Bearer your-jwt-token
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "totalUsers": 150,
    "activeUsers": 145,
    "inactiveUsers": 5,
    "recentSyncUsers": 120,
    "outdatedUsers": 30,
    "syncRate": 80
  },
  "message": "获取用户同步统计成功"
}
```

## 使用场景

### 1. 项目管理中的用户信息显示
当查询项目列表时，系统会自动填充执行PM和内容媒介的用户信息：

```typescript
// 项目查询时自动填充用户信息
const projects = await projectService.getProjects();
// 每个项目会包含：
// - executorPMInfo: { userid, name, avatar, department }
// - contentMediaInfo: [{ userid, name, avatar, department }]
```

### 2. 用户选择器组件
前端可以直接从本地API获取用户列表，无需每次调用钉钉API：

```javascript
// 获取用户列表用于选择器
const response = await fetch('/api/users?pageSize=100');
const { users } = response.data;
```

### 3. 用户搜索功能
支持按姓名、手机号等字段搜索用户：

```javascript
// 搜索用户
const response = await fetch('/api/users?keyword=张三');
```

## 配置选项

### 环境变量
```env
# 用户同步配置
USER_SYNC_INTERVAL_MINUTES=60    # 同步间隔（分钟）
USER_SYNC_ENABLED=true           # 是否启用自动同步
USER_SYNC_BATCH_SIZE=50          # 批量处理大小
USER_SYNC_MAX_RETRIES=3          # 最大重试次数
```

### 代码配置
```typescript
import { userSyncScheduler } from './services/userSyncScheduler.js';

// 更新配置
userSyncScheduler.updateOptions({
  syncIntervalMinutes: 30,  // 改为30分钟同步一次
  enableAutoSync: true,
  maxConcurrentSyncs: 2
});

// 手动触发同步
const results = await userSyncScheduler.triggerSync();

// 获取同步状态
const status = userSyncScheduler.getStatus();
```

## 最佳实践

### 1. 同步策略
- **首次部署**：执行全量用户同步
- **日常运行**：依赖自动定时同步
- **紧急情况**：使用强制同步更新特定用户

### 2. 性能优化
- 合理设置同步间隔，避免过于频繁
- 使用批量接口减少API调用次数
- 监控同步状态，及时处理异常

### 3. 错误处理
- 定期检查同步统计，关注失败率
- 对于持续失败的用户，检查是否已离职
- 保持日志记录，便于问题排查

## 监控和维护

### 1. 同步状态监控
```typescript
// 获取同步状态
const status = userSyncScheduler.getStatus();
console.log('同步状态:', status);

// 检查同步统计
const response = await fetch('/api/users/sync/stats');
const stats = response.data;
```

### 2. 日志监控
系统会输出详细的同步日志：
```
[INFO] 启动用户同步调度器，同步间隔: 60 分钟
[INFO] 开始定时用户同步...
[INFO] 需要同步 25 个用户信息
[INFO] 用户 user001 同步成功
[WARN] 用户 user002 同步失败: 用户不存在或已被删除
[INFO] 定时用户同步完成: 成功 24, 失败 1, 耗时 3500ms
```

### 3. 数据清理
定期清理非活跃用户数据：
```sql
-- 清理超过6个月未同步的非活跃用户
DELETE FROM users 
WHERE isActive = false 
  AND lastSyncAt < NOW() - INTERVAL '6 months';
```

## 故障排除

### 1. 同步失败
- 检查钉钉API配置是否正确
- 验证access_token是否有效
- 确认用户是否仍在企业中

### 2. 性能问题
- 调整同步间隔和批量大小
- 检查数据库索引是否正常
- 监控API调用频率限制

### 3. 数据不一致
- 执行强制同步更新数据
- 检查用户权限变更
- 验证部门结构变化

通过以上用户同步功能，您的项目管理系统将能够高效地管理和查询用户信息，大大提升系统性能和用户体验。
