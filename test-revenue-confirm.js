// 测试收入确认功能API
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api/test';

async function testRevenueConfirmAPI() {
  console.log('🧪 开始测试收入确认功能API...\n');

  try {
    // 0. 首先创建一个测试品牌
    console.log('0. 创建测试品牌...');
    const createBrandData = {
      name: '测试品牌',
      description: '用于测试收入确认功能的品牌'
    };

    const createBrandResponse = await fetch(`${BASE_URL}/brands`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createBrandData)
    });

    const createBrandResult = await createBrandResponse.json();
    console.log('创建品牌响应状态:', createBrandResponse.status);

    let brandId = 'brand-001'; // 默认品牌ID
    if (createBrandResult.success && createBrandResult.data) {
      brandId = createBrandResult.data.id;
      console.log('✅ 创建测试品牌成功, ID:', brandId);
    } else {
      console.log('❌ 创建测试品牌失败，使用默认品牌ID:', brandId);
    }

    // 1. 创建测试项目
    console.log('\n1. 创建测试项目...');
    const createProjectData = {
      documentType: 'project_initiation',
      brandId: brandId,
      projectName: '测试确认收入项目',
      period: {
        startDate: '2024-06-01',
        endDate: '2024-12-31'
      },
      budget: {
        planningBudget: 1000000,
        influencerBudget: 600000,
        adBudget: 300000,
        otherBudget: 100000
      },
      cost: {
        influencerCost: 0,
        adCost: 0,
        otherCost: 0,
        estimatedInfluencerRebate: 0
      },
      executorPM: 'user-001',
      contentMediaIds: ['user-002'],
      contractType: 'single',
      settlementRules: '按月结算',
      kpi: '测试KPI'
    };

    const createProjectResponse = await fetch(`${BASE_URL}/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createProjectData)
    });

    const createProjectResult = await createProjectResponse.json();
    console.log('创建项目响应状态:', createProjectResponse.status);

    let projectId = null;
    if (createProjectResult.success && createProjectResult.data) {
      projectId = createProjectResult.data.id;
      console.log('✅ 创建测试项目成功, ID:', projectId);
    } else {
      console.log('❌ 创建测试项目失败:', createProjectResult.message);
      // 如果创建失败，使用一个已存在的项目ID
      projectId = 'project-001';
      console.log('使用默认项目ID:', projectId);
    }

    // 2. 创建测试收入
    console.log('\n2. 创建测试收入...');
    const createRevenueData = {
      title: '测试确认收入',
      revenueType: 'project_income',
      plannedAmount: 100000,
      plannedDate: '2024-06-30',
      milestone: '项目第一阶段完成',
      paymentTerms: '收到发票后30天内付款',
      notes: '这是一个测试收入记录'
    };

    const createResponse = await fetch(`${BASE_URL}/projects/${projectId}/revenues`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createRevenueData)
    });

    const createResult = await createResponse.json();
    console.log('创建收入响应状态:', createResponse.status);
    
    let revenueId = null;
    if (createResult.success && createResult.data) {
      revenueId = createResult.data.id;
      console.log('✅ 创建项目收入成功, ID:', revenueId);
    } else {
      console.log('❌ 创建项目收入失败:', createResult.message);
      return;
    }

    // 3. 测试单个收入确认
    console.log('\n3. 测试单个收入确认...');
    const confirmData = {
      actualAmount: 95000,
      confirmedDate: '2024-06-25',
      notes: '收入已确认，实际金额略低于预期'
    };

    const confirmResponse = await fetch(`${BASE_URL}/revenues/${revenueId}/confirm`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(confirmData)
    });

    const confirmResult = await confirmResponse.json();
    console.log('确认收入响应状态:', confirmResponse.status);
    
    if (confirmResult.success) {
      console.log('✅ 单个收入确认成功');
      console.log('确认后数据:', {
        id: confirmResult.data.id,
        status: confirmResult.data.status,
        actualAmount: confirmResult.data.actualAmount,
        confirmedDate: confirmResult.data.confirmedDate
      });
    } else {
      console.log('❌ 单个收入确认失败:', confirmResult.message);
    }

    // 3. 创建更多测试收入用于批量确认
    console.log('\n3. 创建更多测试收入用于批量确认...');
    const revenueIds = [];
    
    for (let i = 1; i <= 3; i++) {
      const batchRevenueData = {
        title: `批量测试收入 ${i}`,
        revenueType: 'project_income',
        plannedAmount: 50000 + i * 10000,
        plannedDate: '2024-07-15',
        milestone: `批量测试里程碑 ${i}`,
        notes: `批量测试收入记录 ${i}`
      };

      const batchCreateResponse = await fetch(`${BASE_URL}/projects/${projectId}/revenues`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(batchRevenueData)
      });

      const batchCreateResult = await batchCreateResponse.json();
      if (batchCreateResult.success && batchCreateResult.data) {
        revenueIds.push(batchCreateResult.data.id);
        console.log(`✅ 创建批量测试收入 ${i} 成功, ID:`, batchCreateResult.data.id);
      } else {
        console.log(`❌ 创建批量测试收入 ${i} 失败:`, batchCreateResult.message);
      }
    }

    // 4. 测试批量确认收入
    if (revenueIds.length > 0) {
      console.log('\n4. 测试批量确认收入...');
      const batchConfirmData = {
        revenues: revenueIds.map((id, index) => ({
          id: id,
          actualAmount: 45000 + index * 5000,
          confirmedDate: '2024-07-10',
          notes: `批量确认收入 ${index + 1}`
        }))
      };

      const batchConfirmResponse = await fetch(`${BASE_URL}/revenues/batch-confirm`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(batchConfirmData)
      });

      const batchConfirmResult = await batchConfirmResponse.json();
      console.log('批量确认收入响应状态:', batchConfirmResponse.status);
      
      if (batchConfirmResult.success) {
        console.log('✅ 批量确认收入成功');
        console.log('批量确认结果:', {
          successCount: batchConfirmResult.data.successCount,
          failureCount: batchConfirmResult.data.failureCount,
          totalProcessed: batchConfirmResult.data.results.length
        });
        
        // 显示详细结果
        console.log('\n📋 批量确认详细结果:');
        batchConfirmResult.data.results.forEach((result, index) => {
          if (result.success) {
            console.log(`  ✅ 收入 ${index + 1} (${result.id}): 确认成功`);
          } else {
            console.log(`  ❌ 收入 ${index + 1} (${result.id}): 确认失败 - ${result.error}`);
          }
        });
      } else {
        console.log('❌ 批量确认收入失败:', batchConfirmResult.message);
      }
    }

    // 5. 验证确认后的收入状态
    console.log('\n5. 验证确认后的收入状态...');
    const listResponse = await fetch(`${BASE_URL}/revenues?status=received`);
    
    const listResult = await listResponse.json();
    if (listResult.success) {
      console.log('✅ 获取已确认收入列表成功');
      console.log(`📊 已确认收入数量: ${listResult.data.revenues.length}`);
      
      if (listResult.data.revenues.length > 0) {
        console.log('\n📋 已确认收入示例:');
        const confirmedRevenue = listResult.data.revenues[0];
        console.log({
          id: confirmedRevenue.id,
          title: confirmedRevenue.title,
          status: confirmedRevenue.status,
          plannedAmount: confirmedRevenue.plannedAmount,
          actualAmount: confirmedRevenue.actualAmount,
          confirmedDate: confirmedRevenue.confirmedDate
        });
      }
    } else {
      console.log('❌ 获取已确认收入列表失败:', listResult.message);
    }

    // 6. 测试错误情况
    console.log('\n6. 测试错误情况...');
    
    // 6.1 测试确认不存在的收入
    console.log('6.1 测试确认不存在的收入...');
    const invalidConfirmResponse = await fetch(`${BASE_URL}/revenues/invalid-id/confirm`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        actualAmount: 50000,
        confirmedDate: '2024-07-01'
      })
    });
    
    const invalidConfirmResult = await invalidConfirmResponse.json();
    if (!invalidConfirmResult.success) {
      console.log('✅ 正确处理了不存在收入的确认请求');
    } else {
      console.log('❌ 应该拒绝不存在收入的确认请求');
    }

    // 6.2 测试无效的确认数据
    console.log('6.2 测试无效的确认数据...');
    const invalidDataResponse = await fetch(`${BASE_URL}/revenues/${revenueId}/confirm`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        actualAmount: -1000, // 负数金额
        confirmedDate: 'invalid-date'
      })
    });
    
    const invalidDataResult = await invalidDataResponse.json();
    if (!invalidDataResult.success) {
      console.log('✅ 正确验证了无效的确认数据');
    } else {
      console.log('❌ 应该拒绝无效的确认数据');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🏁 收入确认功能API测试完成');
}

// 运行测试
testRevenueConfirmAPI();
