# 钉钉审批附件功能测试指南

## 🎯 功能概述

现在钉钉审批功能已经完全支持附件上传，包括：
1. 文件上传到钉钉媒体库
2. 审批表单中显示附件
3. 支持多种文件格式

## ✅ 已实现的功能

### 1. 钉钉媒体文件上传
- ✅ `uploadMedia()` - 单文件上传到钉钉媒体库
- ✅ `uploadAttachmentFiles()` - 批量文件上传到钉钉媒体库
- ✅ 自动识别文件类型（image/voice/video/file）
- ✅ 返回钉钉媒体ID

### 2. 审批表单附件支持
- ✅ 在 `PaymentApprovalFormData` 中添加了 `attachmentFiles` 和 `attachmentMediaIds` 字段
- ✅ 在 `createPaymentApprovalFormData()` 中添加了钉钉附件组件支持
- ✅ 使用 `DDAttachment` 组件类型显示附件

### 3. API 接口完善
- ✅ `/approvals/upload/{type}` - 上传文件到钉钉媒体库
- ✅ `/approvals/payment-with-files` - 发起带附件的审批
- ✅ 支持 multipart/form-data 文件上传

### 4. 控制器和服务层
- ✅ `ApprovalController.uploadApprovalFiles()` - 处理文件上传
- ✅ `ApprovalController.createPaymentApprovalWithFiles()` - 处理带附件的审批
- ✅ `DingTalkService.createPaymentApproval()` - 自动处理附件上传

## 🔄 完整的使用流程

### 方式一：先上传附件，后发起审批

```bash
# 1. 上传附件到钉钉媒体库
curl -X POST "http://localhost:3000/api/approvals/upload/attachment" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@document.pdf" \
  -F "file=@contract.docx"

# 响应
{
  "success": true,
  "data": {
    "files": [
      {
        "filename": "document.pdf",
        "mediaId": "dingtalk_media_id_123",
        "size": 1024000
      }
    ],
    "mediaIds": ["dingtalk_media_id_123", "dingtalk_media_id_456"]
  }
}

# 2. 发起审批（使用传统接口，传入媒体ID）
curl -X POST "http://localhost:3000/api/approvals/payment" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "weeklyBudgetId": "budget_123",
    "totalAmount": 10000,
    "paymentReason": "项目费用支付",
    "department": 1,
    "attachmentMediaIds": ["dingtalk_media_id_123", "dingtalk_media_id_456"]
  }'
```

### 方式二：一次性上传附件并发起审批

```bash
curl -X POST "http://localhost:3000/api/approvals/payment-with-files" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "weeklyBudgetId=budget_123" \
  -F "totalAmount=10000" \
  -F "paymentReason=项目费用支付" \
  -F "department=1" \
  -F "contractEntity=company_a" \
  -F "expectedPaymentDate=2024-01-15" \
  -F "paymentMethod=bank_transfer" \
  -F 'receivingAccount={"accountName":"收款方","accountNumber":"*********","bankName":"工商银行"}' \
  -F "remark=备注信息" \
  -F "file=@document.pdf" \
  -F "file=@contract.docx"

# 响应
{
  "success": true,
  "data": {
    "processInstanceId": "dingtalk_process_instance_id",
    "approvalId": "approval_id_123",
    "uploadedFiles": 2
  },
  "message": "发起审批成功，已上传2个附件"
}
```

## 🧪 测试步骤

### 1. 测试文件上传到钉钉媒体库

```javascript
// 前端测试代码
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('/api/approvals/upload/attachment', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('上传成功:', data);
  // 检查是否返回了钉钉媒体ID
  console.log('媒体ID:', data.data.mediaIds);
});
```

### 2. 测试带附件的审批发起

```javascript
// 前端测试代码
const formData = new FormData();
formData.append('weeklyBudgetId', 'budget_123');
formData.append('totalAmount', '10000');
formData.append('paymentReason', '项目费用支付');
formData.append('department', '1');
formData.append('file', fileInput.files[0]);
formData.append('file', fileInput.files[1]);

fetch('/api/approvals/payment-with-files', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('审批发起成功:', data);
  // 检查是否返回了钉钉审批实例ID
  console.log('审批实例ID:', data.data.processInstanceId);
});
```

### 3. 验证钉钉审批表单中的附件

1. 登录钉钉工作台
2. 进入审批应用
3. 查看发起的审批实例
4. 确认附件是否正确显示在审批表单中
5. 测试附件下载功能

## 🔧 配置要求

### 环境变量
```env
# 钉钉配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_CORP_ID=your_corp_id

# 审批模板配置
APPROVAL_PROCESS_CODE=PROC-PAYMENT-001
```

### 钉钉后台配置
1. **审批模板设置**
   - 确保审批模板支持附件字段
   - 附件字段名称：`附件`
   - 组件类型：`DDAttachment`

2. **应用权限**
   - 审批管理权限
   - 媒体文件上传权限
   - 通讯录读取权限

## 🚨 注意事项

1. **文件大小限制**
   - 钉钉媒体库单文件最大 20MB
   - 建议对上传文件进行大小验证

2. **文件格式支持**
   - 图片：jpg, jpeg, png, gif
   - 文档：pdf, doc, docx, xls, xlsx
   - 其他：txt, zip 等

3. **错误处理**
   - 网络超时处理
   - 文件格式验证
   - 钉钉API错误处理

4. **性能优化**
   - 大文件上传进度显示
   - 并发上传控制
   - 上传失败重试机制

## 📝 API 文档更新

API 文档已更新，包含：
- `/approvals/upload/{type}` 接口说明
- `/approvals/payment-with-files` 接口说明
- 请求参数和响应格式
- 使用示例和注意事项

现在您的钉钉审批系统已经完全支持附件功能！🎉
