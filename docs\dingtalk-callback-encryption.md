# 钉钉回调加密配置指南

本项目已实现钉钉回调的加密验证功能，确保回调数据的安全性和完整性。

## 🔐 加密原理

钉钉回调使用 AES 加密和 SHA1 签名验证：

1. **签名验证**: 使用 SHA1 对 token、timestamp、nonce 和 encrypt 进行签名
2. **AES 解密**: 使用 AES-256-CBC 模式解密回调数据
3. **数据完整性**: 验证解密后的数据格式和内容

## 📋 配置步骤

### 1. 获取钉钉回调配置

在钉钉开放平台应用管理后台：

1. 进入应用详情页
2. 找到"事件与回调"或"回调配置"
3. 获取以下配置信息：
   - **Token**: 回调验证令牌
   - **AES Key**: 数据加密密钥
   - **Suite Key**: 应用标识（通常与 App Key 相同）

### 2. 配置环境变量

在 `.env` 文件中添加：

```env
# 钉钉回调加密配置
DINGTALK_CALLBACK_TOKEN=your_callback_token_here
DINGTALK_AES_KEY=your_aes_key_here
DINGTALK_SUITE_KEY=your_suite_key_here
```

### 3. 设置回调地址

在钉钉后台配置回调地址：

- **审批回调**: `https://your-domain.com/api/dingtalk/callback/approval`
- **通用回调**: `https://your-domain.com/api/dingtalk/callback/general`

## 🛠️ API 接口

### 审批回调接口

```
POST /api/dingtalk/callback/approval
```

**查询参数**:
- `signature`: 签名
- `timestamp`: 时间戳
- `nonce`: 随机数

**请求体**:
```json
{
  "encrypt": "加密的回调数据"
}
```

**响应**: 加密的响应数据

### 回调验证接口

```
GET /api/dingtalk/callback/approval
```

用于钉钉验证回调地址的有效性。

**查询参数**:
- `msg_signature`: 消息签名
- `timestamp`: 时间戳
- `nonce`: 随机数
- `echostr`: 加密的验证字符串

**响应**: 解密后的验证字符串

### 通用回调接口

```
POST /api/dingtalk/callback/general
```

处理其他类型的钉钉回调事件（用户变更、部门变更等）。

### 配置测试接口

```
GET /api/dingtalk/callback/test
```

测试回调加密配置是否正确。

## 🔍 支持的回调事件

### 审批事件
- `bpms_instance_change`: 审批实例状态变更

### 通讯录事件
- `user_add_org`: 用户加入企业
- `user_modify_org`: 用户信息修改
- `user_leave_org`: 用户离开企业
- `org_admin_add`: 管理员添加
- `org_admin_remove`: 管理员移除
- `org_dept_create`: 部门创建
- `org_dept_modify`: 部门修改
- `org_dept_remove`: 部门删除

## 🧪 测试验证

### 1. 配置测试

访问测试接口验证配置：

```bash
curl http://localhost:3000/api/dingtalk/callback/test
```

### 2. 回调验证

使用钉钉开发者工具或 Postman 模拟回调请求。

### 3. 日志检查

查看服务器日志确认回调处理：

```bash
# 查看实时日志
npm run dev

# 或查看日志文件
tail -f logs/app.log
```

## 🔧 故障排除

### 常见问题

1. **签名验证失败**
   - 检查 Token 配置是否正确
   - 确认时间戳在有效范围内（5分钟）
   - 验证签名算法实现

2. **解密失败**
   - 检查 AES Key 配置
   - 确认 AES Key 格式（Base64 编码）
   - 验证加密模式和填充方式

3. **回调地址无法访问**
   - 确认服务器公网可访问
   - 检查防火墙和安全组配置
   - 验证 HTTPS 证书（生产环境）

### 调试技巧

1. **启用详细日志**
   ```env
   LOG_LEVEL=debug
   ```

2. **使用测试工具**
   - 钉钉开发者工具
   - Postman 回调模拟
   - 在线加密解密工具

3. **检查配置**
   ```bash
   # 测试配置接口
   curl http://localhost:3000/api/dingtalk/callback/test
   ```

## 📚 相关文档

- [钉钉开放平台 - 回调配置](https://open.dingtalk.com/document/orgapp/configure-event-subcription)
- [钉钉回调加密说明](https://open.dingtalk.com/document/orgapp/callback-encryption-instructions)
- [审批事件回调](https://open.dingtalk.com/document/orgapp/approval-events)

## 🔒 安全建议

1. **保护配置信息**
   - 不要将 Token 和 AES Key 提交到代码仓库
   - 使用环境变量或密钥管理服务
   - 定期轮换密钥

2. **验证回调来源**
   - 始终验证签名
   - 检查时间戳防止重放攻击
   - 记录异常回调请求

3. **监控和告警**
   - 监控回调处理成功率
   - 设置异常告警
   - 定期检查日志

## 🚀 部署注意事项

1. **生产环境**
   - 使用 HTTPS
   - 配置正确的域名
   - 确保服务高可用

2. **测试环境**
   - 可以使用 ngrok 等工具暴露本地服务
   - 配置测试专用的回调地址

3. **监控**
   - 监控回调处理延迟
   - 统计回调事件类型和频率
   - 设置异常告警机制
