import crypto from 'crypto';
import { env } from '../config/env.js';

/**
 * 钉钉回调加密解密服务
 * 基于官方 demo 实现：https://github.com/elixirChain/dingtalk-encrypt
 */
export class DingTalkEncryptService {
  private readonly token: string;
  private readonly encodingAesKey: string;
  private readonly aesKey: Buffer;
  private readonly corpId: string;
  private readonly iv: Buffer;
  private readonly AES_ENCODE_KEY_LENGTH = 43;
  private readonly RANDOM_LENGTH = 16;

  constructor() {
    this.token = env.DINGTALK_CALLBACK_TOKEN || '';
    this.encodingAesKey = env.DINGTALK_AES_KEY || '';
    this.corpId = env.DINGTALK_SUITE_KEY || env.DINGTALK_APP_KEY || '';

    if (!this.token || !this.encodingAesKey || !this.corpId) {
      console.warn('⚠️ 钉钉回调加密配置不完整，回调验证可能失败');
      console.warn('需要配置: DINGTALK_CALLBACK_TOKEN, DINGTALK_AES_KEY, DINGTALK_SUITE_KEY');
    }

    // 验证 AES Key 长度
    if (this.encodingAesKey && this.encodingAesKey.length !== this.AES_ENCODE_KEY_LENGTH) {
      console.log('AES Key 长度:', this.encodingAesKey.length);
      console.log('期望长度:', this.AES_ENCODE_KEY_LENGTH);
      console.log('请检查 DINGTALK_AES_KEY 配置是否正确');
      console.log('当前配置:', this.encodingAesKey);
      throw new Error(`AES Key 长度必须是 ${this.AES_ENCODE_KEY_LENGTH} 个字符`);
    }

    // 初始化加密参数
    this.aesKey = Buffer.from(this.encodingAesKey + '=', 'base64');
    this.iv = this.aesKey.slice(0, 16);
  }

  /**
   * 验证回调签名
   */
  verifySignature(signature: string, timestamp: string, nonce: string, encrypt?: string): boolean {
    try {
      const elements = [this.token, timestamp, nonce];
      if (encrypt) {
        elements.push(encrypt);
      }

      // 按字典序排序
      elements.sort();

      // 拼接字符串
      const concatenated = elements.join('');

      // SHA1 加密
      const sha1 = crypto.createHash('sha1');
      sha1.update(concatenated);
      const hash = sha1.digest('hex');

      return hash === signature;
    } catch (error) {
      console.error('验证回调签名失败:', error);
      return false;
    }
  }

  /**
   * 获取签名
   */
  getSignature(token: string, timestamp: string, nonce: string, encrypt: string): string {
    const elements = [token, timestamp, nonce, encrypt];
    elements.sort();
    const concatenated = elements.join('');

    const sha1 = crypto.createHash('sha1');
    sha1.update(concatenated);
    return sha1.digest('hex');
  }

  /**
   * 解密回调数据
   */
  decrypt(encryptedData: string): string | null {
    try {
      if (!this.aesKey) {
        throw new Error('AES Key 未配置');
      }

      // 解密
      const decipher = crypto.createDecipheriv('AES-256-CBC', this.aesKey, this.iv);
      decipher.setAutoPadding(false);
      const decrypted = Buffer.concat([decipher.update(encryptedData, 'base64')]);

      // 解析解密后的数据
      const textLen = decrypted.slice(16, 20).readUInt32BE();
      const plainText = decrypted.slice(20, 20 + textLen).toString();

      // 验证 corpId
      let pad = decrypted.length - 16 - 4 - textLen - 20;
      if (pad > 31) pad = 0;
      const finalDecrypt = decrypted.slice(0, decrypted.length - pad);
      const corpId = finalDecrypt.slice(20 + textLen).toString();

      // 验证 corpId（允许部分匹配，因为可能有填充问题）
      if (this.corpId !== corpId && !corpId.startsWith(this.corpId) && !this.corpId.startsWith(corpId)) {
        console.warn(`⚠️ CorpId 不匹配: 期望 ${this.corpId}, 实际 ${corpId}`);
        // 不抛出错误，只是警告，因为有时候会有填充问题
      }

      return plainText;
    } catch (error) {
      console.error('解密回调数据失败:', error);
      return null;
    }
  }

  /**
   * 加密响应数据
   */
  encrypt(random: string, plaintext: string): string | null {
    try {
      if (!this.aesKey) {
        throw new Error('AES Key 未配置');
      }

      const randomBuf = Buffer.from(random);
      const plainTextBuf = Buffer.from(plaintext);
      const textLen = plainTextBuf.length;
      const textLenBuf = Buffer.from([
        (textLen >> 24) & 255,
        (textLen >> 16) & 255,
        (textLen >> 8) & 255,
        textLen & 255
      ]);
      const corpIdBuf = Buffer.from(this.corpId);

      // 计算填充
      const padCount = 32 - (randomBuf.length + textLenBuf.length + plainTextBuf.length + corpIdBuf.length) % 32;
      const padBuf = Buffer.from(new Array(padCount).fill(padCount));

      // 拼接所有数据
      const finalBuf = Buffer.concat([randomBuf, textLenBuf, plainTextBuf, corpIdBuf, padBuf]);

      // AES 加密
      const cipher = crypto.createCipheriv('AES-256-CBC', this.aesKey, this.iv);
      cipher.setAutoPadding(false);
      const encrypted = Buffer.concat([cipher.update(finalBuf)]);

      return encrypted.toString('base64');
    } catch (error) {
      console.error('加密响应数据失败:', error);
      return null;
    }
  }

  /**
   * 生成回调响应
   */
  generateCallbackResponse(plaintext: string, timestamp?: string, nonce?: string): any {
    try {
      const ts = timestamp || Date.now().toString();
      const nonceStr = nonce || this.generateRandomString(8);
      const randomStr = this.generateRandomString(this.RANDOM_LENGTH);

      const encrypted = this.encrypt(randomStr, plaintext);
      if (!encrypted) {
        throw new Error('加密失败');
      }

      // 生成签名
      const signature = this.getSignature(this.token, ts, nonceStr, encrypted);

      return {
        msg_signature: signature,
        timeStamp: ts,
        nonce: nonceStr,
        encrypt: encrypted
      };
    } catch (error) {
      console.error('生成回调响应失败:', error);
      return {
        msg_signature: '',
        timeStamp: Date.now().toString(),
        nonce: '',
        encrypt: ''
      };
    }
  }

  /**
   * 解密回调消息（带签名验证）
   */
  getDecryptMsg(msgSignature: string, timestamp: string, nonce: string, encryptMsg: string): string | null {
    try {
      const signature = this.getSignature(this.token, timestamp, nonce, encryptMsg);
      if (signature !== msgSignature) {
        console.error('❌ 签名验证失败:', { expected: signature, actual: msgSignature });
        return null;
      }

      return this.decrypt(encryptMsg);
    } catch (error) {
      console.error('解密回调消息失败:', error);
      return null;
    }
  }

  /**
   * 获取加密响应对象
   */
  getEncryptedMap(plaintext: string, timestamp?: string, nonce?: string): any {
    const ts = timestamp || Date.now().toString();
    const nonceStr = nonce || this.generateRandomString(8);

    if (!plaintext) {
      throw new Error('plaintext 不能为空');
    }
    if (!ts) {
      throw new Error('timestamp 不能为空');
    }
    if (!nonceStr) {
      throw new Error('nonce 不能为空');
    }

    const randomStr = this.generateRandomString(this.RANDOM_LENGTH);
    const encrypt = this.encrypt(randomStr, plaintext);
    if (!encrypt) {
      throw new Error('加密失败');
    }

    const signature = this.getSignature(this.token, ts, nonceStr, encrypt);

    return {
      msg_signature: signature,
      encrypt: encrypt,
      timeStamp: ts,
      nonce: nonceStr
    };
  }

  /**
   * 生成随机字符串
   */
  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 验证回调请求
   */
  verifyCallback(params: {
    signature: string;
    timestamp: string;
    nonce: string;
    encrypt?: string;
  }): boolean {
    const { signature, timestamp, nonce, encrypt } = params;

    // 验证必要参数
    if (!signature || !timestamp || !nonce) {
      console.error('回调验证失败: 缺少必要参数');
      return false;
    }

    // 验证时间戳 (防止重放攻击) - 钉钉时间戳是毫秒级
    const now = Date.now();
    const callbackTime = parseInt(timestamp);
    const timeDiff = Math.abs(now - callbackTime);

    if (timeDiff > 5 * 60 * 1000) { // 5分钟内有效
      console.error('回调验证失败: 时间戳过期', { now, callbackTime, timeDiff });
      return false;
    }

    // 验证签名
    return this.verifySignature(signature, timestamp, nonce, encrypt);
  }

  /**
   * 处理回调请求
   */
  processCallback(params: {
    signature: string;
    timestamp: string;
    nonce: string;
    encrypt: string;
  }): { success: boolean; data?: any; error?: string } {
    try {
      // 使用 msg_signature 进行验证和解密
      const decryptedData = this.getDecryptMsg(params.signature, params.timestamp, params.nonce, params.encrypt);
      if (!decryptedData) {
        return { success: false, error: '数据解密失败或签名验证失败' };
      }

      // 解析 JSON
      let callbackData;
      try {
        callbackData = JSON.parse(decryptedData);
      } catch (error) {
        console.error('解析回调数据失败:', error);
        return { success: false, error: '数据格式错误' };
      }

      return { success: true, data: callbackData };
    } catch (error) {
      console.error('处理回调请求失败:', error);
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    }
  }
}

// 单例实例
export const dingTalkEncryptService = new DingTalkEncryptService();

