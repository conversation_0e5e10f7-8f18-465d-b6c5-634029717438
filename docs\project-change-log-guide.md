# 项目变更记录功能指南

## 功能概述

项目变更记录功能为项目管理系统提供了完整的操作审计跟踪能力，记录项目的所有变更操作，包括创建、更新、删除等操作的详细信息。

## 功能特性

### 1. 变更类型支持
- **CREATE**: 项目创建
- **UPDATE**: 项目更新
- **DELETE**: 项目删除
- **STATUS_CHANGE**: 状态变更
- **APPROVAL**: 审批操作
- **ATTACHMENT**: 附件操作

### 2. 记录内容
- 操作类型和标题
- 变更前后数据对比
- 变更字段列表
- 操作人员信息（用户ID、姓名、IP地址、用户代理）
- 操作时间
- 变更原因和描述

### 3. 查询功能
- 按项目ID查询变更历史
- 按操作人员查询
- 按变更类型过滤
- 按时间范围查询
- 分页和排序支持

## 数据库设计

### 表结构
```sql
CREATE TABLE project_change_logs (
  id TEXT PRIMARY KEY,
  change_type change_type NOT NULL,
  change_title VARCHAR(200) NOT NULL,
  change_details JSONB,
  before_data JSONB,
  after_data JSONB,
  changed_fields TEXT[],
  operator_id VARCHAR(50) NOT NULL,
  operator_name VARCHAR(100) NOT NULL,
  operator_ip VARCHAR(45),
  user_agent VARCHAR(500),
  reason TEXT,
  description TEXT,
  project_id TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### 索引设计
- `idx_project_change_logs_project_created`: 项目ID + 创建时间复合索引
- `idx_project_change_logs_operator`: 操作人员索引
- `idx_project_change_logs_change_type`: 变更类型索引
- `idx_project_change_logs_created_at`: 创建时间索引

## API接口

### 1. 获取变更记录列表
```http
GET /api/change-logs
```

**查询参数:**
- `projectId`: 项目ID（可选）
- `operatorId`: 操作人员ID（可选）
- `changeType`: 变更类型（可选）
- `startDate`: 开始日期（可选）
- `endDate`: 结束日期（可选）
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认20）
- `sortBy`: 排序字段（默认createdAt）
- `sortOrder`: 排序方向（默认desc）

**响应示例:**
```json
{
  "success": true,
  "data": {
    "changeLogs": [
      {
        "id": "log-001",
        "changeType": "UPDATE",
        "changeTitle": "更新项目: 春节营销活动",
        "changeDetails": {
          "action": "update_project",
          "updateData": {...}
        },
        "beforeData": {...},
        "afterData": {...},
        "changedFields": ["projectName", "budget"],
        "operatorId": "user-001",
        "operatorName": "张三",
        "operatorIP": "*************",
        "userAgent": "Mozilla/5.0...",
        "reason": "客户需求变更",
        "description": "更新了项目名称和预算",
        "projectId": "project-001",
        "project": {
          "id": "project-001",
          "projectName": "春节营销活动",
          "status": "active",
          "brand": {
            "id": "brand-001",
            "name": "可口可乐"
          }
        },
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  },
  "message": "获取变更记录列表成功"
}
```

### 2. 获取项目变更记录
```http
GET /api/projects/{projectId}/change-logs
```

**路径参数:**
- `projectId`: 项目ID

**查询参数:**
- `page`: 页码（默认1）
- `pageSize`: 每页数量（默认50）
- `sortOrder`: 排序方向（默认desc）

## 使用示例

### 1. 查询项目的所有变更记录
```javascript
// 获取项目ID为project-001的所有变更记录
const response = await fetch('/api/projects/project-001/change-logs?page=1&pageSize=20');
const data = await response.json();
```

### 2. 查询特定用户的操作记录
```javascript
// 获取用户user-001的所有操作记录
const response = await fetch('/api/change-logs?operatorId=user-001&page=1&pageSize=20');
const data = await response.json();
```

### 3. 查询特定时间范围的变更记录
```javascript
// 获取2024年1月的所有变更记录
const response = await fetch('/api/change-logs?startDate=2024-01-01&endDate=2024-01-31');
const data = await response.json();
```

## 部署说明

### 1. 数据库迁移
执行以下SQL脚本创建变更记录表：
```bash
psql -d your_database -f scripts/add-project-change-log-table.sql
```

### 2. Prisma客户端更新
```bash
npx prisma generate
```

### 3. 重启应用
重启应用以加载新的路由和功能。

## 注意事项

1. **性能考虑**: 变更记录表会随时间增长，建议定期归档历史数据
2. **存储空间**: JSON字段可能占用较多存储空间，建议监控数据库大小
3. **权限控制**: 变更记录查询需要JWT认证，确保只有授权用户可以访问
4. **数据隐私**: 变更记录包含敏感信息，注意数据保护

## 扩展功能

### 1. 数据归档
可以实现定期归档功能，将历史变更记录移动到归档表：
```sql
-- 创建归档表
CREATE TABLE project_change_logs_archive (LIKE project_change_logs);

-- 归档6个月前的记录
INSERT INTO project_change_logs_archive 
SELECT * FROM project_change_logs 
WHERE created_at < NOW() - INTERVAL '6 months';
```

### 2. 变更统计
可以添加变更统计功能，分析项目变更频率和模式：
```sql
-- 按月统计变更次数
SELECT 
  DATE_TRUNC('month', created_at) as month,
  change_type,
  COUNT(*) as change_count
FROM project_change_logs 
GROUP BY month, change_type
ORDER BY month DESC;
```

### 3. 变更通知
可以集成钉钉通知，在重要变更时发送通知给相关人员。
