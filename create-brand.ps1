$headers = @{
    'Content-Type' = 'application/json'
}

$body = @{
    name = '可口可乐'
    description = '可口可乐品牌描述'
    logo = 'https://example.com/logos/可口可乐.png'
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:3000/api/brands' -Method Post -Headers $headers -Body $body
    Write-Host "Brand created successfully!"
    Write-Host "Brand ID: $($response.data.id)"
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Brand creation failed: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error details: $responseBody"
    }
}
