
/**
 * 测试用户同步功能，特别是部门嵌套问题
 */
async function testUserSync() {
  // console.log('🧪 开始测试用户同步功能...\n');

  // const databaseService = new DatabaseService();
  // const dingTalkService = new DingTalkService();
  // const userSyncService = new UserSyncService(databaseService, dingTalkService);
  // let allDepartments;
  // try {
  //   // 1. 测试获取所有部门（包括嵌套部门）
  //   console.log('📋 测试1: 获取所有部门（包括嵌套部门）');
  //   allDepartments = await dingTalkService.getAllDepartments();
  //   console.log(`✅ 递归获取到 ${allDepartments.length} 个部门`);
      
  //   if (allDepartments.length > 0) {
  //     console.log('📊 部门列表预览:');
  //     allDepartments.slice(0, 5).forEach(dept => {
  //       console.log(`  - ${dept.name} (ID: ${dept.dept_id}, 父部门: ${dept.parent_id})`);
  //     });
  //     if (allDepartments.length > 5) {
  //       console.log(`  ... 还有 ${allDepartments.length - 5} 个部门`);
  //     }
  //   }

  //   console.log('\n' + '='.repeat(50) + '\n');

  //   // 2. 测试获取所有用户（使用优化后的方法）
  //   console.log('👥 测试2: 获取所有用户（使用优化后的方法）');
  //   let cursor = 0;
  //   const size = 100;
  //   let totalUsers = 0;
  //   let hasMore = true;

  //   while (hasMore) {
  //     const userListResult = await dingTalkService.getAllUsers(cursor, size);
      
  //     if (userListResult?.list) {
  //       totalUsers += userListResult.list.length;
  //       console.log(`📄 第 ${Math.floor(cursor / size) + 1} 页: 获取到 ${userListResult.list.length} 个用户`);
        
  //       // 显示前几个用户的信息
  //       if (cursor === 0 && userListResult.list.length > 0) {
  //         console.log('👤 用户列表预览:');
  //         userListResult.list.slice(0, 3).forEach(user => {
  //           console.log(`  - ${user.name} (${user.userid})`);
  //         });
  //       }
  //     }

  //     hasMore = userListResult?.has_more || false;
  //     cursor = userListResult?.next_cursor || 0;
      
  //     if (!hasMore || totalUsers >= 500) { // 限制测试数量
  //       break;
  //     }
  //   }

  //   console.log(`✅ 总共获取到 ${totalUsers} 个用户`);

  //   console.log('\n' + '='.repeat(50) + '\n');

  //   // 3. 测试同步少量用户
  //   console.log('🔄 测试3: 同步少量用户到数据库');
    
  //   // 获取前10个用户进行测试
  //   const testUserResult = await dingTalkService.getAllUsers(0, 10);
  //   if (testUserResult?.list && testUserResult.list.length > 0) {
  //     const testUserIds = testUserResult.list.map(user => user.userid);
  //     console.log(`📝 准备同步 ${testUserIds.length} 个用户: ${testUserIds.join(', ')}`);
      
  //     const syncResult = await userSyncService.syncUsersByIds(testUserIds);
  //     console.log(`✅ 同步结果: 成功 ${syncResult.success}, 失败 ${syncResult.failed}`);
      
  //     if (syncResult.errors.length > 0) {
  //       console.log('❌ 同步错误:');
  //       syncResult.errors.forEach(error => {
  //         console.log(`  - ${error.userid}: ${error.error}`);
  //       });
  //     }
  //   } else {
  //     console.log('⚠️  未获取到测试用户');
  //   }

  //   console.log('\n' + '='.repeat(50) + '\n');

  //   // 4. 测试部门用户获取
  //   console.log('🏢 测试4: 测试特定部门的用户获取');
  //   if (allDepartments.length > 0) {
  //     // 选择一个部门进行测试
  //     const testDept = allDepartments[0];
  //     console.log(`📋 测试部门: ${testDept.name} (ID: ${testDept.dept_id})`);
      
  //     const deptUsers = await dingTalkService.getDepartmentUsers(testDept.dept_id, 0, 50);
  //     if (deptUsers?.list) {
  //       console.log(`✅ 部门 ${testDept.name} 有 ${deptUsers.list.length} 个用户`);
  //       if (deptUsers.list.length > 0) {
  //         console.log('👤 部门用户预览:');
  //         deptUsers.list.slice(0, 3).forEach(user => {
  //           console.log(`  - ${user.name} (${user.userid})`);
  //         });
  //       }
  //     } else {
  //       console.log(`⚠️  部门 ${testDept.name} 没有用户或获取失败`);
  //     }
  //   }

  //   console.log('\n🎉 用户同步功能测试完成！');

  // } catch (error) {
  //   console.error('❌ 测试过程中发生错误:', error);
  //   throw error;
  // } finally {
  //   await databaseService.client.$disconnect();
  // }
}

// 如果直接运行此脚本
if (process.argv[1] && import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'))) {
  testUserSync()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}
