#!/usr/bin/env node

/**
 * 部门角色管理功能测试脚本
 * 
 * 使用方法：
 * node scripts/test-department-roles.js
 * 
 * 需要先启动服务并获取有效的JWT token
 */

import fetch from 'node-fetch';

// 配置
const BASE_URL = 'http://localhost:3000/api';
const JWT_TOKEN = 'your-jwt-token-here'; // 请替换为实际的JWT token

// 测试数据
const TEST_DEPT_ID = 1; // 请替换为实际的部门ID
const TEST_ROLE_IDS = ['role_001', 'role_002']; // 请替换为实际的角色ID

// HTTP请求封装
async function apiRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    headers: {
      'Authorization': `Bearer ${JWT_TOKEN}`,
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  };

  console.log(`\n🔄 ${config.method || 'GET'} ${endpoint}`);
  
  try {
    const response = await fetch(url, config);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 成功:', data.message);
      return data;
    } else {
      console.log('❌ 失败:', data.message);
      return null;
    }
  } catch (error) {
    console.log('❌ 请求错误:', error.message);
    return null;
  }
}

// 测试函数
async function testDepartmentRoleManagement() {
  console.log('🚀 开始测试部门角色管理功能\n');

  // 1. 获取部门角色列表（初始状态）
  console.log('📋 1. 获取部门角色列表（初始状态）');
  const initialRoles = await apiRequest(`/departments/${TEST_DEPT_ID}/roles`);
  if (initialRoles) {
    console.log(`   部门 ${TEST_DEPT_ID} 当前有 ${initialRoles.data.total} 个角色`);
  }

  // 2. 为部门分配角色
  console.log('\n🎯 2. 为部门分配角色');
  const assignResult = await apiRequest(`/departments/${TEST_DEPT_ID}/roles`, {
    method: 'POST',
    body: JSON.stringify({
      roleIds: TEST_ROLE_IDS,
      expiresAt: null // 永不过期
    })
  });

  // 3. 再次获取部门角色列表（验证分配结果）
  console.log('\n📋 3. 验证角色分配结果');
  const updatedRoles = await apiRequest(`/departments/${TEST_DEPT_ID}/roles`);
  if (updatedRoles) {
    console.log(`   部门 ${TEST_DEPT_ID} 现在有 ${updatedRoles.data.total} 个角色`);
    updatedRoles.data.roles.forEach(role => {
      console.log(`   - ${role.displayName} (${role.name})`);
    });
  }

  // 4. 测试角色视角：获取角色关联的部门
  if (TEST_ROLE_IDS.length > 0) {
    console.log('\n🏢 4. 从角色视角查看关联的部门');
    const roleDepts = await apiRequest(`/roles/${TEST_ROLE_IDS[0]}/departments`);
    if (roleDepts) {
      console.log(`   角色 ${TEST_ROLE_IDS[0]} 关联了 ${roleDepts.data.total} 个部门`);
      roleDepts.data.departments.forEach(dept => {
        console.log(`   - ${dept.name} (ID: ${dept.deptId})`);
      });
    }
  }

  // 5. 测试角色视角：获取角色关联的用户
  if (TEST_ROLE_IDS.length > 0) {
    console.log('\n👥 5. 从角色视角查看关联的用户');
    const roleUsers = await apiRequest(`/roles/${TEST_ROLE_IDS[0]}/users?includeInherited=true`);
    if (roleUsers) {
      console.log(`   角色 ${TEST_ROLE_IDS[0]} 关联了 ${roleUsers.data.total} 个用户`);
      console.log(`   - 直接分配: ${roleUsers.data.directUsers.length} 个用户`);
      console.log(`   - 部门继承: ${roleUsers.data.inheritedUsers.length} 个用户`);
    }
  }

  // 6. 移除部门的特定角色
  if (TEST_ROLE_IDS.length > 1) {
    console.log('\n🗑️ 6. 移除部门的特定角色');
    const removeResult = await apiRequest(`/departments/${TEST_DEPT_ID}/roles/${TEST_ROLE_IDS[1]}`, {
      method: 'DELETE'
    });
  }

  // 7. 验证移除结果
  console.log('\n📋 7. 验证角色移除结果');
  const finalRoles = await apiRequest(`/departments/${TEST_DEPT_ID}/roles`);
  if (finalRoles) {
    console.log(`   部门 ${TEST_DEPT_ID} 最终有 ${finalRoles.data.total} 个角色`);
    finalRoles.data.roles.forEach(role => {
      console.log(`   - ${role.displayName} (${role.name})`);
    });
  }

  // 8. 清空部门所有角色（可选，谨慎使用）
  console.log('\n🧹 8. 清空部门所有角色（演示功能，实际使用请谨慎）');
  const clearResult = await apiRequest(`/departments/${TEST_DEPT_ID}/roles`, {
    method: 'DELETE'
  });

  // 9. 最终验证
  console.log('\n📋 9. 最终验证');
  const emptyRoles = await apiRequest(`/departments/${TEST_DEPT_ID}/roles`);
  if (emptyRoles) {
    console.log(`   部门 ${TEST_DEPT_ID} 现在有 ${emptyRoles.data.total} 个角色`);
  }

  console.log('\n✨ 部门角色管理功能测试完成！');
}

// 使用说明
function showUsage() {
  console.log(`
📖 使用说明：

1. 确保服务已启动（通常在 http://localhost:3000）
2. 修改脚本中的配置：
   - JWT_TOKEN: 有效的JWT认证token
   - TEST_DEPT_ID: 测试用的部门ID
   - TEST_ROLE_IDS: 测试用的角色ID列表

3. 运行测试：
   node scripts/test-department-roles.js

⚠️ 注意：
- 此脚本会修改数据库中的部门角色关联
- 请在测试环境中运行
- 确保有足够的权限执行角色管理操作
`);
}

// 主函数
async function main() {
  if (JWT_TOKEN === 'your-jwt-token-here') {
    console.log('❌ 请先配置有效的JWT token');
    showUsage();
    return;
  }

  try {
    await testDepartmentRoleManagement();
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { testDepartmentRoleManagement };
