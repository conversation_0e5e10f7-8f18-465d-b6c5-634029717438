import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { DingTalkService } from '../services/dingtalk.js';
import { jwtService } from '../services/jwt.js';

// 请求参数验证模式
const authCodeSchema = z.object({
  authCode: z.string().min(1, '免登码不能为空'),
});

const jsapiSignatureSchema = z.object({
  url: z.string().url('URL格式不正确'),
});

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, '刷新令牌不能为空'),
});

const userListSchema = z.object({
  deptId: z.number().optional(),
  cursor: z.number().min(0).default(0),
  size: z.number().min(1).max(100).default(20),
});

export class AuthController {
  private dingTalkService: DingTalkService;

  constructor() {
    this.dingTalkService = new DingTalkService();
  }

  /**
   * 通过免登码获取用户信息并生成JWT token
   */
  async getUserInfoByCode(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { authCode } = authCodeSchema.parse(request.body);

      // 获取用户ID
      const userid = await this.dingTalkService.getUserInfoByCode(authCode);

      // 获取用户详细信息
      const userInfo = await this.dingTalkService.getUserDetail(userid);

      if (!userInfo) {
        return reply.status(500).send({
          success: false,
          message: '获取用户信息失败',
        });
      }

      // 生成JWT token和用户会话
      const authResponse = await jwtService.generateAuthResponse({
        userid: userInfo.userid,
        name: userInfo.name,
        mobile: userInfo.mobile || '',
        deptIds: userInfo.dept_id_list || [],
        isAdmin: userInfo.admin || false,
        isBoss: userInfo.boss || false
      });

      // 记录登录日志
      request.log.info({
        userid: userInfo.userid,
        name: userInfo.name,
        action: 'dingtalk_login_success'
      }, '用户登录成功');

      return reply.send({
        success: true,
        data: authResponse,
        message: '登录成功',
      });
    } catch (error) {
      console.error('用户登录失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors,
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '登录失败',
      });
    }
  }

  /**
   * 获取JSAPI签名
   */
  async getJSAPISignature(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { url } = jsapiSignatureSchema.parse(request.query);

      const signature = await this.dingTalkService.generateJSAPISignature(url);

      return reply.send({
        success: true,
        data: signature,
        message: '获取JSAPI签名成功',
      });
    } catch (error) {
      console.error('获取JSAPI签名失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors,
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取JSAPI签名失败',
      });
    }
  }

  /**
   * 获取部门列表
   */
  async getDepartmentList(request: FastifyRequest, reply: FastifyReply) {
    try {
      const departments = await this.dingTalkService.getAllDepartments();

      return reply.send({
        success: true,
        data: departments,
        message: '获取部门列表成功',
      });
    } catch (error) {
      console.error('获取部门列表失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取部门列表失败',
      });
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { refreshToken } = refreshTokenSchema.parse(request.body);

      const result = await jwtService.refreshAccessToken(refreshToken);

      if (!result) {
        return reply.status(401).send({
          success: false,
          message: '刷新令牌无效或已过期',
          code: 'INVALID_REFRESH_TOKEN'
        });
      }

      return reply.send({
        success: true,
        data: result,
        message: '令牌刷新成功',
      });
    } catch (error) {
      console.error('刷新令牌失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors,
        });
      }

      return reply.status(500).send({
        success: false,
        message: '刷新令牌失败',
      });
    }
  }

  /**
   * 用户登出
   */
  async logout(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 从请求头获取token
      const authorization = request.headers.authorization;
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return reply.status(400).send({
          success: false,
          message: '缺少访问令牌',
          code: 'MISSING_TOKEN'
        });
      }

      const token = authorization.substring(7);
      const payload = await jwtService.verifyAccessToken(token);

      if (payload) {
        // 删除用户会话和令牌
        await jwtService.logout(payload.userid, token);

        request.log.info({
          userid: payload.userid,
          name: payload.name,
          action: 'user_logout'
        }, '用户登出');
      }

      return reply.send({
        success: true,
        message: '登出成功',
      });
    } catch (error) {
      console.error('用户登出失败:', error);
      return reply.status(500).send({
        success: false,
        message: '登出失败',
      });
    }
  }

  /**
   * 验证token并获取当前用户信息
   */
  async getCurrentUser(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 从请求头获取token
      const authorization = request.headers.authorization;
      if (!authorization || !authorization.startsWith('Bearer ')) {
        return reply.status(401).send({
          success: false,
          message: '缺少访问令牌',
          code: 'MISSING_TOKEN'
        });
      }

      const token = authorization.substring(7);
      const payload = await jwtService.verifyAccessToken(token);

      if (!payload) {
        return reply.status(401).send({
          success: false,
          message: '访问令牌无效或已过期',
          code: 'INVALID_TOKEN'
        });
      }

      // 获取用户会话信息
      const session = await jwtService.getUserSession(payload.userid);

      return reply.send({
        success: true,
        data: {
          user: {
            userid: payload.userid,
            name: payload.name,
            mobile: payload.mobile,
            deptIds: payload.deptIds,
            isAdmin: payload.isAdmin,
            isBoss: payload.isBoss
          },
          session: session ? {
            loginTime: session.loginTime,
            lastActiveTime: session.lastActiveTime
          } : null
        },
        message: '获取用户信息成功',
      });
    } catch (error) {
      console.error('获取当前用户信息失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取用户信息失败',
      });
    }
  }

  /**
   * 获取会话统计信息（管理员功能）
   */
  async getSessionStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 这里应该添加管理员权限检查
      const stats = await jwtService.getActiveSessionsStats();

      return reply.send({
        success: true,
        data: stats,
        message: '获取会话统计成功',
      });
    } catch (error) {
      console.error('获取会话统计失败:', error);
      return reply.status(500).send({
        success: false,
        message: '获取会话统计失败',
      });
    }
  }

  /**
   * 获取用户列表
   */
  async getUserList(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { deptId, cursor, size } = userListSchema.parse(request.query);

      let userListResult;

      if (deptId) {
        // 获取指定部门的用户
        userListResult = await this.dingTalkService.getDepartmentUsers(deptId, cursor, size);
      } else {
        // 获取所有用户（当没有设置部门时）
        userListResult = await this.dingTalkService.getAllUsers(cursor, size);
      }

      if (!userListResult) {
        return reply.status(500).send({
          success: false,
          message: '获取用户列表失败',
        });
      }

      return reply.send({
        success: true,
        data: {
          users: userListResult.list || [],
          pagination: {
            cursor,
            size,
            hasMore: userListResult.has_more || false,
            nextCursor: userListResult.next_cursor || 0,
          },
        },
        message: deptId ? `获取部门 ${deptId} 用户列表成功` : '获取所有用户列表成功',
      });
    } catch (error) {
      console.error('获取用户列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors,
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取用户列表失败',
      });
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(request: FastifyRequest, reply: FastifyReply) {
    return reply.send({
      success: true,
      message: '服务运行正常',
      timestamp: new Date().toISOString(),
    });
  }
}
