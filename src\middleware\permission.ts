import { FastifyReply, FastifyRequest } from 'fastify';
import { DatabaseService } from '../services/database.js';
import { UserPermissionService } from '../services/userPermissionService.js';

// 权限中间件选项
export interface PermissionMiddlewareOptions {
  permissions: string | string[]; // 需要的权限
  requireAll?: boolean; // 是否需要所有权限（默认false，只需要其中一个）
  allowAdmin?: boolean; // 是否允许管理员绕过权限检查（默认true）
  allowBoss?: boolean; // 是否允许老板绕过权限检查（默认true）
}

// 使用已定义的AuthenticatedUser类型

// 创建权限中间件实例
let userPermissionService: UserPermissionService;

// 初始化权限服务
export function initializePermissionMiddleware(databaseService: DatabaseService) {
  userPermissionService = new UserPermissionService(databaseService);
}

/**
 * 权限验证中间件工厂函数
 */
export function requirePermissions(options: PermissionMiddlewareOptions) {
  return async function permissionMiddleware(
    request: FastifyRequest,
    reply: FastifyReply
  ) {
    try {
      // 检查用户是否已认证
      if (!request.user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证',
          code: 'UNAUTHORIZED'
        });
      }

      const { userid, isAdmin, isBoss } = request.user;
      const {
        permissions,
        requireAll = false,
        allowAdmin = true,
        allowBoss = true
      } = options;

      // 管理员和老板绕过权限检查
      if ((allowAdmin && isAdmin) || (allowBoss && isBoss)) {
        return;
      }

      // 确保权限服务已初始化
      if (!userPermissionService) {
        console.error('权限服务未初始化');
        return reply.status(500).send({
          success: false,
          message: '权限服务未初始化',
          code: 'PERMISSION_SERVICE_NOT_INITIALIZED'
        });
      }

      // 标准化权限列表
      const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];

      if (requiredPermissions.length === 0) {
        // 没有指定权限要求，直接通过
        return;
      }

      // 检查用户权限
      const userPermissions = await userPermissionService.hasPermissions(userid, requiredPermissions);

      // 验证权限
      let hasAccess = false;

      if (requireAll) {
        // 需要所有权限
        hasAccess = requiredPermissions.every(permission => userPermissions[permission]);
      } else {
        // 只需要其中一个权限
        hasAccess = requiredPermissions.some(permission => userPermissions[permission]);
      }

      if (!hasAccess) {
        const missingPermissions = requiredPermissions.filter(permission => !userPermissions[permission]);
        
        return reply.status(403).send({
          success: false,
          message: '权限不足',
          code: 'INSUFFICIENT_PERMISSIONS',
          data: {
            requiredPermissions,
            missingPermissions,
            requireAll
          }
        });
      }

      // 权限验证通过，继续执行
    } catch (error) {
      console.error('权限验证失败:', error);
      return reply.status(500).send({
        success: false,
        message: '权限验证失败',
        code: 'PERMISSION_CHECK_ERROR'
      });
    }
  };
}

/**
 * 检查单个权限的中间件
 */
export function requirePermission(permission: string, options: Omit<PermissionMiddlewareOptions, 'permissions'> = {}) {
  return requirePermissions({
    ...options,
    permissions: permission
  });
}

/**
 * 检查多个权限（需要全部）的中间件
 */
export function requireAllPermissions(permissions: string[], options: Omit<PermissionMiddlewareOptions, 'permissions' | 'requireAll'> = {}) {
  return requirePermissions({
    ...options,
    permissions,
    requireAll: true
  });
}

/**
 * 检查多个权限（需要其中一个）的中间件
 */
export function requireAnyPermission(permissions: string[], options: Omit<PermissionMiddlewareOptions, 'permissions' | 'requireAll'> = {}) {
  return requirePermissions({
    ...options,
    permissions,
    requireAll: false
  });
}

/**
 * 仅管理员权限中间件
 */
export async function requireAdmin(
  request: FastifyRequest,
  reply: FastifyReply
) {
  if (!request.user) {
    return reply.status(401).send({
      success: false,
      message: '用户未认证',
      code: 'UNAUTHORIZED'
    });
  }

  if (!request.user.isAdmin) {
    return reply.status(403).send({
      success: false,
      message: '需要管理员权限',
      code: 'ADMIN_REQUIRED'
    });
  }
}

/**
 * 仅老板权限中间件
 */
export async function requireBoss(
  request: FastifyRequest,
  reply: FastifyReply
) {
  if (!request.user) {
    return reply.status(401).send({
      success: false,
      message: '用户未认证',
      code: 'UNAUTHORIZED'
    });
  }

  if (!request.user.isBoss) {
    return reply.status(403).send({
      success: false,
      message: '需要老板权限',
      code: 'BOSS_REQUIRED'
    });
  }
}

/**
 * 管理员或老板权限中间件
 */
export async function requireAdminOrBoss(
  request: FastifyRequest,
  reply: FastifyReply
) {
  if (!request.user) {
    return reply.status(401).send({
      success: false,
      message: '用户未认证',
      code: 'UNAUTHORIZED'
    });
  }

  if (!request.user.isAdmin && !request.user.isBoss) {
    return reply.status(403).send({
      success: false,
      message: '需要管理员或老板权限',
      code: 'ADMIN_OR_BOSS_REQUIRED'
    });
  }
}

/**
 * 检查用户是否可以访问指定用户的数据
 */
export function requireUserAccess(targetUserIdParam: string = 'userid') {
  return async function userAccessMiddleware(
    request: FastifyRequest,
    reply: FastifyReply
  ) {
    if (!request.user) {
      return reply.status(401).send({
        success: false,
        message: '用户未认证',
        code: 'UNAUTHORIZED'
      });
    }

    const { userid, isAdmin, isBoss } = request.user;
    
    // 管理员和老板可以访问任何用户数据
    if (isAdmin || isBoss) {
      return;
    }

    // 获取目标用户ID
    const params = request.params as any;
    const targetUserId = params[targetUserIdParam];

    if (!targetUserId) {
      return reply.status(400).send({
        success: false,
        message: '缺少用户ID参数',
        code: 'MISSING_USER_ID'
      });
    }

    // 用户只能访问自己的数据
    if (userid !== targetUserId) {
      return reply.status(403).send({
        success: false,
        message: '无权访问其他用户数据',
        code: 'ACCESS_DENIED'
      });
    }
  };
}

/**
 * 权限常量定义
 */
export const PERMISSIONS = {
  // 用户管理
  USER_READ: 'user.read',
  USER_CREATE: 'user.create',
  USER_UPDATE: 'user.update',
  USER_DELETE: 'user.delete',
  
  // 角色管理
  ROLE_READ: 'role.read',
  ROLE_CREATE: 'role.create',
  ROLE_UPDATE: 'role.update',
  ROLE_DELETE: 'role.delete',
  ROLE_ASSIGN: 'role.assign',
  
  // 权限管理
  PERMISSION_READ: 'permission.read',
  PERMISSION_CREATE: 'permission.create',
  PERMISSION_UPDATE: 'permission.update',
  PERMISSION_DELETE: 'permission.delete',
  
  // 项目管理
  PROJECT_READ: 'project.read',
  PROJECT_CREATE: 'project.create',
  PROJECT_UPDATE: 'project.update',
  PROJECT_DELETE: 'project.delete',
  PROJECT_APPROVE: 'project.approve',
  
  // 品牌管理
  BRAND_READ: 'brand.read',
  BRAND_CREATE: 'brand.create',
  BRAND_UPDATE: 'brand.update',
  BRAND_DELETE: 'brand.delete',
  
  // 财务管理
  FINANCE_READ: 'finance.read',
  FINANCE_CREATE: 'finance.create',
  FINANCE_UPDATE: 'finance.update',
  FINANCE_DELETE: 'finance.delete',
  FINANCE_APPROVE: 'finance.approve',
  
  // 系统管理
  SYSTEM_CONFIG: 'system.config',
  SYSTEM_LOG: 'system.log',
  SYSTEM_BACKUP: 'system.backup',
} as const;
