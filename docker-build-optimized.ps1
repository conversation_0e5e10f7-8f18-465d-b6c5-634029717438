# Docker 构建优化脚本 (PowerShell 版本)
# 使用 BuildKit 和多种优化技术来加速构建

param(
    [string]$Tag = "latest",
    [switch]$NoCache,
    [string]$Registry = $env:REGISTRY
)

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

# 配置
$ImageName = "cantv-ding-backend"

Write-ColorOutput Blue "🚀 开始优化构建 Docker 镜像..."

# 启用 Docker BuildKit（提升构建性能）
$env:DOCKER_BUILDKIT = "1"
$env:BUILDKIT_PROGRESS = "plain"

# 检查 Docker 版本
Write-ColorOutput Yellow "📋 检查 Docker 环境..."
docker --version
try {
    docker buildx version
    $BuildXAvailable = $true
} catch {
    Write-ColorOutput Yellow "BuildX 不可用，使用标准构建"
    $BuildXAvailable = $false
}

# 清理构建缓存（可选）
if ($NoCache) {
    Write-ColorOutput Yellow "🧹 清理构建缓存..."
    docker builder prune -f
}

# 构建镜像
Write-ColorOutput Blue "🔨 构建镜像: ${ImageName}:${Tag}"

# 使用 BuildKit 的高级特性
if ($BuildXAvailable) {
    Write-ColorOutput Green "✅ 使用 BuildX 进行优化构建"
    
    # 创建并使用 buildx 实例（如果不存在）
    try {
        docker buildx create --name mybuilder --use 2>$null
    } catch {
        try {
            docker buildx use mybuilder 2>$null
        } catch {
            # 忽略错误，继续使用默认构建器
        }
    }
    
    # 创建缓存目录
    $CacheDir = "$env:TEMP\.buildx-cache"
    $CacheDirNew = "$env:TEMP\.buildx-cache-new"
    
    if (!(Test-Path $CacheDir)) {
        New-Item -ItemType Directory -Path $CacheDir -Force | Out-Null
    }
    
    # 使用 BuildX 构建（支持并行构建和高级缓存）
    docker buildx build `
        --platform linux/amd64 `
        --cache-from "type=local,src=$CacheDir" `
        --cache-to "type=local,dest=$CacheDirNew,mode=max" `
        --tag "${ImageName}:${Tag}" `
        --load `
        .
    
    # 更新缓存
    if (Test-Path $CacheDir) {
        Remove-Item -Recurse -Force $CacheDir
    }
    if (Test-Path $CacheDirNew) {
        Rename-Item $CacheDirNew $CacheDir
    }
    
} else {
    Write-ColorOutput Yellow "⚠️  使用标准 Docker 构建"
    
    # 标准构建（带缓存优化）
    docker build `
        --tag "${ImageName}:${Tag}" `
        --build-arg BUILDKIT_INLINE_CACHE=1 `
        .
}

# 检查构建是否成功
if ($LASTEXITCODE -eq 0) {
    Write-ColorOutput Green "✅ 构建成功！"
} else {
    Write-ColorOutput Red "❌ 构建失败！"
    exit $LASTEXITCODE
}

# 检查镜像大小
Write-ColorOutput Blue "📊 镜像信息:"
docker images "${ImageName}:${Tag}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 如果指定了注册表，推送镜像
if ($Registry) {
    Write-ColorOutput Blue "📤 推送镜像到注册表..."
    docker tag "${ImageName}:${Tag}" "${Registry}/${ImageName}:${Tag}"
    docker push "${Registry}/${ImageName}:${Tag}"
    Write-ColorOutput Green "✅ 镜像已推送到: ${Registry}/${ImageName}:${Tag}"
}

# 清理悬空镜像
Write-ColorOutput Yellow "🧹 清理悬空镜像..."
docker image prune -f

Write-ColorOutput Green "🎉 构建完成！"
Write-ColorOutput Blue "📝 使用方法:"
Write-ColorOutput Yellow "  运行容器: docker run -d -p 3000:3000 --name cantv-ding ${ImageName}:${Tag}"
Write-ColorOutput Yellow "  查看日志: docker logs -f cantv-ding"
Write-ColorOutput Yellow "  停止容器: docker stop cantv-ding"

# 性能提示
Write-ColorOutput Blue "💡 性能优化提示:"
Write-ColorOutput Yellow "  1. 使用 -NoCache 参数进行完全重新构建"
Write-ColorOutput Yellow "  2. 设置 REGISTRY 环境变量自动推送镜像"
Write-ColorOutput Yellow "  3. 确保 Docker 有足够的内存和 CPU 资源"
Write-ColorOutput Yellow "  4. 使用 SSD 存储可显著提升构建速度"

# 显示使用示例
Write-ColorOutput Blue "📖 PowerShell 使用示例:"
Write-ColorOutput Yellow "  .\docker-build-optimized.ps1"
Write-ColorOutput Yellow "  .\docker-build-optimized.ps1 -Tag v1.0.0"
Write-ColorOutput Yellow "  .\docker-build-optimized.ps1 -NoCache"
Write-ColorOutput Yellow "  `$env:REGISTRY='your-registry.com'; .\docker-build-optimized.ps1"
