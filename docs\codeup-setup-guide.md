# 阿里云云效配置快速设置指南

## 1. 创建云效项目

1. 登录 [阿里云云效](https://codeup.aliyun.com/)
2. 创建新项目或使用现有项目
3. 将代码推送到云效代码仓库

## 2. 配置容器镜像服务

### 创建镜像仓库
1. 登录 [阿里云容器镜像服务](https://cr.console.aliyun.com/)
2. 创建命名空间：`cantv-ding`
3. 创建镜像仓库：`cantv-ding-backend`
4. 设置仓库为私有

### 获取访问凭证
```bash
# 在容器镜像服务控制台获取
DOCKER_REGISTRY=registry.cn-hangzhou.aliyuncs.com
DOCKER_USERNAME=your-username
DOCKER_PASSWORD=your-password
```

## 3. 准备服务器

### 创建 ECS 实例
1. 登录 [阿里云ECS控制台](https://ecs.console.aliyun.com/)
2. 创建ECS实例（推荐配置：2核4G，系统盘40G）
3. 配置安全组，开放必要端口（22, 80, 443, 3000）
4. 记录服务器的公网IP地址

### 服务器初始化
```bash
# 连接到服务器
ssh root@your-server-ip

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装 Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 创建部署目录
mkdir -p /opt/cantv-ding
```

## 4. 配置云效环境变量

在云效项目的 **设置 > 流水线 > 变量** 中添加以下环境变量：

### 必需变量
```bash
# Docker Registry
DOCKER_REGISTRY=registry.cn-hangzhou.aliyuncs.com
DOCKER_USERNAME=your-aliyun-username
DOCKER_PASSWORD=your-aliyun-password

# 服务器信息
DEV_SERVER_IP=your-dev-server-ip
PROD_SERVER_IP=your-prod-server-ip
SSH_PRIVATE_KEY=your-ssh-private-key-content

# 应用配置
DATABASE_URL=postgresql://user:password@host:port/database
DATABASE_URL_DEV=postgresql://user:password@dev-host:port/dev-database
DINGTALK_APP_KEY=your-dingtalk-app-key
DINGTALK_APP_SECRET=your-dingtalk-app-secret
DINGTALK_CORP_ID=your-dingtalk-corp-id
DINGTALK_AGENT_ID=your-dingtalk-agent-id
JWT_SECRET=your-jwt-secret-key
```

### 可选变量
```bash
# 钉钉通知
DINGTALK_WEBHOOK=https://oapi.dingtalk.com/robot/send?access_token=xxx

# 自定义配置
NODE_ENV=production
LOG_LEVEL=info
```

## 5. 创建流水线

### 方式一：使用配置文件
1. 将 `.flow.yml` 文件推送到代码仓库根目录
2. 在云效中创建流水线，选择"使用代码库中的配置文件"
3. 指定配置文件路径：`.flow.yml`

### 方式二：可视化配置
1. 在云效中创建新流水线
2. 按照以下阶段配置：

#### 阶段1：代码检查
```yaml
- 检出代码
- 安装 Node.js 20
- 安装 pnpm
- 安装依赖：pnpm install
- 构建项目：pnpm run build
```

#### 阶段2：运行测试
```yaml
- 运行 API 测试：pnpm run test:api
- 生成测试报告
```

#### 阶段3：构建镜像
```yaml
- 构建 Docker 镜像
- 推送到镜像仓库
- 镜像标签：${PIPELINE_ID}
```

#### 阶段4：部署应用
```yaml
- 部署到开发环境（develop 分支）
- 部署到生产环境（main 分支）
- 执行健康检查
```

## 6. 配置触发器

### 代码推送触发
```yaml
triggers:
  push:
    branches:
      - main      # 生产环境
      - develop   # 开发环境
      - feature/* # 功能分支（仅构建测试）
```

### 定时触发（可选）
```yaml
triggers:
  schedule:
    - cron: "0 2 * * *"  # 每天凌晨2点
      branches: [main]
```

## 7. 配置部署环境

### 开发环境
```bash
# 创建命名空间
kubectl create namespace cantv-ding-dev

# 创建密钥
kubectl create secret generic cantv-ding-secrets-dev \
  --from-literal=database-url="$DATABASE_URL_DEV" \
  --from-literal=dingtalk-app-key="$DINGTALK_APP_KEY" \
  --from-literal=dingtalk-app-secret="$DINGTALK_APP_SECRET" \
  --from-literal=dingtalk-corp-id="$DINGTALK_CORP_ID" \
  --from-literal=dingtalk-agent-id="$DINGTALK_AGENT_ID" \
  --from-literal=jwt-secret="$JWT_SECRET" \
  --namespace=cantv-ding-dev
```

### 生产环境
```bash
# 创建命名空间
kubectl create namespace cantv-ding-prod

# 创建密钥
kubectl create secret generic cantv-ding-secrets \
  --from-literal=database-url="$DATABASE_URL_PROD" \
  --from-literal=dingtalk-app-key="$DINGTALK_APP_KEY" \
  --from-literal=dingtalk-app-secret="$DINGTALK_APP_SECRET" \
  --from-literal=dingtalk-corp-id="$DINGTALK_CORP_ID" \
  --from-literal=dingtalk-agent-id="$DINGTALK_AGENT_ID" \
  --from-literal=jwt-secret="$JWT_SECRET" \
  --namespace=cantv-ding-prod
```

## 8. 配置域名和证书

### 配置 Ingress
```bash
# 确保 Nginx Ingress Controller 已安装
kubectl get pods -n ingress-nginx

# 配置域名解析
# dev-api.cantv-ding.com -> 开发环境 LoadBalancer IP
# api.cantv-ding.com -> 生产环境 LoadBalancer IP
```

### 配置 SSL 证书（生产环境）
```bash
# 安装 cert-manager
kubectl apply -f https://github.com/jetstack/cert-manager/releases/download/v1.8.0/cert-manager.yaml

# 创建 ClusterIssuer
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

## 9. 测试流水线

### 触发首次构建
```bash
# 推送代码到 develop 分支
git checkout develop
git push origin develop

# 推送代码到 main 分支
git checkout main
git push origin main
```

### 验证部署
```bash
# 检查开发环境
curl https://dev-api.cantv-ding.com/api/health

# 检查生产环境
curl https://api.cantv-ding.com/api/health
```

## 10. 监控和告警

### 配置钉钉通知
1. 创建钉钉群机器人
2. 获取 Webhook 地址
3. 在云效环境变量中设置 `DINGTALK_WEBHOOK`

### 配置监控（可选）
```bash
# 安装 Prometheus 和 Grafana
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack
```

## 常见问题

### 1. 镜像拉取失败
```bash
# 检查镜像拉取密钥
kubectl get secret aliyun-registry-secret -n cantv-ding-prod -o yaml
```

### 2. 部署超时
```bash
# 增加超时时间
kubectl patch deployment cantv-ding-backend -p '{"spec":{"progressDeadlineSeconds":600}}'
```

### 3. 健康检查失败
```bash
# 检查应用日志
kubectl logs -f deployment/cantv-ding-backend -n cantv-ding-prod
```

## 下一步

1. 配置自动扩缩容（HPA）
2. 设置资源监控和告警
3. 配置备份和恢复策略
4. 实施安全扫描和合规检查

## 支持

如需帮助，请联系：
- 邮箱：<EMAIL>
- 文档：[部署指南](./deployment-guide.md)
