import { FastifyInstance } from 'fastify';
import { env } from '../config/env.js';
import { ProjectController } from '../controllers/project.js';
import { RevenueController } from '../controllers/revenue.js';

export async function testRoutes(fastify: FastifyInstance) {
  // 只在开发环境下启用测试路由
  if (env.NODE_ENV !== 'development') {
    return;
  }

  const projectController = new ProjectController();
  const revenueController = new RevenueController();

  // 无认证的项目列表接口（仅用于测试）
  fastify.get('/test/projects', {
    schema: {
      description: '获取项目列表（测试用，无认证）',
      tags: ['Test'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          documentType: { type: 'string', description: '单据类型' },
          brandId: { type: 'string', description: '品牌ID' },
          contractType: { type: 'string', description: '合同类型' },
          executorPM: { type: 'string', description: '执行PM' },
          status: { type: 'string', description: '项目状态' },
          keyword: { type: 'string', description: '项目名称关键词' },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          sortBy: { type: 'string', description: '排序字段' },
          sortOrder: { type: 'string', description: '排序方向' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.getProjects.bind(projectController));

  // 无认证的单个项目接口（仅用于测试）
  fastify.get('/test/projects/:id', {
    schema: {
      description: '获取单个项目（测试用，无认证）',
      tags: ['Test'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '项目ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.getProject.bind(projectController));

  // 无认证的品牌列表接口（仅用于测试）
  fastify.get('/test/brands', {
    schema: {
      description: '获取品牌列表（测试用，无认证）',
      tags: ['Test'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          status: { type: 'string', description: '品牌状态' },
          keyword: { type: 'string', description: '品牌名称关键词' },
          sortBy: { type: 'string', description: '排序字段' },
          sortOrder: { type: 'string', description: '排序方向' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, projectController.getBrands.bind(projectController));

  // 创建品牌（测试用）
  fastify.post('/test/brands', projectController.createBrand.bind(projectController));

  // 创建项目（测试用）
  fastify.post('/test/projects', projectController.createProject.bind(projectController));

  // 收入管理测试接口

  // 创建项目收入（测试用）
  fastify.post('/test/projects/:projectId/revenues', revenueController.createRevenue.bind(revenueController));

  // 获取收入列表（测试用）
  fastify.get('/test/revenues', revenueController.getRevenues.bind(revenueController));

  // 获取单个收入（测试用）
  fastify.get('/test/revenues/:id', revenueController.getRevenue.bind(revenueController));

  // 更新收入（测试用）
  fastify.put('/test/revenues/:id', revenueController.updateRevenue.bind(revenueController));

  // 删除收入（测试用）
  fastify.delete('/test/revenues/:id', revenueController.deleteRevenue.bind(revenueController));

  // 获取收入统计（测试用）
  fastify.get('/test/revenues/stats', revenueController.getRevenueStats.bind(revenueController));

  // 确认收入（测试用）
  fastify.put('/test/revenues/:id/confirm', revenueController.confirmRevenue.bind(revenueController));

  // 批量确认收入（测试用）
  fastify.put('/test/revenues/batch-confirm', revenueController.batchConfirmRevenues.bind(revenueController));

  // 供应商管理测试接口
  const { SupplierController } = await import('../controllers/supplier.js');
  const supplierController = new SupplierController();

  // 创建供应商（测试用）
  fastify.post('/test/suppliers', supplierController.createSupplier.bind(supplierController));

  // 获取供应商列表（测试用）
  fastify.get('/test/suppliers', supplierController.getSuppliers.bind(supplierController));

  // 获取单个供应商（测试用）
  fastify.get('/test/suppliers/:id', supplierController.getSupplier.bind(supplierController));

  // 更新供应商（测试用）
  fastify.put('/test/suppliers/:id', supplierController.updateSupplier.bind(supplierController));

  // 删除供应商（测试用）
  fastify.delete('/test/suppliers/:id', supplierController.deleteSupplier.bind(supplierController));

  // 周预算管理测试接口
  const { WeeklyBudgetController } = await import('../controllers/weeklyBudget.js');
  const weeklyBudgetController = new WeeklyBudgetController();

  // 创建周预算（测试用）
  fastify.post('/test/projects/:projectId/weekly-budgets', weeklyBudgetController.createWeeklyBudget.bind(weeklyBudgetController));

  // 批量创建周预算（测试用）
  fastify.post('/test/projects/:projectId/weekly-budgets/batch', weeklyBudgetController.batchCreateWeeklyBudgets.bind(weeklyBudgetController));

  // 获取周预算列表（测试用）
  fastify.get('/test/weekly-budgets', weeklyBudgetController.getWeeklyBudgets.bind(weeklyBudgetController));

  // 获取单个周预算（测试用）
  fastify.get('/test/weekly-budgets/:id', weeklyBudgetController.getWeeklyBudget.bind(weeklyBudgetController));

  // 更新周预算（测试用）
  fastify.put('/test/weekly-budgets/:id', weeklyBudgetController.updateWeeklyBudget.bind(weeklyBudgetController));

  // 删除周预算（测试用）
  fastify.delete('/test/weekly-budgets/:id', weeklyBudgetController.deleteWeeklyBudget.bind(weeklyBudgetController));

  // 获取周预算统计（测试用）
  fastify.get('/test/weekly-budgets/stats', weeklyBudgetController.getWeeklyBudgetStats.bind(weeklyBudgetController));

  // 财务报表测试接口
  const { FinancialController } = await import('../controllers/financial.js');
  const financialController = new FinancialController();

  // 获取品牌财务汇总报表（测试用）
  fastify.get('/test/financial/brands/summary', {
    schema: {
      description: '获取品牌财务汇总报表（测试用，无认证）',
      tags: ['Test'],
      querystring: {
        type: 'object',
        properties: {
          brandId: { type: 'string', description: '品牌ID过滤' },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          projectStatus: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['draft', 'active', 'completed', 'cancelled']
            },
            description: '项目状态过滤'
          },
          includeCompleted: { type: 'string', enum: ['true', 'false'], description: '是否包含已完成项目' },
          includeCancelled: { type: 'string', enum: ['true', 'false'], description: '是否包含已取消项目' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialController.getBrandFinancialSummary.bind(financialController));

  // 获取品牌财务详细报表（测试用）
  fastify.get('/test/financial/brands/:brandId/detail', {
    schema: {
      description: '获取品牌财务详细报表（测试用，无认证）',
      tags: ['Test'],
      params: {
        type: 'object',
        required: ['brandId'],
        properties: {
          brandId: { type: 'string', description: '品牌ID' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          projectStatus: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['draft', 'active', 'completed', 'cancelled']
            },
            description: '项目状态过滤'
          },
          includeCompleted: { type: 'string', enum: ['true', 'false'], description: '是否包含已完成项目' },
          includeCancelled: { type: 'string', enum: ['true', 'false'], description: '是否包含已取消项目' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialController.getBrandFinancialDetail.bind(financialController));

  console.log('🧪 测试路由已启用（仅开发环境）');
}
