# 合同签署状态功能说明

## 功能概述

为项目管理系统新增了合同签署状态字段，用于跟踪项目合同的签署进度。该功能提供了完整的CRUD操作和查询过滤能力。

## 合同签署状态枚举

### 状态定义

```typescript
enum ContractSigningStatus {
  NO_CONTRACT = 'no_contract',   // 无合同
  SIGNED = 'signed',             // 已签订
  SIGNING = 'signing',           // 签订中
  PENDING = 'pending',           // 待定
}
```

### 状态说明

- **无合同 (NO_CONTRACT)**: 项目不需要签署合同，如内部项目或小型合作
- **已签订 (SIGNED)**: 合同已经正式签署完成，可以正常执行项目
- **签订中 (SIGNING)**: 合同正在签署流程中，可能在法务审核或等待签字
- **待定 (PENDING)**: 合同签署状态未确定，需要进一步确认（默认状态）

## 数据库变更

### 新增字段

在 `projects` 表中新增了以下字段：

```sql
ALTER TABLE projects ADD COLUMN contract_signing_status contract_signing_status NOT NULL DEFAULT 'PENDING';
CREATE INDEX idx_projects_contract_signing_status ON projects(contract_signing_status);
```

### 字段属性

- **字段名**: `contractSigningStatus`
- **类型**: `ContractSigningStatus` 枚举
- **默认值**: `PENDING`
- **是否必填**: 是
- **索引**: 已建立索引以优化查询性能

## API接口更新

### 1. 创建项目

**接口**: `POST /api/projects`

**请求体新增字段**:
```json
{
  "contractSigningStatus": "pending"  // 可选，默认为 "pending"
}
```

**示例**:
```json
{
  "documentType": "proposal",
  "brandId": "brand-001",
  "projectName": "春季营销活动",
  "contractSigningStatus": "signing",
  "period": {
    "startDate": "2024-03-01",
    "endDate": "2024-05-31"
  },
  "budget": {
    "planningBudget": 500000,
    "influencerBudget": 300000,
    "adBudget": 150000,
    "otherBudget": 50000
  },
  "cost": {
    "influencerCost": 250000,
    "adCost": 120000,
    "otherCost": 40000,
    "estimatedInfluencerRebate": 25000
  },
  "executorPM": "user-001",
  "contentMediaIds": ["user-002", "user-003"],
  "contractType": "quarterly_frame",
  "settlementRules": "<p>按季度结算</p>",
  "kpi": "<p>目标ROI: 3.0</p>"
}
```

### 2. 更新项目

**接口**: `PUT /api/projects/:id`

**请求体新增字段**:
```json
{
  "contractSigningStatus": "signed"  // 可选
}
```

### 3. 查询项目

**接口**: `GET /api/projects`

**新增查询参数**:
- `contractSigningStatus`: 按合同签署状态过滤

**示例**:
```bash
# 查询所有已签订合同的项目
GET /api/projects?contractSigningStatus=signed

# 查询签订中的单次合同项目
GET /api/projects?contractType=single&contractSigningStatus=signing

# 分页查询待定状态的项目
GET /api/projects?contractSigningStatus=pending&page=1&pageSize=10
```

### 4. 项目详情

**接口**: `GET /api/projects/:id`

**响应新增字段**:
```json
{
  "id": "project-001",
  "projectName": "春季营销活动",
  "contractSigningStatus": "signed",
  // ... 其他字段
}
```

## 使用示例

### 1. JavaScript/TypeScript

```typescript
import { ContractSigningStatus } from './types/project';

// 创建项目时设置合同签署状态
const createProjectData = {
  // ... 其他字段
  contractSigningStatus: ContractSigningStatus.SIGNING
};

// 更新项目合同状态
const updateData = {
  id: 'project-001',
  contractSigningStatus: ContractSigningStatus.SIGNED
};

// 查询特定状态的项目
const signedProjects = await projectService.getProjects({
  contractSigningStatus: ContractSigningStatus.SIGNED
});
```

### 2. API调用

```bash
# 创建项目
curl -X POST http://localhost:3000/api/projects \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "projectName": "测试项目",
    "contractSigningStatus": "signing",
    "documentType": "proposal",
    "brandId": "brand-001",
    ...
  }'

# 更新合同状态
curl -X PUT http://localhost:3000/api/projects/project-001 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "contractSigningStatus": "signed"
  }'

# 查询已签订的项目
curl "http://localhost:3000/api/projects?contractSigningStatus=signed" \
  -H "Authorization: Bearer your-token"
```

## 业务场景

### 1. 项目生命周期管理

```
创建项目 → 待定(PENDING) → 签订中(SIGNING) → 已签订(SIGNED) → 项目执行
                ↓
            无合同(NO_CONTRACT) → 项目执行
```

### 2. 状态流转规则

- **待定 → 签订中**: 开始合同签署流程
- **待定 → 无合同**: 确认项目不需要合同
- **签订中 → 已签订**: 合同签署完成
- **签订中 → 待定**: 合同签署暂停或需要重新评估
- **已签订 → 签订中**: 合同需要修改或重新签署（少见）

### 3. 权限控制建议

- **项目经理**: 可以查看和更新自己负责项目的合同状态
- **法务人员**: 可以查看和更新所有项目的合同状态
- **财务人员**: 可以查看合同状态，用于财务规划
- **普通员工**: 只能查看自己参与项目的合同状态

## 报表和统计

### 1. 合同状态分布

```sql
SELECT 
  contract_signing_status,
  COUNT(*) as project_count,
  SUM(planning_budget) as total_budget
FROM projects 
GROUP BY contract_signing_status;
```

### 2. 签署进度监控

```sql
SELECT 
  DATE_TRUNC('month', created_at) as month,
  contract_signing_status,
  COUNT(*) as count
FROM projects 
WHERE created_at >= NOW() - INTERVAL '12 months'
GROUP BY month, contract_signing_status
ORDER BY month, contract_signing_status;
```

## 测试

### 运行测试

```bash
# 测试合同签署状态功能
node scripts/test-contract-signing-status.js
```

### 测试覆盖

- ✅ 创建项目时设置合同签署状态
- ✅ 更新项目的合同签署状态
- ✅ 按合同签署状态过滤项目
- ✅ 组合查询条件（合同类型 + 签署状态）
- ✅ 枚举值验证
- ✅ 默认值处理

## 注意事项

1. **向后兼容**: 现有项目会自动设置为 `PENDING` 状态
2. **数据验证**: 只接受预定义的枚举值
3. **索引优化**: 已为合同签署状态字段建立索引
4. **业务逻辑**: 建议在业务层面定义状态流转规则
5. **权限控制**: 根据用户角色限制状态更新权限

## 扩展功能

### 未来可能的增强

1. **状态变更历史**: 记录合同状态的变更历史和操作人
2. **自动化流程**: 集成合同管理系统，自动更新签署状态
3. **提醒功能**: 对长时间处于"签订中"状态的项目发送提醒
4. **审批流程**: 某些状态变更需要审批确认
5. **文件关联**: 关联合同文件到项目记录

合同签署状态功能现在已经完全集成到项目管理系统中，提供了完整的状态跟踪和查询能力！
