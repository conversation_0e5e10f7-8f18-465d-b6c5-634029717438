# 简化版 Dockerfile - 解决构建问题
# 多阶段构建
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 配置 npm 和 pnpm
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm@latest --registry=https://registry.npmmirror.com && \
    pnpm config set registry https://registry.npmmirror.com

# 复制依赖文件
COPY package.json ./
COPY pnpm-lock.yaml* ./
COPY .env.prod ./.env.prod

# 安装所有依赖（包括 devDependencies）
RUN pnpm install

# 复制 Prisma schema 并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 复制源代码
COPY src ./src/
COPY tsconfig.json ./

# 构建应用
RUN pnpm run build

# 第二阶段：运行阶段
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

# 安装系统依赖
RUN apk add --no-cache tzdata curl

# 配置 npm 和 pnpm
RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm@latest --registry=https://registry.npmmirror.com && \
    pnpm config set registry https://registry.npmmirror.com

# 设置工作目录
WORKDIR /app

# 创建用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 复制依赖文件并安装生产依赖
COPY package.json ./
COPY pnpm-lock.yaml* ./
RUN pnpm install --prod && pnpm store prune

# 复制 Prisma schema 并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 复制构建后的应用
COPY --from=builder /app/dist ./dist

# 复制配置文件
COPY .env ./
COPY start.sh ./start.sh

# 设置权限
RUN chown -R nodejs:nodejs /app && \
    chmod +x /app/start.sh

# 切换用户
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["./start.sh"]
