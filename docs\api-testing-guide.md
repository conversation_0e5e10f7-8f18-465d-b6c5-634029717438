# API接口测试指南

## 📋 概述

本指南介绍如何测试和验证钉钉API接口的正常工作状态，包括使用脚本工具和网页界面两种方式。

## 🔧 命令行测试工具

### 使用方法

```bash
# 基本使用
node scripts/test-api-endpoints.js

# 指定服务器地址
API_BASE_URL=http://localhost:8080 node scripts/test-api-endpoints.js

# 查看帮助
node scripts/test-api-endpoints.js --help
```

### 测试项目

脚本会自动测试以下API接口：

1. **健康检查** (`GET /api/health`)
   - 验证服务器基本运行状态
   - 期望状态码：200

2. **获取应用配置** (`GET /api/app/config`)
   - 获取钉钉应用的基本配置信息
   - 返回：AgentId、CorpId等配置

3. **获取JSAPI签名** (`GET /api/auth/jsapi-signature`)
   - 获取钉钉JSAPI所需的签名信息
   - 参数：url（当前页面URL）
   - 返回：签名、时间戳、随机字符串

4. **获取部门列表** (`GET /api/auth/departments`)
   - 获取企业部门结构
   - 返回：部门列表和层级关系

5. **获取增强版JSAPI签名** (`GET /api/app/jsapi-signature/enhanced`)
   - 增强版签名接口
   - 包含更多调试信息

6. **获取应用统计** (`GET /api/app/stats`)
   - 获取应用使用统计信息
   - 返回：调用次数、用户数等

7. **获取用户信息** (`POST /api/auth/user-info`)
   - 通过免登授权码获取用户信息
   - 参数：authCode（免登授权码）
   - 注意：需要有效的授权码

### 输出示例

```
🚀 开始API接口测试...
📍 测试服务器: http://localhost:3000

🧪 测试: 健康检查
   ✅ 成功 - 状态码: 200, 耗时: 15ms
   📊 响应: success=true, message="服务运行正常"

🧪 测试: 获取应用配置
   ✅ 成功 - 状态码: 200, 耗时: 23ms
   📊 响应: success=true, message="N/A"
   🔧 配置: AgentId=123456789, CorpId=ding123abc

🧪 测试: 获取JSAPI签名
   ✅ 成功 - 状态码: 200, 耗时: 45ms
   📊 响应: success=true, message="N/A"
   🔐 签名: a1b2c3d4e5...

📊 测试总结:
   ✅ 通过: 6
   ❌ 失败: 1
   📈 成功率: 85.7%
```

## 🌐 网页界面测试

### 官方演示页面

访问 `official-login-demo.html` 页面，使用内置的API状态检查功能：

1. **打开页面**：在浏览器中访问官方演示页面
2. **点击检查**：点击"🔍 检查API状态"按钮
3. **查看结果**：在调试日志中查看详细的测试结果

### 测试功能

- **实时检查**：在浏览器中实时检查API状态
- **详细日志**：显示每个接口的详细测试结果
- **错误诊断**：提供具体的错误信息和建议
- **成功率统计**：显示整体API健康状况

### 使用场景

1. **开发调试**：在开发过程中快速验证API状态
2. **部署验证**：部署后确认所有接口正常工作
3. **问题排查**：当免登录流程出现问题时，先检查API状态
4. **监控检查**：定期检查API健康状况

## 🚨 常见问题

### 1. 连接失败

**问题**：无法连接到服务器
```
❌ 无法连接到服务器: connect ECONNREFUSED 127.0.0.1:3000
```

**解决方案**：
- 确认服务器已启动
- 检查端口号是否正确
- 验证防火墙设置

### 2. 配置错误

**问题**：获取应用配置失败
```
❌ 获取应用配置失败: 应用配置未找到
```

**解决方案**：
- 检查环境变量配置
- 验证钉钉应用信息
- 确认 `.env` 文件设置

### 3. 签名失败

**问题**：JSAPI签名生成失败
```
❌ 获取JSAPI签名失败: 获取jsapi_ticket失败
```

**解决方案**：
- 检查钉钉应用权限
- 验证AppKey和AppSecret
- 确认网络连接正常

### 4. 授权码无效

**问题**：用户信息获取失败
```
❌ 获取用户信息失败: 无效的免登码
```

**解决方案**：
- 免登码只能使用一次
- 确认在钉钉环境中获取
- 检查授权码格式

## 🔍 调试技巧

### 1. 详细日志

启用详细日志模式：
```bash
DEBUG=* node scripts/test-api-endpoints.js
```

### 2. 单独测试

测试特定接口：
```javascript
// 在浏览器控制台中执行
fetch('/api/health')
  .then(res => res.json())
  .then(data => console.log(data));
```

### 3. 网络抓包

使用浏览器开发者工具：
1. 打开Network面板
2. 执行API测试
3. 查看请求和响应详情

### 4. 服务器日志

查看服务器端日志：
```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 📊 性能监控

### 响应时间

- **健康检查**：< 50ms
- **应用配置**：< 100ms
- **JSAPI签名**：< 200ms（需要调用钉钉API）
- **部门列表**：< 500ms（取决于部门数量）

### 成功率标准

- **生产环境**：> 99%
- **测试环境**：> 95%
- **开发环境**：> 90%

## 🛠️ 自动化测试

### CI/CD集成

在CI/CD流水线中集成API测试：

```yaml
# GitHub Actions 示例
- name: Test API Endpoints
  run: |
    npm start &
    sleep 10
    node scripts/test-api-endpoints.js
    kill %1
```

### 定时监控

设置定时任务监控API状态：

```bash
# crontab 示例 - 每5分钟检查一次
*/5 * * * * cd /path/to/project && node scripts/test-api-endpoints.js >> logs/api-monitor.log 2>&1
```

## 📚 相关文档

- [钉钉官方免登录实现指南](./official-dingtalk-login-guide.md)
- [页面导航使用指南](./navigation-guide.md)
- [签名问题修复指南](./signature-fix-guide.md)
- [项目实现总结](../DINGTALK_IMPLEMENTATION.md)

## 🎯 最佳实践

1. **定期测试**：每次部署后都要运行完整的API测试
2. **监控告警**：设置API失败率告警机制
3. **日志记录**：保留详细的API调用日志
4. **错误处理**：为每个API调用实现适当的错误处理
5. **性能优化**：监控API响应时间，及时优化慢接口
