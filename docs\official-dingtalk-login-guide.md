# 钉钉官方免登录实现指南

## 📚 基于官方文档的完整实现

根据钉钉官方文档 [企业内部应用免登](https://open.dingtalk.com/document/orgapp/enterprise-internal-application-logon-free) 和 [获取微应用免登授权码](https://open.dingtalk.com/document/orgapp/obtain-the-micro-application-exemption-authorization-code)，本指南提供了完整的免登录实现方案。

## 🔄 官方免登录流程

### 1. 前端流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant H5 as H5页面
    participant DD as 钉钉客户端
    participant Server as 后端服务
    participant DT as 钉钉服务器

    User->>H5: 访问H5页面
    H5->>Server: 获取应用配置
    Server-->>H5: 返回agentId, corpId等
    H5->>Server: 获取JSAPI签名
    Server->>DT: 获取jsapi_ticket
    DT-->>Server: 返回ticket
    Server-->>H5: 返回签名信息
    H5->>DD: dd.config()配置JSAPI
    DD-->>H5: dd.ready()回调
    H5->>DD: dd.runtime.permission.requestAuthCode()
    DD-->>H5: 返回免登授权码
    H5->>Server: 发送授权码获取用户信息
    Server->>DT: 通过授权码获取用户ID
    DT-->>Server: 返回用户ID
    Server->>DT: 获取用户详细信息
    DT-->>Server: 返回用户信息
    Server-->>H5: 返回用户信息
    H5-->>User: 显示登录成功
```

### 2. 关键API调用

#### 前端JSAPI调用

```javascript
// 1. 配置JSAPI
dd.config({
    agentId: 'your_agent_id',
    corpId: 'your_corp_id', 
    timeStamp: timestamp,
    nonceStr: 'random_string',
    signature: 'calculated_signature',
    jsApiList: ['runtime.permission.requestAuthCode']
});

// 2. 获取免登授权码
dd.runtime.permission.requestAuthCode({
    redirection: "none", // 不跳转
    onSuccess: function(result) {
        // result.code 就是免登授权码
        console.log('免登码:', result.code);
    },
    onFail: function(err) {
        console.error('获取免登码失败:', err);
    }
});
```

#### 后端API调用

```typescript
// 1. 获取访问令牌
POST https://oapi.dingtalk.com/gettoken
{
    "appkey": "your_app_key",
    "appsecret": "your_app_secret"
}

// 2. 通过免登码获取用户ID
POST https://oapi.dingtalk.com/topapi/v2/user/getuserinfo
{
    "access_token": "access_token",
    "code": "auth_code"
}

// 3. 获取用户详细信息
POST https://oapi.dingtalk.com/topapi/v2/user/get
{
    "access_token": "access_token", 
    "userid": "user_id"
}
```

## 🔧 技术实现要点

### 1. JSAPI签名算法

根据官方文档，签名算法如下：

```typescript
// 签名字符串格式（参数按字母顺序排列）
const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`;

// 使用SHA1加密
const signature = crypto.createHash('sha1').update(string1, 'utf8').digest('hex');
```

**重要注意事项**：
- 参数必须按字母顺序排列：`jsapi_ticket`, `noncestr`, `timestamp`, `url`
- URL必须是当前页面的完整URL，不能包含fragment（#后面的部分）
- 钉钉调试参数（dd_debug_*）会影响签名，需要清理

### 2. 错误处理

常见错误码及处理：

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 9 | 签名校验失败 | 检查签名算法、URL处理、ticket有效性 |
| 40001 | access_token无效 | 重新获取access_token |
| 40014 | 不合法的access_token | 检查appkey和appsecret |
| 60011 | 无效的免登码 | 免登码只能使用一次，需重新获取 |

### 3. 安全考虑

- **HTTPS要求**：生产环境必须使用HTTPS
- **域名白名单**：在钉钉开放平台配置可信域名
- **IP白名单**：配置服务器出口IP
- **免登码有效期**：免登码只能使用一次，有效期5分钟

## 📱 移动端适配

### 1. 响应式设计

```css
/* 适配钉钉客户端 */
@media screen and (max-width: 768px) {
    .container {
        padding: 10px;
        margin: 0;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 5px;
    }
}
```

### 2. 钉钉容器检测

```javascript
// 检测是否在钉钉容器中
function isDingTalkEnv() {
    return /DingTalk/i.test(navigator.userAgent) || 
           typeof dd !== 'undefined';
}

// 检测设备类型
function getDeviceType() {
    const ua = navigator.userAgent;
    if (/Android/i.test(ua)) return 'android';
    if (/iPhone|iPad/i.test(ua)) return 'ios';
    return 'pc';
}
```

## 🚀 最佳实践

### 1. 性能优化

- **缓存策略**：合理缓存access_token和jsapi_ticket
- **并发控制**：避免同时多次获取token
- **错误重试**：实现指数退避重试机制

### 2. 用户体验

- **加载状态**：显示清晰的加载进度
- **错误提示**：友好的错误信息和解决建议
- **降级方案**：非钉钉环境的处理方案

### 3. 调试支持

- **详细日志**：记录关键步骤和参数
- **调试模式**：开发环境的调试信息
- **测试工具**：提供独立的测试页面

## 🔍 故障排除

### 1. 签名问题

```javascript
// 调试签名生成
function debugSignature(ticket, nonceStr, timestamp, url) {
    console.log('签名参数:', {
        ticket: ticket.substring(0, 10) + '...',
        nonceStr,
        timestamp,
        url
    });
    
    const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`;
    console.log('签名字符串:', string1);
    
    const signature = crypto.createHash('sha1').update(string1, 'utf8').digest('hex');
    console.log('生成的签名:', signature);
    
    return signature;
}
```

### 2. 网络问题

```javascript
// 网络请求重试
async function retryRequest(fn, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await fn();
        } catch (error) {
            if (i === maxRetries - 1) throw error;
            await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)));
        }
    }
}
```

### 3. 环境问题

```javascript
// 环境检查
function checkEnvironment() {
    const checks = {
        https: location.protocol === 'https:',
        dingtalk: isDingTalkEnv(),
        jsapi: typeof dd !== 'undefined',
        config: !!window.DINGTALK_CONFIG
    };
    
    console.log('环境检查:', checks);
    return checks;
}
```

## 📋 检查清单

### 开发阶段
- [ ] 配置钉钉应用信息（AppKey、AppSecret、AgentId）
- [ ] 实现JSAPI签名生成
- [ ] 实现免登录流程
- [ ] 添加错误处理
- [ ] 测试各种场景

### 部署阶段
- [ ] 配置HTTPS证书
- [ ] 设置域名白名单
- [ ] 配置IP白名单
- [ ] 验证生产环境
- [ ] 监控和日志

### 测试阶段
- [ ] 钉钉客户端测试
- [ ] 不同设备测试
- [ ] 网络异常测试
- [ ] 边界条件测试
- [ ] 性能测试

## 📚 参考资料

- [钉钉开放平台](https://open.dingtalk.com/)
- [企业内部应用免登](https://open.dingtalk.com/document/orgapp/enterprise-internal-application-logon-free)
- [获取微应用免登授权码](https://open.dingtalk.com/document/orgapp/obtain-the-micro-application-exemption-authorization-code)
- [免登流程](https://open.dingtalk.com/document/orgapp/logon-free-process)
- [JSAPI概述](https://open.dingtalk.com/document/orgapp/jsapi-overview)
