/**
 * 钉钉Stream推送管理路由
 * 提供Stream服务的状态监控和控制接口
 */

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DingTalkStreamService } from '../services/dingtalk-stream.js';

// 全局Stream服务实例
let globalStreamService: DingTalkStreamService | null = null;

export async function dingTalkStreamRoutes(fastify: FastifyInstance) {
  
  /**
   * 获取Stream服务状态
   */
  fastify.get('/dingtalk/stream/status', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      if (!globalStreamService) {
        return reply.code(200).send({
          success: true,
          data: {
            isRunning: false,
            isConnected: false,
            message: 'Stream服务未启动'
          }
        });
      }

      const status = globalStreamService.getConnectionStatus();
      
      return reply.code(200).send({
        success: true,
        data: {
          isRunning: true,
          isConnected: status.isConnected,
          reconnectAttempts: status.reconnectAttempts,
          endpoint: status.endpoint,
          message: status.isConnected ? 'Stream服务运行正常' : 'Stream服务已启动但未连接'
        }
      });
    } catch (error) {
      console.error('获取Stream服务状态失败:', error);
      return reply.code(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取Stream服务状态失败'
      });
    }
  });

  /**
   * 启动Stream服务
   */
  fastify.post('/dingtalk/stream/start', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      if (globalStreamService) {
        const status = globalStreamService.getConnectionStatus();
        if (status.isConnected) {
          return reply.code(200).send({
            success: true,
            message: 'Stream服务已在运行中',
            data: status
          });
        }
      }

      // 创建新的Stream服务实例
      globalStreamService = new DingTalkStreamService();
      
      // 设置事件监听器
      globalStreamService.on('callback', (data) => {
        console.log('📨 收到Stream回调事件:', data);
      });

      globalStreamService.on('error', (error) => {
        console.error('❌ Stream服务错误:', error);
      });

      // 启动服务
      await globalStreamService.start();

      return reply.code(200).send({
        success: true,
        message: 'Stream服务启动成功',
        data: globalStreamService.getConnectionStatus()
      });
    } catch (error) {
      console.error('启动Stream服务失败:', error);
      return reply.code(500).send({
        success: false,
        message: error instanceof Error ? error.message : '启动Stream服务失败'
      });
    }
  });

  /**
   * 停止Stream服务
   */
  fastify.post('/dingtalk/stream/stop', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      if (!globalStreamService) {
        return reply.code(200).send({
          success: true,
          message: 'Stream服务未运行'
        });
      }

      await globalStreamService.stop();
      globalStreamService = null;

      return reply.code(200).send({
        success: true,
        message: 'Stream服务已停止'
      });
    } catch (error) {
      console.error('停止Stream服务失败:', error);
      return reply.code(500).send({
        success: false,
        message: error instanceof Error ? error.message : '停止Stream服务失败'
      });
    }
  });

  /**
   * 重启Stream服务
   */
  fastify.post('/dingtalk/stream/restart', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // 先停止现有服务
      if (globalStreamService) {
        await globalStreamService.stop();
        globalStreamService = null;
      }

      // 等待一秒后重新启动
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 启动新服务
      globalStreamService = new DingTalkStreamService();
      
      // 设置事件监听器
      globalStreamService.on('callback', (data) => {
        console.log('📨 收到Stream回调事件:', data);
      });

      globalStreamService.on('error', (error) => {
        console.error('❌ Stream服务错误:', error);
      });

      await globalStreamService.start();

      return reply.code(200).send({
        success: true,
        message: 'Stream服务重启成功',
        data: globalStreamService.getConnectionStatus()
      });
    } catch (error) {
      console.error('重启Stream服务失败:', error);
      return reply.code(500).send({
        success: false,
        message: error instanceof Error ? error.message : '重启Stream服务失败'
      });
    }
  });

  /**
   * 测试Stream连接
   */
  fastify.post('/dingtalk/stream/test', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      if (!globalStreamService) {
        return reply.code(400).send({
          success: false,
          message: 'Stream服务未启动，请先启动服务'
        });
      }

      const status = globalStreamService.getConnectionStatus();
      
      if (!status.isConnected) {
        return reply.code(400).send({
          success: false,
          message: 'Stream服务未连接',
          data: status
        });
      }

      // 这里可以添加更多的连接测试逻辑
      // 比如发送测试消息等

      return reply.code(200).send({
        success: true,
        message: 'Stream连接测试通过',
        data: {
          ...status,
          testTime: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('测试Stream连接失败:', error);
      return reply.code(500).send({
        success: false,
        message: error instanceof Error ? error.message : '测试Stream连接失败'
      });
    }
  });

  /**
   * 获取Stream服务配置信息
   */
  fastify.get('/dingtalk/stream/config', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const config = {
        appKey: process.env.DINGTALK_APP_KEY ? '***' + process.env.DINGTALK_APP_KEY.slice(-4) : '未配置',
        appSecret: process.env.DINGTALK_APP_SECRET ? '***' + process.env.DINGTALK_APP_SECRET.slice(-4) : '未配置',
        enableStream: process.env.ENABLE_DINGTALK_STREAM || 'false',
        nodeEnv: process.env.NODE_ENV || 'development'
      };

      return reply.code(200).send({
        success: true,
        data: config
      });
    } catch (error) {
      console.error('获取Stream配置失败:', error);
      return reply.code(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取Stream配置失败'
      });
    }
  });
}

/**
 * 获取全局Stream服务实例
 */
export function getGlobalStreamService(): DingTalkStreamService | null {
  return globalStreamService;
}

/**
 * 设置全局Stream服务实例
 */
export function setGlobalStreamService(service: DingTalkStreamService | null): void {
  globalStreamService = service;
}
