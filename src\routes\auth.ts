import { FastifyInstance } from 'fastify';
import { AuthController } from '../controllers/auth.js';

export async function authRoutes(fastify: FastifyInstance) {
  const authController = new AuthController();

  // 通过免登码登录并获取JWT token
  fastify.post('/auth/login', {
    schema: {
      description: '通过钉钉免登码登录并获取JWT token',
      tags: ['Auth'],
      body: {
        type: 'object',
        required: ['authCode'],
        properties: {
          authCode: {
            type: 'string',
            description: '钉钉免登码',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' },
                expiresIn: { type: 'string' },
                user: {
                  type: 'object',
                  properties: {
                    userid: { type: 'string' },
                    name: { type: 'string' },
                    mobile: { type: 'string' },
                    deptIds: {
                      type: 'array',
                      items: { type: 'number' },
                    },
                    isAdmin: { type: 'boolean' },
                    isBoss: { type: 'boolean' },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            errors: { type: 'array' },
          },
        },
        500: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, authController.getUserInfoByCode.bind(authController));

  // 获取JSAPI签名
  fastify.get('/auth/jsapi-signature', {
    schema: {
      description: '获取钉钉JSAPI签名',
      tags: ['Auth'],
      querystring: {
        type: 'object',
        required: ['url'],
        properties: {
          url: {
            type: 'string',
            format: 'uri',
            description: '当前页面URL',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                agentId: { type: 'string' },
                corpId: { type: 'string' },
                timeStamp: { type: 'number' },
                nonceStr: { type: 'string' },
                signature: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, authController.getJSAPISignature.bind(authController));

  // 获取部门列表
  fastify.get('/auth/departments', {
    schema: {
      description: '获取部门列表',
      tags: ['Auth'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  dept_id: { type: 'number' },
                  name: { type: 'string' },
                  parent_id: { type: 'number' },
                  order: { type: 'number' },
                  create_dept_group: { type: 'boolean' },
                  auto_add_user: { type: 'boolean' },
                },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, authController.getDepartmentList.bind(authController));

  // 刷新访问令牌
  fastify.post('/auth/refresh', {
    schema: {
      description: '刷新访问令牌',
      tags: ['Auth'],
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: {
            type: 'string',
            description: '刷新令牌',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                accessToken: { type: 'string' },
                expiresIn: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
        401: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
  }, authController.refreshToken.bind(authController));

  // 用户登出
  fastify.post('/auth/logout', {
    schema: {
      description: '用户登出',
      tags: ['Auth'],
      headers: {
        type: 'object',
        properties: {
          authorization: {
            type: 'string',
            description: 'Bearer token',
          },
        },
        required: ['authorization'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, authController.logout.bind(authController));

  // 获取当前用户信息
  fastify.get('/auth/me', {
    schema: {
      description: '获取当前用户信息',
      tags: ['Auth'],
      headers: {
        type: 'object',
        properties: {
          authorization: {
            type: 'string',
            description: 'Bearer token',
          },
        },
        required: ['authorization'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    userid: { type: 'string' },
                    name: { type: 'string' },
                    mobile: { type: 'string' },
                    deptIds: {
                      type: 'array',
                      items: { type: 'number' },
                    },
                    isAdmin: { type: 'boolean' },
                    isBoss: { type: 'boolean' },
                  },
                },
                session: {
                  type: 'object',
                  properties: {
                    loginTime: { type: 'number' },
                    lastActiveTime: { type: 'number' },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
        401: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            code: { type: 'string' },
          },
        },
      },
    },
  }, authController.getCurrentUser.bind(authController));

  // 获取用户列表
  fastify.get('/auth/users', {
    schema: {
      description: '获取用户列表，支持按部门查询或查询所有用户',
      tags: ['Auth'],
      querystring: {
        type: 'object',
        properties: {
          deptId: {
            type: 'number',
            description: '部门ID，不传则查询所有用户',
          },
          cursor: {
            type: 'number',
            minimum: 0,
            default: 0,
            description: '分页游标',
          },
          size: {
            type: 'number',
            minimum: 1,
            maximum: 100,
            default: 20,
            description: '每页数量',
          },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      name: { type: 'string' },
                      dept_id_list: {
                        type: 'array',
                        items: { type: 'number' },
                      },
                    },
                  },
                },
                pagination: {
                  type: 'object',
                  properties: {
                    cursor: { type: 'number' },
                    size: { type: 'number' },
                    hasMore: { type: 'boolean' },
                    nextCursor: { type: 'number' },
                  },
                },
              },
            },
            message: { type: 'string' },
          },
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            errors: { type: 'array' },
          },
        },
        500: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, authController.getUserList.bind(authController));

  // 获取会话统计（管理员功能）
  fastify.get('/auth/sessions/stats', {
    schema: {
      description: '获取会话统计信息',
      tags: ['Auth'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalSessions: { type: 'number' },
                adminSessions: { type: 'number' },
                bossSessions: { type: 'number' },
                recentSessions: { type: 'number' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, authController.getSessionStats.bind(authController));
}
