#!/bin/bash

# Docker 构建优化脚本
# 使用 BuildKit 和多种优化技术来加速构建

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="cantv-ding-backend"
TAG=${1:-latest}
REGISTRY=${REGISTRY:-""}

echo -e "${BLUE}🚀 开始优化构建 Docker 镜像...${NC}"

# 启用 Docker BuildKit（提升构建性能）
export DOCKER_BUILDKIT=1
export BUILDKIT_PROGRESS=plain

# 检查 Docker 版本
echo -e "${YELLOW}📋 检查 Docker 环境...${NC}"
docker --version
docker buildx version 2>/dev/null || echo "BuildX 不可用，使用标准构建"

# 清理构建缓存（可选，如果需要完全重新构建）
if [ "$2" = "--no-cache" ]; then
    echo -e "${YELLOW}🧹 清理构建缓存...${NC}"
    docker builder prune -f
fi

# 构建镜像
echo -e "${BLUE}🔨 构建镜像: ${IMAGE_NAME}:${TAG}${NC}"

# 使用 BuildKit 的高级特性
if command -v docker buildx >/dev/null 2>&1; then
    echo -e "${GREEN}✅ 使用 BuildX 进行优化构建${NC}"
    
    # 创建并使用 buildx 实例（如果不存在）
    docker buildx create --name mybuilder --use 2>/dev/null || docker buildx use mybuilder 2>/dev/null || true
    
    # 使用 BuildX 构建（支持并行构建和高级缓存）
    docker buildx build \
        --platform linux/amd64 \
        --cache-from type=local,src=/tmp/.buildx-cache \
        --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max \
        --tag ${IMAGE_NAME}:${TAG} \
        --load \
        .
    
    # 更新缓存
    rm -rf /tmp/.buildx-cache
    mv /tmp/.buildx-cache-new /tmp/.buildx-cache 2>/dev/null || true
    
else
    echo -e "${YELLOW}⚠️  使用标准 Docker 构建${NC}"
    
    # 标准构建（带缓存优化）
    docker build \
        --tag ${IMAGE_NAME}:${TAG} \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        .
fi

# 检查镜像大小
echo -e "${BLUE}📊 镜像信息:${NC}"
docker images ${IMAGE_NAME}:${TAG} --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 如果指定了注册表，推送镜像
if [ -n "$REGISTRY" ]; then
    echo -e "${BLUE}📤 推送镜像到注册表...${NC}"
    docker tag ${IMAGE_NAME}:${TAG} ${REGISTRY}/${IMAGE_NAME}:${TAG}
    docker push ${REGISTRY}/${IMAGE_NAME}:${TAG}
    echo -e "${GREEN}✅ 镜像已推送到: ${REGISTRY}/${IMAGE_NAME}:${TAG}${NC}"
fi

# 清理悬空镜像
echo -e "${YELLOW}🧹 清理悬空镜像...${NC}"
docker image prune -f

echo -e "${GREEN}🎉 构建完成！${NC}"
echo -e "${BLUE}📝 使用方法:${NC}"
echo -e "  运行容器: ${YELLOW}docker run -d -p 3000:3000 --name cantv-ding ${IMAGE_NAME}:${TAG}${NC}"
echo -e "  查看日志: ${YELLOW}docker logs -f cantv-ding${NC}"
echo -e "  停止容器: ${YELLOW}docker stop cantv-ding${NC}"

# 性能提示
echo -e "${BLUE}💡 性能优化提示:${NC}"
echo -e "  1. 使用 ${YELLOW}--no-cache${NC} 参数进行完全重新构建"
echo -e "  2. 设置 ${YELLOW}REGISTRY${NC} 环境变量自动推送镜像"
echo -e "  3. 确保 Docker 有足够的内存和 CPU 资源"
echo -e "  4. 使用 SSD 存储可显著提升构建速度"
