import jwt from 'jsonwebtoken';
import { env } from '../config/env.js';
import { redisService } from './redis.js';

// JWT载荷接口
export interface JWTPayload {
  userid: string;
  name: string;
  mobile: string;
  deptIds: number[];
  isAdmin: boolean;
  isBoss: boolean;
  iat?: number;
  exp?: number;
}

// 用户会话信息接口
export interface UserSession {
  userid: string;
  name: string;
  mobile: string;
  deptIds: number[];
  isAdmin: boolean;
  isBoss: boolean;
  loginTime: number;
  lastActiveTime: number;
}

export class JWTService {
  private readonly secret: string;
  private readonly expiresIn: string;
  private readonly refreshExpiresIn: string;
  private readonly useRedis: boolean;

  // Redis 键前缀
  private readonly REDIS_PREFIX = {
    ACCESS_TOKEN: 'jwt:access:',
    REFRESH_TOKEN: 'jwt:refresh:',
    USER_SESSION: 'session:user:',
    BLACKLIST: 'jwt:blacklist:'
  };

  // 内存中的用户会话存储（Redis 不可用时的备用方案）
  private static userSessions = new Map<string, UserSession>();

  constructor() {
    this.secret = env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
    this.expiresIn = env.JWT_EXPIRES_IN || '24h';
    this.refreshExpiresIn = env.JWT_REFRESH_EXPIRES_IN || '7d';
    this.useRedis = !!env.REDIS_URL || !!env.REDIS_HOST;
  }

  /**
   * 生成访问令牌
   */
  generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    return jwt.sign(payload, this.secret, {
      expiresIn: this.expiresIn,
      issuer: 'cantv-ding',
      audience: 'cantv-ding-users'
    } as jwt.SignOptions);
  }

  /**
   * 生成刷新令牌
   */
  generateRefreshToken(userid: string): string {
    return jwt.sign(
      { userid, type: 'refresh' },
      this.secret,
      {
        expiresIn: this.refreshExpiresIn,
        issuer: 'cantv-ding',
        audience: 'cantv-ding-users'
      } as jwt.SignOptions
    );
  }

  /**
   * 验证访问令牌
   */
  async verifyAccessToken(token: string): Promise<JWTPayload | null> {
    try {
      // 首先检查 token 是否在黑名单中
      if (this.useRedis && redisService.isReady()) {
        const isBlacklisted = await redisService.exists(this.REDIS_PREFIX.BLACKLIST + token);
        if (isBlacklisted) {
          console.log('Token 已被加入黑名单');
          return null;
        }
      }

      const decoded = jwt.verify(token, this.secret, {
        issuer: 'cantv-ding',
        audience: 'cantv-ding-users'
      }) as JWTPayload;

      // 检查用户会话是否仍然有效
      const session = await this.getUserSession(decoded.userid);
      if (!session) {
        console.log('用户会话不存在:', decoded.userid);
        return null;
      }

      // 更新最后活跃时间
      await this.updateUserLastActiveTime(decoded.userid);

      return decoded;
    } catch (error) {
      console.error('JWT验证失败:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * 验证刷新令牌
   */
  verifyRefreshToken(token: string): { userid: string } | null {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: 'cantv-ding',
        audience: 'cantv-ding-users'
      }) as any;

      if (decoded.type !== 'refresh') {
        return null;
      }

      return { userid: decoded.userid };
    } catch (error) {
      console.error('刷新令牌验证失败:', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * 创建用户会话
   */
  async createUserSession(userInfo: Omit<UserSession, 'loginTime' | 'lastActiveTime'>): Promise<void> {
    const session: UserSession = {
      ...userInfo,
      loginTime: Date.now(),
      lastActiveTime: Date.now()
    };

    if (this.useRedis && redisService.isReady()) {
      try {
        const sessionKey = this.REDIS_PREFIX.USER_SESSION + userInfo.userid;
        await redisService.set(sessionKey, JSON.stringify(session), env.REDIS_TTL_USER_SESSION);
        console.log(`用户会话已创建 (Redis): ${userInfo.name} (${userInfo.userid})`);
      } catch (error) {
        console.error('Redis 存储会话失败，使用内存存储:', error);
        JWTService.userSessions.set(userInfo.userid, session);
      }
    } else {
      JWTService.userSessions.set(userInfo.userid, session);
      console.log(`用户会话已创建 (内存): ${userInfo.name} (${userInfo.userid})`);
    }
  }

  /**
   * 获取用户会话
   */
  async getUserSession(userid: string): Promise<UserSession | null> {
    if (this.useRedis && redisService.isReady()) {
      try {
        const sessionKey = this.REDIS_PREFIX.USER_SESSION + userid;
        const sessionData = await redisService.get(sessionKey);
        if (sessionData) {
          return JSON.parse(sessionData) as UserSession;
        }
      } catch (error) {
        console.error('Redis 获取会话失败，尝试内存存储:', error);
      }
    }

    return JWTService.userSessions.get(userid) || null;
  }

  /**
   * 更新用户最后活跃时间
   */
  async updateUserLastActiveTime(userid: string): Promise<void> {
    if (this.useRedis && redisService.isReady()) {
      try {
        const sessionKey = this.REDIS_PREFIX.USER_SESSION + userid;
        const sessionData = await redisService.get(sessionKey);
        if (sessionData) {
          const session = JSON.parse(sessionData) as UserSession;
          session.lastActiveTime = Date.now();
          await redisService.set(sessionKey, JSON.stringify(session), env.REDIS_TTL_USER_SESSION);
        }
      } catch (error) {
        console.error('Redis 更新活跃时间失败:', error);
      }
    } else {
      const session = JWTService.userSessions.get(userid);
      if (session) {
        session.lastActiveTime = Date.now();
        JWTService.userSessions.set(userid, session);
      }
    }
  }

  /**
   * 删除用户会话（登出）
   */
  async removeUserSession(userid: string): Promise<boolean> {
    let removed = false;

    if (this.useRedis && redisService.isReady()) {
      try {
        const sessionKey = this.REDIS_PREFIX.USER_SESSION + userid;
        const result = await redisService.del(sessionKey);
        removed = result > 0;
        console.log(`用户会话已删除 (Redis): ${userid}`);
      } catch (error) {
        console.error('Redis 删除会话失败:', error);
      }
    }

    // 同时从内存中删除（如果存在）
    const memoryRemoved = JWTService.userSessions.delete(userid);
    if (memoryRemoved && !removed) {
      console.log(`用户会话已删除 (内存): ${userid}`);
      removed = true;
    }

    return removed;
  }

  /**
   * 将 token 加入黑名单
   */
  async blacklistToken(token: string, expiresIn?: number): Promise<void> {
    if (this.useRedis && redisService.isReady()) {
      try {
        const blacklistKey = this.REDIS_PREFIX.BLACKLIST + token;
        const ttl = expiresIn || env.REDIS_TTL_ACCESS_TOKEN;
        await redisService.set(blacklistKey, '1', ttl);
        console.log('Token 已加入黑名单');
      } catch (error) {
        console.error('添加 token 到黑名单失败:', error);
      }
    }
  }

  /**
   * 检查 token 是否在黑名单中
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    if (this.useRedis && redisService.isReady()) {
      try {
        return await redisService.exists(this.REDIS_PREFIX.BLACKLIST + token);
      } catch (error) {
        console.error('检查 token 黑名单状态失败:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * 清理过期会话（仅清理内存中的会话，Redis 会自动过期）
   */
  cleanExpiredSessions(): void {
    const now = Date.now();
    const maxInactiveTime = 24 * 60 * 60 * 1000; // 24小时

    for (const [userid, session] of JWTService.userSessions.entries()) {
      if (now - session.lastActiveTime > maxInactiveTime) {
        JWTService.userSessions.delete(userid);
        console.log(`已清理过期会话 (内存): ${userid}`);
      }
    }
  }

  /**
   * 获取所有活跃会话统计
   */
  async getActiveSessionsStats(): Promise<{
    totalSessions: number;
    adminSessions: number;
    bossSessions: number;
    recentSessions: number;
    redisEnabled: boolean;
  }> {
    const now = Date.now();
    const recentThreshold = 30 * 60 * 1000; // 30分钟

    let totalSessions = 0;
    let adminSessions = 0;
    let bossSessions = 0;
    let recentSessions = 0;

    // 如果使用 Redis，从 Redis 获取统计
    if (this.useRedis && redisService.isReady()) {
      try {
        const sessionKeys = await redisService.keys(this.REDIS_PREFIX.USER_SESSION + '*');
        const sessions = await Promise.all(
          sessionKeys.map(async (key) => {
            const sessionData = await redisService.get(key);
            return sessionData ? JSON.parse(sessionData) as UserSession : null;
          })
        );

        for (const session of sessions) {
          if (!session) continue;

          totalSessions++;
          if (session.isAdmin) adminSessions++;
          if (session.isBoss) bossSessions++;
          if (now - session.lastActiveTime < recentThreshold) recentSessions++;
        }
      } catch (error) {
        console.error('从 Redis 获取会话统计失败:', error);
      }
    }

    // 同时统计内存中的会话
    for (const session of JWTService.userSessions.values()) {
      totalSessions++;
      if (session.isAdmin) adminSessions++;
      if (session.isBoss) bossSessions++;
      if (now - session.lastActiveTime < recentThreshold) recentSessions++;
    }

    return {
      totalSessions,
      adminSessions,
      bossSessions,
      recentSessions,
      redisEnabled: this.useRedis && redisService.isReady()
    };
  }

  /**
   * 生成完整的认证响应
   */
  async generateAuthResponse(userInfo: Omit<UserSession, 'loginTime' | 'lastActiveTime'>) {
    // 创建用户会话
    await this.createUserSession(userInfo);

    // 生成令牌
    const accessToken = this.generateAccessToken({
      userid: userInfo.userid,
      name: userInfo.name,
      mobile: userInfo.mobile,
      deptIds: userInfo.deptIds,
      isAdmin: userInfo.isAdmin,
      isBoss: userInfo.isBoss
    });

    const refreshToken = this.generateRefreshToken(userInfo.userid);

    // 如果使用 Redis，存储 refresh token
    if (this.useRedis && redisService.isReady()) {
      try {
        const refreshKey = this.REDIS_PREFIX.REFRESH_TOKEN + userInfo.userid;
        await redisService.set(refreshKey, refreshToken, env.REDIS_TTL_REFRESH_TOKEN);
      } catch (error) {
        console.error('存储 refresh token 到 Redis 失败:', error);
      }
    }

    return {
      accessToken,
      refreshToken,
      expiresIn: this.expiresIn,
      user: {
        userid: userInfo.userid,
        name: userInfo.name,
        mobile: userInfo.mobile,
        deptIds: userInfo.deptIds,
        isAdmin: userInfo.isAdmin,
        isBoss: userInfo.isBoss
      }
    };
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(refreshToken: string): Promise<{ accessToken: string; expiresIn: string } | null> {
    const decoded = this.verifyRefreshToken(refreshToken);
    if (!decoded) {
      return null;
    }

    // 如果使用 Redis，验证 refresh token 是否存在
    if (this.useRedis && redisService.isReady()) {
      try {
        const refreshKey = this.REDIS_PREFIX.REFRESH_TOKEN + decoded.userid;
        const storedToken = await redisService.get(refreshKey);
        if (storedToken !== refreshToken) {
          console.log('Refresh token 不匹配或已过期');
          return null;
        }
      } catch (error) {
        console.error('验证 refresh token 失败:', error);
        return null;
      }
    }

    const session = await this.getUserSession(decoded.userid);
    if (!session) {
      return null;
    }

    const newAccessToken = this.generateAccessToken({
      userid: session.userid,
      name: session.name,
      mobile: session.mobile,
      deptIds: session.deptIds,
      isAdmin: session.isAdmin,
      isBoss: session.isBoss
    });

    return {
      accessToken: newAccessToken,
      expiresIn: this.expiresIn
    };
  }

  /**
   * 登出用户（删除会话和令牌）
   */
  async logout(userid: string, accessToken?: string): Promise<void> {
    // 删除用户会话
    await this.removeUserSession(userid);

    // 如果提供了 access token，将其加入黑名单
    if (accessToken) {
      await this.blacklistToken(accessToken);
    }

    // 删除 refresh token
    if (this.useRedis && redisService.isReady()) {
      try {
        const refreshKey = this.REDIS_PREFIX.REFRESH_TOKEN + userid;
        await redisService.del(refreshKey);
      } catch (error) {
        console.error('删除 refresh token 失败:', error);
      }
    }

    console.log(`用户已登出: ${userid}`);
  }
}

// 单例实例
export const jwtService = new JWTService();

// 定期清理过期会话（每小时执行一次）
setInterval(() => {
  jwtService.cleanExpiredSessions();
}, 60 * 60 * 1000);
