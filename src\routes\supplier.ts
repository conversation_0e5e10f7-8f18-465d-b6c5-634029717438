import { FastifyInstance } from 'fastify';
import { SupplierController } from '../controllers/supplier.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';

export async function supplierRoutes(fastify: FastifyInstance) {
  const supplierController = new SupplierController();

  // 供应商管理路由

  // 创建供应商
  fastify.post('/suppliers', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '创建供应商',
      tags: ['Supplier'],
      body: {
        type: 'object',
        required: ['name', 'serviceTypes'],
        properties: {
          name: { type: 'string', description: '供应商名称' },
          shortName: { type: 'string', description: '简称' },
          code: { type: 'string', description: '供应商编码' },
          contactPerson: { type: 'string', description: '联系人' },
          contactPhone: { type: 'string', description: '联系电话' },
          contactEmail: { type: 'string', format: 'email', description: '联系邮箱' },
          address: { type: 'string', description: '地址' },
          taxNumber: { type: 'string', description: '税号' },
          bankAccount: { type: 'string', description: '银行账号' },
          bankName: { type: 'string', description: '开户银行' },
          legalPerson: { type: 'string', description: '法人代表' },
          serviceTypes: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['influencer', 'advertising', 'other']
            },
            description: '服务类型'
          },
          preferredTaxRate: {
            type: 'string',
            enum: ['special_1', 'special_3', 'special_6', 'general'],
            description: '首选税率'
          },
          creditLimit: { type: 'number', description: '信用额度' },
          paymentTerms: { type: 'string', description: '付款条件' },
          rating: { type: 'number', minimum: 1, maximum: 5, description: '评级' },
          notes: { type: 'string', description: '备注' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, supplierController.createSupplier.bind(supplierController));

  // 获取供应商列表
  fastify.get('/suppliers', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取供应商列表',
      tags: ['Supplier'],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', description: '页码' },
          pageSize: { type: 'string', description: '每页数量' },
          status: {
            type: 'string',
            enum: ['active', 'inactive', 'pending', 'blacklisted'],
            description: '供应商状态'
          },
          serviceType: {
            type: 'string',
            enum: ['influencer', 'advertising', 'other'],
            description: '服务类型'
          },
          keyword: { type: 'string', description: '关键词搜索' },
          sortBy: {
            type: 'string',
            enum: ['name', 'createdAt', 'rating'],
            description: '排序字段'
          },
          sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            description: '排序方向'
          },
          code: { type: 'string', description: '供应商编码' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                suppliers: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      code: { type: 'string' },
                      shortName: { type: 'string' },
                      serviceTypes: { type: 'array', items: { type: 'string' } },
                      status: { type: 'string' },
                      rating: { type: 'number' },
                      contactPerson: { type: 'string' },
                      contactPhone: { type: 'string' },
                      contactEmail: { type: 'string' },
                      address: { type: 'string' },
                      taxNumber: { type: 'string' },
                      bankAccount: { type: 'string' },
                      bankName: { type: 'string' },
                      legalPerson: { type: 'string' },
                      preferredTaxRate: { type: 'string' },
                      creditLimit: { type: 'number' },
                      paymentTerms: { type: 'string' },
                      notes: { type: 'string' },
                      createdAt: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                pageSize: { type: 'number' },
                totalPages: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, supplierController.getSuppliers.bind(supplierController));

  // 获取单个供应商
  fastify.get('/suppliers/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取单个供应商',
      tags: ['Supplier'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '供应商ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                shortName: { type: 'string' },
                code: { type: 'string' },
                contactPerson: { type: 'string' },
                contactPhone: { type: 'string' },
                contactEmail: { type: 'string' },
                address: { type: 'string' },
                taxNumber: { type: 'string' },
                bankAccount: { type: 'string' },
                bankName: { type: 'string' },
                legalPerson: { type: 'string' },
                serviceTypes: { type: 'array', items: { type: 'string' } },
                preferredTaxRate: { type: 'string' },
                creditLimit: { type: 'number' },
                paymentTerms: { type: 'string' },
                status: { type: 'string' },
                rating: { type: 'number' },
                notes: { type: 'string' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' },
                createdBy: { type: 'string' },
                updatedBy: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, supplierController.getSupplier.bind(supplierController));

  // 更新供应商
  fastify.put('/suppliers/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '更新供应商',
      tags: ['Supplier'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '供应商ID' }
        }
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', description: '供应商名称' },
          shortName: { type: 'string', description: '简称' },
          code: { type: 'string', description: '供应商编码' },
          contactPerson: { type: 'string', description: '联系人' },
          contactPhone: { type: 'string', description: '联系电话' },
          contactEmail: { type: 'string', format: 'email', description: '联系邮箱' },
          address: { type: 'string', description: '地址' },
          taxNumber: { type: 'string', description: '税号' },
          bankAccount: { type: 'string', description: '银行账号' },
          bankName: { type: 'string', description: '开户银行' },
          legalPerson: { type: 'string', description: '法人代表' },
          serviceTypes: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['influencer', 'advertising', 'other']
            },
            description: '服务类型'
          },
          preferredTaxRate: {
            type: 'string',
            enum: ['special_1', 'special_3', 'special_6', 'general'],
            description: '首选税率'
          },
          creditLimit: { type: 'number', description: '信用额度' },
          paymentTerms: { type: 'string', description: '付款条件' },
          status: {
            type: 'string',
            enum: ['active', 'inactive', 'pending', 'blacklisted'],
            description: '供应商状态'
          },
          rating: { type: 'number', minimum: 1, maximum: 5, description: '评级' },
          notes: { type: 'string', description: '备注' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: { type: 'object' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, supplierController.updateSupplier.bind(supplierController));

  // 删除供应商
  fastify.delete('/suppliers/:id', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '删除供应商',
      tags: ['Supplier'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', description: '供应商ID' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, supplierController.deleteSupplier.bind(supplierController));

  // 获取供应商统计
  fastify.get('/suppliers/stats', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取供应商统计',
      tags: ['Supplier'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                totalSuppliers: { type: 'number' },
                activeSuppliers: { type: 'number' },
                suppliersByServiceType: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      serviceType: { type: 'string' },
                      count: { type: 'number' }
                    }
                  }
                },
                suppliersByRating: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      rating: { type: 'number' },
                      count: { type: 'number' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }, supplierController.getSupplierStats.bind(supplierController));

  console.log('🏢 供应商管理路由已注册');
}
