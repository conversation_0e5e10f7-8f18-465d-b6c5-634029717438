# CanTV钉钉后端服务环境变量配置示例
# 复制此文件为 .env.dev 或 .env.prod 并填入实际值

# 应用配置
NODE_ENV=production
PORT=3000
HOST=0.0.0.0
LOG_LEVEL=info

# 数据库配置
DATABASE_URL=***************************************************/cantv_ding
POSTGRES_DB=cantv_ding
POSTGRES_USER=cantv_user
POSTGRES_PASSWORD=your_strong_password

# 钉钉应用配置
DINGTALK_APP_KEY=your_dingtalk_app_key
DINGTALK_APP_SECRET=your_dingtalk_app_secret
DINGTALK_CORP_ID=your_dingtalk_corp_id
DINGTALK_AGENT_ID=your_dingtalk_agent_id

# 钉钉API地址
DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
DINGTALK_NEW_API_BASE_URL=https://api.dingtalk.com

# JWT配置
JWT_SECRET=your_jwt_secret_key_at_least_32_characters_long
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 审批配置
# 对公付款审批流程代码（从钉钉后台获取）
APPROVAL_PROCESS_CODE_PAYMENT=PROC-PAYMENT-001
# 费用报销审批流程代码
APPROVAL_PROCESS_CODE_EXPENSE=PROC-EXPENSE-001
# 合同审批流程代码
APPROVAL_PROCESS_CODE_CONTRACT=PROC-CONTRACT-001

# Redis配置（可选）
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your_redis_password

# 镜像配置
IMAGE_TAG=latest

# 监控配置（可选）
ENABLE_METRICS=false
METRICS_PORT=9090

# 文件上传配置
MAX_FILE_SIZE=50MB
UPLOAD_PATH=/app/uploads

# 安全配置
CORS_ORIGIN=https://your-frontend-domain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
