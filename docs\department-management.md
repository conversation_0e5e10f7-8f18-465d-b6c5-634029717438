# 部门管理功能说明

## 功能概述

部门管理功能提供了完整的钉钉部门数据同步和查询能力，包括：

- 🔄 **自动同步**：定时从钉钉同步部门数据到本地数据库
- 📋 **部门查询**：提供多种方式查询部门信息
- 🌳 **树形结构**：支持部门层级关系和路径查询
- 🔍 **搜索功能**：支持按部门名称搜索
- 📊 **状态监控**：提供同步状态和统计信息

## 已实现的功能

### 1. **数据库模型**

新增了 `Department` 模型，包含以下字段：

```typescript
model Department {
  deptId                Int       @id                      // 钉钉部门ID
  name                  String    @db.VarChar(200)         // 部门名称
  parentId              Int       @default(1)              // 父部门ID
  createDeptGroup       Boolean   @default(false)          // 是否同时创建企业群
  autoAddUser           Boolean   @default(false)          // 当群满员后，是否自动加群
  fromUnionOrg          Boolean   @default(false)          // 是否来自关联组织
  tags                  String?   @db.VarChar(500)         // 部门标签
  order                 Int       @default(0)              // 在父部门中的次序值
  deptManagerUseridList String[]                           // 部门主管用户ID列表
  outerDept             Boolean   @default(false)          // 是否限制本部门成员查看通讯录
  outerPermitDepts      Int[]                              // 配置的部门员工可见部门列表
  outerPermitUsers      String[]                           // 配置的部门员工可见员工列表
  orgDeptOwner          String?   @db.VarChar(50)          // 部门的主管
  deptPerimits          Int       @default(0)              // 企业群中@all权限
  userPerimits          Int       @default(0)              // 企业群中群成员查看成员列表的权限
  outerDeptOnlySelf     Boolean   @default(false)          // 只能看到所在部门及下级部门通讯录
  sourceIdentifier      String?   @db.VarChar(100)         // 部门标识字段
  ext                   String?   @db.Text                 // 扩展字段
  hideSceneConfig       Json?                              // 隐藏场景配置
  lastSyncAt            DateTime  @default(now()) @db.Timestamptz // 最后同步时间
}
```

### 2. **部门同步服务**

`DepartmentSyncService` 提供以下功能：

- **完整同步**：`syncAllDepartments()` - 同步所有部门数据
- **获取部门信息**：`getDepartmentInfo(deptId)` - 获取单个部门详细信息
- **获取所有部门**：`getAllDepartments()` - 获取所有部门列表
- **搜索部门**：`searchDepartments(keyword)` - 按名称搜索部门
- **获取子部门**：`getSubDepartments(parentId)` - 获取指定部门的子部门
- **获取部门路径**：`getDepartmentPath(deptId)` - 获取部门的完整路径

### 3. **定时同步调度器**

`DepartmentSyncScheduler` 提供自动同步功能：

- **完整同步**：每天凌晨2点执行完整同步
- **增量同步**：每6小时检查新增部门
- **启动同步**：服务启动30秒后执行一次同步
- **手动触发**：支持手动触发同步
- **状态监控**：提供同步统计和状态信息

### 4. **API接口**

提供完整的RESTful API接口：

#### 获取所有部门列表
```http
GET /api/departments
```

#### 搜索部门
```http
GET /api/departments/search?keyword=技术
```

#### 获取单个部门信息
```http
GET /api/departments/:deptId
```

#### 获取子部门列表
```http
GET /api/departments/:parentId/children
```

#### 获取部门树形结构
```http
GET /api/departments/tree
```

#### 手动同步部门数据
```http
POST /api/departments/sync
```

#### 获取同步状态
```http
GET /api/departments/sync/status
```

## 使用方法

### 1. **启动服务**

部门功能已集成到主应用中，启动时会自动：

1. 加载部门路由
2. 启动部门同步调度器
3. 执行初始同步（延迟30秒）

```bash
npm start
```

### 2. **测试功能**

```bash
# 测试部门管理功能
node scripts/test-departments.js
```

### 3. **API调用示例**

```javascript
// 获取所有部门
const response = await fetch('/api/departments', {
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});
const departments = await response.json();

// 搜索部门
const searchResponse = await fetch('/api/departments/search?keyword=技术', {
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});
const searchResults = await searchResponse.json();

// 手动同步
const syncResponse = await fetch('/api/departments/sync', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});
const syncResult = await syncResponse.json();
```

### 4. **在代码中使用**

```typescript
import { DepartmentSyncService } from './services/departmentSync.js';
import { DatabaseService } from './services/database.js';
import { DingTalkService } from './services/dingtalk.js';

const databaseService = new DatabaseService();
const dingTalkService = new DingTalkService();
const departmentSyncService = new DepartmentSyncService(databaseService, dingTalkService);

// 获取部门信息
const deptInfo = await departmentSyncService.getDepartmentInfo(123);

// 搜索部门
const searchResults = await departmentSyncService.searchDepartments('技术');

// 获取部门树
const allDepartments = await departmentSyncService.getAllDepartments();
```

## 同步机制

### 1. **自动同步**

- **完整同步**：每天凌晨2点执行，同步所有部门数据
- **增量同步**：每6小时执行，只同步新增的部门
- **启动同步**：服务启动30秒后执行一次完整同步

### 2. **数据一致性**

- 使用 `upsert` 操作确保数据一致性
- 支持重试机制（最多3次）
- 批量处理（每批50个部门）
- 详细的错误日志和统计

### 3. **性能优化**

- 增量同步减少不必要的数据传输
- 批量处理提高同步效率
- 本地缓存减少API调用
- 索引优化提高查询性能

## 监控和调试

### 1. **同步状态监控**

```bash
# 查看同步状态
curl -H "Authorization: Bearer your-token" \
  http://localhost:3000/api/departments/sync/status
```

### 2. **日志输出**

系统会输出详细的同步日志：

```
📅 部门同步调度器已启动
⏰ 完整同步: 每天凌晨2点
⏰ 增量同步: 每6小时
🚀 执行启动时部门同步...
🔄 开始部门数据同步...
✅ 部门同步完成 (耗时: 1234ms)
📊 同步结果: 成功 15, 失败 0
```

### 3. **错误处理**

- 网络错误自动重试
- 详细的错误信息记录
- 失败部门单独记录
- 不影响其他部门同步

## 注意事项

1. **权限要求**：确保钉钉应用有部门读取权限
2. **数据库连接**：需要PostgreSQL数据库连接
3. **认证要求**：所有API接口需要JWT认证
4. **性能考虑**：大量部门时建议调整批量大小
5. **时区设置**：调度器使用Asia/Shanghai时区

## 扩展功能

部门管理功能为以下扩展提供了基础：

1. **用户部门关联**：用户信息中的部门字段可以关联到部门表
2. **权限管理**：基于部门的权限控制
3. **组织架构图**：可视化部门层级结构
4. **部门统计**：各部门的项目、预算等统计信息
5. **部门负责人**：部门主管信息管理

部门管理功能现在已经完全集成并可以使用，提供了完整的部门数据同步和查询能力！
