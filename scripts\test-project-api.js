#!/usr/bin/env node

/**
 * 项目管理API测试脚本
 * 测试项目管理相关的所有API接口
 */

import http from 'http';
import { URL } from 'url';

// 配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求函数
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || 3000,
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Project-API-Test/1.0',
                ...options.headers
            }
        };

        const req = http.request(requestOptions, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: jsonData
                    });
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: data
                    });
                }
            });
        });

        req.on('error', reject);

        if (options.body) {
            req.write(JSON.stringify(options.body));
        }

        req.end();
    });
}

// 测试结果统计
let testResults = {
    total: 0,
    passed: 0,
    failed: 0,
    details: []
};

// 执行测试
async function runTest(name, testFn) {
    testResults.total++;
    const startTime = Date.now();
    
    try {
        log(`\n🧪 测试: ${name}`, 'yellow');
        await testFn();
        const duration = Date.now() - startTime;
        log(`✅ 通过 (${duration}ms)`, 'green');
        testResults.passed++;
        testResults.details.push({ name, status: 'PASS', duration });
    } catch (error) {
        const duration = Date.now() - startTime;
        log(`❌ 失败: ${error.message} (${duration}ms)`, 'red');
        testResults.failed++;
        testResults.details.push({ name, status: 'FAIL', duration, error: error.message });
    }
}

// 测试用例

// 1. 测试项目统计接口
async function testProjectStats() {
    const response = await makeRequest(`${API_BASE_URL}/api/projects/stats`);
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const stats = response.data.data;
    const requiredFields = ['totalProjects', 'activeProjects', 'completedProjects', 'totalBudget', 'totalProfit', 'averageGrossMargin'];
    
    for (const field of requiredFields) {
        if (!(field in stats)) {
            throw new Error(`缺少必需字段: ${field}`);
        }
    }
    
    log(`   总项目数: ${stats.totalProjects}`, 'blue');
    log(`   活跃项目: ${stats.activeProjects}`, 'blue');
    log(`   总预算: ¥${stats.totalBudget.toLocaleString()}`, 'blue');
}

// 2. 测试品牌列表接口
async function testBrandsList() {
    const response = await makeRequest(`${API_BASE_URL}/api/brands`);
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const result = response.data.data;
    const requiredFields = ['brands', 'total', 'page', 'pageSize', 'totalPages'];
    
    for (const field of requiredFields) {
        if (!(field in result)) {
            throw new Error(`缺少必需字段: ${field}`);
        }
    }
    
    log(`   品牌数量: ${result.brands.length}`, 'blue');
    log(`   总数: ${result.total}`, 'blue');
}

// 3. 测试创建品牌
async function testCreateBrand() {
    const brandData = {
        name: `测试品牌_${Date.now()}`,
        description: '这是一个测试品牌',
        logo: 'https://example.com/logo.png'
    };
    
    const response = await makeRequest(`${API_BASE_URL}/api/brands`, {
        method: 'POST',
        body: brandData
    });
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const brand = response.data.data;
    
    if (brand.name !== brandData.name) {
        throw new Error('品牌名称不匹配');
    }
    
    log(`   创建的品牌ID: ${brand.id}`, 'blue');
    log(`   品牌名称: ${brand.name}`, 'blue');
    
    // 保存品牌ID供后续测试使用
    global.testBrandId = brand.id;
}

// 4. 测试获取单个品牌
async function testGetBrand() {
    if (!global.testBrandId) {
        throw new Error('没有可用的测试品牌ID');
    }
    
    const response = await makeRequest(`${API_BASE_URL}/api/brands/${global.testBrandId}`);
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const brand = response.data.data;
    
    if (brand.id !== global.testBrandId) {
        throw new Error('品牌ID不匹配');
    }
    
    log(`   品牌名称: ${brand.name}`, 'blue');
    log(`   品牌状态: ${brand.status}`, 'blue');
}

// 5. 测试项目列表接口
async function testProjectsList() {
    const response = await makeRequest(`${API_BASE_URL}/api/projects`);
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const result = response.data.data;
    const requiredFields = ['projects', 'total', 'page', 'pageSize', 'totalPages'];
    
    for (const field of requiredFields) {
        if (!(field in result)) {
            throw new Error(`缺少必需字段: ${field}`);
        }
    }
    
    log(`   项目数量: ${result.projects.length}`, 'blue');
    log(`   总数: ${result.total}`, 'blue');
}

// 6. 测试创建项目
async function testCreateProject() {
    if (!global.testBrandId) {
        throw new Error('没有可用的测试品牌ID');
    }
    
    const projectData = {
        documentType: 'project_initiation',
        brandId: global.testBrandId,
        projectName: `测试项目_${Date.now()}`,
        period: {
            startDate: '2024-02-01',
            endDate: '2024-02-29'
        },
        budget: {
            planningBudget: 100000,
            influencerBudget: 40000,
            adBudget: 30000,
            otherBudget: 10000
        },
        cost: {
            influencerCost: 35000,
            adCost: 28000,
            otherCost: 8000,
            estimatedInfluencerRebate: 2000
        },
        executorPM: 'user-001',
        contentMediaIds: ['user-002', 'user-003'],
        contractType: 'single',
        settlementRules: '测试结算规则',
        kpi: '测试KPI要求'
    };
    
    const response = await makeRequest(`${API_BASE_URL}/api/projects`, {
        method: 'POST',
        body: projectData
    });
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const project = response.data.data;
    
    if (project.projectName !== projectData.projectName) {
        throw new Error('项目名称不匹配');
    }
    
    if (!project.profit || typeof project.profit.profit !== 'number') {
        throw new Error('项目利润计算错误');
    }
    
    log(`   创建的项目ID: ${project.id}`, 'blue');
    log(`   项目名称: ${project.projectName}`, 'blue');
    log(`   项目利润: ¥${project.profit.profit.toLocaleString()}`, 'blue');
    log(`   毛利率: ${project.profit.grossMargin.toFixed(2)}%`, 'blue');
    
    // 保存项目ID供后续测试使用
    global.testProjectId = project.id;
}

// 7. 测试获取单个项目
async function testGetProject() {
    if (!global.testProjectId) {
        throw new Error('没有可用的测试项目ID');
    }
    
    const response = await makeRequest(`${API_BASE_URL}/api/projects/${global.testProjectId}`);
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const project = response.data.data;
    
    if (project.id !== global.testProjectId) {
        throw new Error('项目ID不匹配');
    }
    
    log(`   项目名称: ${project.projectName}`, 'blue');
    log(`   品牌: ${project.brand ? project.brand.name : '未知'}`, 'blue');
    log(`   状态: ${project.status}`, 'blue');
}

// 8. 测试项目查询过滤
async function testProjectsFilter() {
    if (!global.testBrandId) {
        throw new Error('没有可用的测试品牌ID');
    }
    
    const response = await makeRequest(`${API_BASE_URL}/api/projects?brandId=${global.testBrandId}&contractType=single`);
    
    if (response.status !== 200) {
        throw new Error(`状态码错误: ${response.status}`);
    }
    
    if (!response.data.success) {
        throw new Error(`API返回失败: ${response.data.message}`);
    }
    
    const result = response.data.data;
    
    // 验证过滤结果
    result.projects.forEach(project => {
        if (project.brandId !== global.testBrandId) {
            throw new Error('品牌过滤失败');
        }
        if (project.contractType !== 'single') {
            throw new Error('合同类型过滤失败');
        }
    });
    
    log(`   过滤后项目数量: ${result.projects.length}`, 'blue');
}

// 主函数
async function main() {
    log('🚀 项目管理API测试开始', 'magenta');
    log('================================', 'magenta');
    log(`测试服务器: ${API_BASE_URL}`, 'cyan');
    log('', 'reset');
    
    // 执行所有测试
    await runTest('项目统计接口', testProjectStats);
    await runTest('品牌列表接口', testBrandsList);
    await runTest('创建品牌', testCreateBrand);
    await runTest('获取单个品牌', testGetBrand);
    await runTest('项目列表接口', testProjectsList);
    await runTest('创建项目', testCreateProject);
    await runTest('获取单个项目', testGetProject);
    await runTest('项目查询过滤', testProjectsFilter);
    
    // 显示测试结果
    log('\n📊 测试结果汇总', 'magenta');
    log('================================', 'magenta');
    log(`总测试数: ${testResults.total}`, 'cyan');
    log(`通过: ${testResults.passed}`, 'green');
    log(`失败: ${testResults.failed}`, 'red');
    log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`, 'cyan');
    
    if (testResults.failed > 0) {
        log('\n❌ 失败的测试:', 'red');
        testResults.details.filter(t => t.status === 'FAIL').forEach(test => {
            log(`   - ${test.name}: ${test.error}`, 'red');
        });
    }
    
    log('\n⏱️  性能统计:', 'cyan');
    const avgDuration = testResults.details.reduce((sum, t) => sum + t.duration, 0) / testResults.details.length;
    log(`   平均响应时间: ${avgDuration.toFixed(0)}ms`, 'blue');
    
    const slowTests = testResults.details.filter(t => t.duration > 1000);
    if (slowTests.length > 0) {
        log('   慢速测试 (>1s):', 'yellow');
        slowTests.forEach(test => {
            log(`     - ${test.name}: ${test.duration}ms`, 'yellow');
        });
    }
    
    process.exit(testResults.failed > 0 ? 1 : 0);
}

// 处理命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🚀 项目管理API测试工具

功能:
  - 测试项目管理相关的所有API接口
  - 验证数据结构和业务逻辑
  - 性能监控和统计分析

用法:
  node scripts/test-project-api.js [选项]

选项:
  --help, -h     显示帮助信息

环境变量:
  API_BASE_URL   API服务器地址 (默认: http://localhost:3000)

示例:
  node scripts/test-project-api.js
  API_BASE_URL=http://localhost:8080 node scripts/test-project-api.js
`);
    process.exit(0);
}

// 运行测试
main().catch(error => {
    log(`💥 测试运行失败: ${error.message}`, 'red');
    process.exit(1);
});
