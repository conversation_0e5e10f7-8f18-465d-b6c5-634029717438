# Docker 构建性能测试

## 🧪 测试对比

### 测试环境
- **系统**: Windows 11 / Linux
- **Docker**: 最新版本，启用 BuildKit
- **硬件**: 建议 8GB+ 内存，SSD 存储

### 测试方法

#### 1. 首次构建测试
```bash
# 清理所有缓存
docker system prune -a -f
docker builder prune -a -f

# 测试原始 Dockerfile（如果有备份）
time docker build -t cantv-ding-backend:old .

# 测试优化后的 Dockerfile
time docker build -t cantv-ding-backend:new .
```

#### 2. 增量构建测试（模拟代码变更）
```bash
# 修改一个源文件
echo "// Updated at $(date)" >> src/index.ts

# 测试构建时间
time docker build -t cantv-ding-backend:incremental .
```

#### 3. 依赖变更测试
```bash
# 修改 package.json（添加一个小依赖）
# 测试依赖安装时间
time docker build -t cantv-ding-backend:deps .
```

## 📊 预期性能提升

### 构建时间对比

| 场景 | 优化前 | 优化后 | 提升幅度 |
|------|--------|--------|----------|
| 首次构建 | 8-12分钟 | 6-8分钟 | 25-33% |
| 代码变更 | 6-10分钟 | 1-3分钟 | 70-83% |
| 依赖变更 | 8-12分钟 | 4-6分钟 | 40-50% |
| 无变更重建 | 3-5分钟 | 30秒-1分钟 | 80-90% |

### 镜像大小对比

| 阶段 | 优化前 | 优化后 | 减少幅度 |
|------|--------|--------|----------|
| 构建镜像 | ~1.2GB | ~800MB | 33% |
| 运行镜像 | ~400MB | ~350MB | 12% |

## 🔍 性能分析工具

### 1. 构建时间分析
```bash
# 启用详细输出
export DOCKER_BUILDKIT=1
export BUILDKIT_PROGRESS=plain

# 分析每个步骤的耗时
docker build --progress=plain -t cantv-ding-backend:analysis . 2>&1 | tee build.log

# 提取时间信息
grep -E "^#[0-9]+" build.log | grep -E "DONE|CACHED"
```

### 2. 镜像层分析
```bash
# 查看镜像历史
docker history cantv-ding-backend:latest

# 使用 dive 工具分析镜像内容（需要安装）
docker run --rm -it \
  -v /var/run/docker.sock:/var/run/docker.sock \
  wagoodman/dive:latest cantv-ding-backend:latest
```

### 3. 构建上下文分析
```bash
# 检查构建上下文大小
du -sh .

# 检查 .dockerignore 效果
tar -czf - . | wc -c  # 实际传输大小
```

## 🚀 实际测试步骤

### 步骤1: 基准测试
```powershell
# PowerShell 版本
Measure-Command { docker build -t cantv-ding-backend:baseline . }
```

```bash
# Linux/Mac 版本
time docker build -t cantv-ding-backend:baseline .
```

### 步骤2: 优化构建测试
```powershell
# 使用优化脚本
Measure-Command { .\docker-build-optimized.ps1 -Tag optimized }
```

```bash
# 使用优化脚本
time ./docker-build-optimized.sh optimized
```

### 步骤3: 缓存效果测试
```bash
# 第一次构建
time docker build -t cantv-ding-backend:cache1 .

# 立即重新构建（测试缓存效果）
time docker build -t cantv-ding-backend:cache2 .

# 修改代码后构建
echo "// Cache test" >> src/index.ts
time docker build -t cantv-ding-backend:cache3 .
```

## 📈 监控指标

### 关键指标
1. **总构建时间**: 从开始到完成的总时间
2. **依赖安装时间**: npm/pnpm install 的时间
3. **代码编译时间**: TypeScript 编译时间
4. **镜像推送时间**: 推送到注册表的时间
5. **缓存命中率**: 使用缓存的层数比例

### 监控命令
```bash
# 实时监控 Docker 资源使用
docker stats

# 监控磁盘使用
docker system df

# 查看构建历史
docker builder ls
docker buildx du
```

## 🎯 优化验证清单

- [ ] 首次构建时间减少 20% 以上
- [ ] 代码变更后构建时间减少 60% 以上
- [ ] 无变更重建时间减少 80% 以上
- [ ] 镜像大小减少 10% 以上
- [ ] 构建缓存正常工作
- [ ] .dockerignore 有效排除文件
- [ ] 多阶段构建正常工作
- [ ] 生产镜像可正常运行

## 🔧 故障排除

### 常见问题

#### 1. BuildKit 不工作
```bash
# 检查 BuildKit 状态
docker version | grep BuildKit

# 手动启用
export DOCKER_BUILDKIT=1
```

#### 2. 缓存不生效
```bash
# 检查缓存使用情况
docker builder du

# 清理并重建缓存
docker builder prune -a
```

#### 3. 构建失败
```bash
# 查看详细错误信息
docker build --progress=plain --no-cache -t debug .

# 检查 .dockerignore
cat .dockerignore
```

#### 4. 镜像过大
```bash
# 分析镜像层
docker history cantv-ding-backend:latest

# 检查未清理的文件
docker run --rm -it cantv-ding-backend:latest sh
```

## 📝 测试报告模板

```markdown
## Docker 构建性能测试报告

### 测试环境
- 日期: [测试日期]
- 系统: [操作系统]
- Docker 版本: [版本号]
- 硬件: [CPU/内存/存储]

### 测试结果
| 测试场景 | 优化前 | 优化后 | 提升幅度 |
|----------|--------|--------|----------|
| 首次构建 | [时间] | [时间] | [百分比] |
| 增量构建 | [时间] | [时间] | [百分比] |
| 缓存构建 | [时间] | [时间] | [百分比] |

### 镜像大小
- 构建镜像: [大小]
- 运行镜像: [大小]

### 结论
[总结优化效果和建议]
```

通过这些测试，您可以量化 Docker 构建优化的效果！🚀
