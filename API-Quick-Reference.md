# API快速参考

## 基础信息
- **基础URL**: `http://localhost:3000/api`
- **认证**: `Authorization: Bearer {token}`
- **格式**: JSON

## 认证
```
POST /auth/dingtalk          # 钉钉登录
```

## 项目管理
```
POST   /projects             # 创建项目
GET    /projects             # 获取项目列表
GET    /projects/{id}        # 获取单个项目
PUT    /projects/{id}        # 更新项目
DELETE /projects/{id}        # 删除项目
```

## 品牌管理
```
POST   /brands               # 创建品牌
GET    /brands               # 获取品牌列表
GET    /brands/{id}          # 获取单个品牌
PUT    /brands/{id}          # 更新品牌
DELETE /brands/{id}          # 删除品牌
```

## 收入管理
```
POST   /projects/{projectId}/revenues  # 创建项目收入
GET    /revenues                       # 获取收入列表
GET    /revenues/{id}                  # 获取单个收入
PUT    /revenues/{id}                  # 更新收入
DELETE /revenues/{id}                  # 删除收入
GET    /revenues/stats                 # 获取收入统计
```

## 供应商管理
```
POST   /suppliers            # 创建供应商
GET    /suppliers            # 获取供应商列表
GET    /suppliers/{id}       # 获取单个供应商
PUT    /suppliers/{id}       # 更新供应商
DELETE /suppliers/{id}       # 删除供应商
GET    /suppliers/stats      # 获取供应商统计
```

## 周预算管理
```
POST   /projects/{projectId}/weekly-budgets        # 创建周预算
POST   /projects/{projectId}/weekly-budgets/batch  # 批量创建周预算
GET    /weekly-budgets                             # 获取周预算列表
GET    /weekly-budgets/{id}                        # 获取单个周预算
PUT    /weekly-budgets/{id}                        # 更新周预算
DELETE /weekly-budgets/{id}                        # 删除周预算
GET    /weekly-budgets/stats                       # 获取周预算统计
```

## 文件管理
```
POST   /upload               # 上传文件
GET    /files/{fileId}       # 下载文件
```

## 测试接口（无认证）
```
# 项目和品牌
POST   /test/brands
GET    /test/brands
POST   /test/projects
GET    /test/projects

# 收入管理
POST   /test/projects/{projectId}/revenues
GET    /test/revenues
GET    /test/revenues/{id}
PUT    /test/revenues/{id}
DELETE /test/revenues/{id}
GET    /test/revenues/stats

# 供应商管理
POST   /test/suppliers
GET    /test/suppliers
GET    /test/suppliers/{id}
PUT    /test/suppliers/{id}
DELETE /test/suppliers/{id}

# 周预算管理
POST   /test/projects/{projectId}/weekly-budgets
POST   /test/projects/{projectId}/weekly-budgets/batch
GET    /test/weekly-budgets
GET    /test/weekly-budgets/{id}
PUT    /test/weekly-budgets/{id}
DELETE /test/weekly-budgets/{id}
GET    /test/weekly-budgets/stats
```

## 枚举值

### 服务类型 (ServiceType)
- `influencer` - 达人服务
- `advertising` - 投流服务
- `other` - 其他服务

### 税率类型 (TaxRate)
- `special_1` - 专票1%
- `special_3` - 专票3%
- `special_6` - 专票6%
- `general` - 普票

### 供应商状态 (SupplierStatus)
- `active` - 活跃
- `inactive` - 停用
- `pending` - 待审核
- `blacklisted` - 黑名单

### 周预算状态 (WeeklyBudgetStatus)
- `draft` - 草稿
- `approved` - 已批准
- `executing` - 执行中
- `completed` - 已完成
- `cancelled` - 已取消

### 收入状态 (RevenueStatus)
- `planned` - 计划中
- `confirmed` - 已确认
- `invoiced` - 已开票
- `received` - 已收款
- `cancelled` - 已取消

### 收入类型 (RevenueType)
- `milestone` - 里程碑收入
- `monthly` - 月度收入
- `quarterly` - 季度收入
- `final` - 最终收入
- `bonus` - 奖金收入
- `other` - 其他收入

## 常用查询参数
- `page` - 页码
- `pageSize` - 每页数量
- `keyword` - 关键词搜索
- `status` - 状态筛选
- `sortBy` - 排序字段
- `sortOrder` - 排序方向 (asc/desc)
- `startDate` - 开始日期
- `endDate` - 结束日期

## 响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

## 错误码
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `422` - 数据验证失败
- `500` - 服务器错误
