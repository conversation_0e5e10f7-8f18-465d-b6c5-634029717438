<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉审批附件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>钉钉审批附件上传测试</h1>
    
    <!-- JWT Token 输入 -->
    <div class="form-group">
        <label for="token">JWT Token:</label>
        <input type="text" id="token" placeholder="请输入您的JWT Token">
        <small>从登录接口获取的JWT Token</small>
    </div>

    <!-- 方式一：单独上传附件 -->
    <h2>方式一：单独上传附件到钉钉媒体库</h2>
    <form id="uploadForm">
        <div class="form-group">
            <label for="uploadType">文件类型:</label>
            <select id="uploadType" name="type">
                <option value="attachment">附件</option>
                <option value="invoice">发票</option>
            </select>
        </div>
        <div class="form-group">
            <label for="uploadFiles">选择文件:</label>
            <input type="file" id="uploadFiles" name="file" multiple accept=".pdf,.doc,.docx,.jpg,.png,.txt">
        </div>
        <button type="submit">上传到钉钉媒体库</button>
    </form>

    <!-- 方式二：一次性上传并发起审批 -->
    <h2>方式二：一次性上传附件并发起审批</h2>
    <form id="approvalForm">
        <div class="form-group">
            <label for="weeklyBudgetId">周预算ID:</label>
            <input type="text" id="weeklyBudgetId" name="weeklyBudgetId" required placeholder="例如: budget_123">
        </div>
        <div class="form-group">
            <label for="totalAmount">付款总额:</label>
            <input type="number" id="totalAmount" name="totalAmount" required placeholder="例如: 10000">
        </div>
        <div class="form-group">
            <label for="paymentReason">付款事由:</label>
            <input type="text" id="paymentReason" name="paymentReason" required placeholder="例如: 项目费用支付">
        </div>
        <div class="form-group">
            <label for="department">申请部门:</label>
            <input type="number" id="department" name="department" value="1" required>
        </div>
        <div class="form-group">
            <label for="contractEntity">合同签署主体:</label>
            <select id="contractEntity" name="contractEntity">
                <option value="company_a">公司A</option>
                <option value="company_b">公司B</option>
                <option value="subsidiary">子公司</option>
                <option value="other">其他</option>
            </select>
        </div>
        <div class="form-group">
            <label for="expectedPaymentDate">期望付款时间:</label>
            <input type="date" id="expectedPaymentDate" name="expectedPaymentDate">
        </div>
        <div class="form-group">
            <label for="paymentMethod">付款方式:</label>
            <select id="paymentMethod" name="paymentMethod">
                <option value="bank_transfer">银行转账</option>
                <option value="online_payment">在线支付</option>
                <option value="check">支票</option>
                <option value="cash">现金</option>
                <option value="other">其他</option>
            </select>
        </div>
        <div class="form-group">
            <label for="receivingAccount">收款账号信息 (JSON格式):</label>
            <textarea id="receivingAccount" name="receivingAccount" rows="3" placeholder='{"accountName":"收款方","accountNumber":"*********","bankName":"工商银行"}'></textarea>
        </div>
        <div class="form-group">
            <label for="remark">备注:</label>
            <textarea id="remark" name="remark" rows="2" placeholder="备注信息"></textarea>
        </div>
        <div class="form-group">
            <label for="approvalFiles">选择附件:</label>
            <input type="file" id="approvalFiles" name="file" multiple accept=".pdf,.doc,.docx,.jpg,.png,.txt">
        </div>
        <button type="submit">发起带附件的审批</button>
    </form>

    <div id="result"></div>

    <script>
        // 显示结果
        function showResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
        }

        // 获取Token
        function getToken() {
            const token = document.getElementById('token').value.trim();
            if (!token) {
                showResult('请先输入JWT Token', false);
                return null;
            }
            return token;
        }

        // 方式一：单独上传附件
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const token = getToken();
            if (!token) return;

            const formData = new FormData();
            const files = document.getElementById('uploadFiles').files;
            const type = document.getElementById('uploadType').value;

            if (files.length === 0) {
                showResult('请选择要上传的文件', false);
                return;
            }

            // 添加文件到FormData
            for (let i = 0; i < files.length; i++) {
                formData.append('file', files[i]);
            }

            try {
                showResult('正在上传文件到钉钉媒体库...');
                
                const response = await fetch(`/api/approvals/upload/${type}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showResult(`上传成功！\n\n媒体ID列表: ${result.data.mediaIds.join(', ')}\n\n详细信息:\n${JSON.stringify(result.data, null, 2)}`);
                } else {
                    showResult(`上传失败: ${result.message}`, false);
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, false);
            }
        });

        // 方式二：一次性上传并发起审批
        document.getElementById('approvalForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const token = getToken();
            if (!token) return;

            const formData = new FormData(e.target);
            const files = document.getElementById('approvalFiles').files;

            if (files.length === 0) {
                showResult('请选择要上传的附件', false);
                return;
            }

            try {
                showResult('正在发起审批并上传附件...');
                
                const response = await fetch('/api/approvals/payment-with-files', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showResult(`审批发起成功！\n\n审批实例ID: ${result.data.processInstanceId}\n审批ID: ${result.data.approvalId}\n上传文件数: ${result.data.uploadedFiles}\n\n请到钉钉中查看审批详情`);
                } else {
                    showResult(`发起审批失败: ${result.message}`, false);
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, false);
            }
        });

        // 设置默认日期为今天
        document.getElementById('expectedPaymentDate').value = new Date().toISOString().split('T')[0];
    </script>
</body>
</html>
