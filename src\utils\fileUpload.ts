import { FastifyRequest } from 'fastify';

// 文件上传辅助函数
export class FileUploadHelper {
  
  /**
   * 处理多文件上传
   */
  static async handleMultipleFileUpload(request: FastifyRequest): Promise<string[]> {
    const files = request.files();
    const uploadedUrls: string[] = [];

    for await (const file of files) {
      try {
        // 这里应该调用实际的文件上传服务
        // 暂时返回模拟的URL
        const filename = `${Date.now()}_${file.filename}`;
        const url = `/uploads/${filename}`;
        uploadedUrls.push(url);
        
        console.log(`文件上传成功: ${file.filename} -> ${url}`);
      } catch (error) {
        console.error(`文件上传失败: ${file.filename}`, error);
        throw new Error(`文件上传失败: ${file.filename}`);
      }
    }

    return uploadedUrls;
  }

  /**
   * 验证文件类型
   */
  static validateFileType(filename: string, allowedTypes: string[]): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return allowedTypes.includes(extension || '');
  }

  /**
   * 验证文件大小
   */
  static validateFileSize(size: number, maxSizeInMB: number): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return size <= maxSizeInBytes;
  }

  /**
   * 获取允许的发票文件类型
   */
  static getAllowedInvoiceTypes(): string[] {
    return ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx', 'xls', 'xlsx'];
  }

  /**
   * 获取允许的附件文件类型
   */
  static getAllowedAttachmentTypes(): string[] {
    return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'zip', 'rar'];
  }

  /**
   * 验证发票文件
   */
  static validateInvoiceFile(filename: string, size: number): { valid: boolean; error?: string } {
    if (!this.validateFileType(filename, this.getAllowedInvoiceTypes())) {
      return {
        valid: false,
        error: `不支持的发票文件类型，支持的类型: ${this.getAllowedInvoiceTypes().join(', ')}`
      };
    }

    if (!this.validateFileSize(size, 10)) { // 10MB限制
      return {
        valid: false,
        error: '发票文件大小不能超过10MB'
      };
    }

    return { valid: true };
  }

  /**
   * 验证附件文件
   */
  static validateAttachmentFile(filename: string, size: number): { valid: boolean; error?: string } {
    if (!this.validateFileType(filename, this.getAllowedAttachmentTypes())) {
      return {
        valid: false,
        error: `不支持的附件文件类型，支持的类型: ${this.getAllowedAttachmentTypes().join(', ')}`
      };
    }

    if (!this.validateFileSize(size, 20)) { // 20MB限制
      return {
        valid: false,
        error: '附件文件大小不能超过20MB'
      };
    }

    return { valid: true };
  }
}

// 文件上传相关的类型定义
export interface FileUploadResult {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
  uploadedAt: Date;
  uploadedBy: string;
}

export interface FileValidationResult {
  valid: boolean;
  error?: string;
}

// 文件类型枚举
export enum FileCategory {
  INVOICE = 'invoice',
  ATTACHMENT = 'attachment',
  CONTRACT = 'contract',
  OTHER = 'other'
}
