import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { ApprovalService } from '../services/approval.js';
import {
  ApprovalStatus,
  CreateApprovalRequest
} from '../types/approval.js';
import { FileUploadHelper } from '../utils/fileUpload.js';

export class ApprovalController {
  private approvalService: ApprovalService;

  constructor() {
    this.approvalService = new ApprovalService();
  }

  /**
   * 发起对公付款审批
   */
  async createPaymentApproval(request: FastifyRequest, reply: FastifyReply) {
    try {
      const createApprovalSchema = z.object({
        weeklyBudgetId: z.string().min(1, '周预算ID不能为空'),
        totalAmount: z.number().min(0.01, '付款总额必须大于0'),
        paymentReason: z.string().min(1, '付款事由不能为空'),
        department: z.number().min(1, '申请部门不能为空'),
        contractEntity: z.string().optional(),
        expectedPaymentDate: z.string().optional(),
        paymentMethod: z.string().optional(),
        receivingAccount: z.object({
          accountName: z.string().min(1, '账户名称不能为空'),
          accountNumber: z.string().min(1, '账号不能为空'),
          bankName: z.string().min(1, '开户银行不能为空'),
          bankCode: z.string().optional()
        }).optional(),
        relatedApprovalId: z.string().optional(),
        invoiceFiles: z.array(z.string()).optional(),
        attachments: z.array(z.string()).optional(),
        remark: z.string().optional(),
        // 兼容旧版本字段
        approvalAmount: z.number().optional(),
        reason: z.string().optional()
      });

      const requestData = createApprovalSchema.parse(request.body);

      // 从JWT认证中间件获取当前用户信息
      const user = (request as any).user;
      if (!user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证',
          code: 'USER_NOT_AUTHENTICATED'
        });
      }

      const originatorUserId = user.userid;

      // 构建完整的审批请求对象
      const approvalRequest: CreateApprovalRequest = {
        weeklyBudgetId: requestData.weeklyBudgetId,
        department: requestData.department,
        totalAmount: requestData.totalAmount || requestData.approvalAmount || 0,
        paymentReason: requestData.paymentReason || requestData.reason || '周预算付款',
        contractEntity: (requestData.contractEntity as any) || 'company_a',
        expectedPaymentDate: (requestData.expectedPaymentDate || new Date().toISOString().split('T')[0]) as string,
        paymentMethod: (requestData.paymentMethod as any) || 'bank_transfer',
        receivingAccount: requestData.receivingAccount || {
          accountName: '待填写',
          accountNumber: '待填写',
          bankName: '待填写'
        },
        relatedApprovalId: requestData.relatedApprovalId,
        invoiceFiles: requestData.invoiceFiles,
        attachments: requestData.attachments,
        remark: requestData.remark,
        // 兼容字段
        approvalAmount: requestData.approvalAmount,
        reason: requestData.reason
      };

      const approval = await this.approvalService.createPaymentApproval(
        approvalRequest,
        originatorUserId
      );

      return reply.send({
        success: true,
        data: approval,
        message: '发起审批成功'
      });
    } catch (error) {
      console.error('发起审批失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '发起审批失败'
      });
    }
  }

  /**
   * 发起带附件的对公付款审批
   */
  async createPaymentApprovalWithFiles(request: FastifyRequest, reply: FastifyReply) {
    try {
      // 获取当前用户信息
      const user = (request as any).user;
      if (!user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证'
        });
      }

      // 处理multipart/form-data
      const data = await request.file();
      if (!data) {
        return reply.status(400).send({
          success: false,
          message: '没有上传文件'
        });
      }

      // 解析表单字段
      const fields = data.fields;
      const formData: any = {};

      // 解析所有字段
      for (const [key, value] of Object.entries(fields)) {
        if (Array.isArray(value)) {
          formData[key] = value.map((v: any) => v.value);
        } else {
          formData[key] = (value as any).value;
        }
      }

      // 验证必需字段
      if (!formData.weeklyBudgetId) {
        return reply.status(400).send({
          success: false,
          message: '周预算ID不能为空'
        });
      }

      if (!formData.totalAmount || Number(formData.totalAmount) <= 0) {
        return reply.status(400).send({
          success: false,
          message: '付款总额必须大于0'
        });
      }

      if (!formData.paymentReason) {
        return reply.status(400).send({
          success: false,
          message: '付款事由不能为空'
        });
      }

      // 收集所有上传的文件
      const files = request.files();
      const attachmentFiles: any[] = [];

      for await (const file of files) {
        attachmentFiles.push(file);
      }

      // 构建审批请求数据
      const approvalRequest = {
        weeklyBudgetId: formData.weeklyBudgetId,
        totalAmount: Number(formData.totalAmount),
        paymentReason: formData.paymentReason,
        department: Number(formData.department || 1),
        contractEntity: formData.contractEntity,
        expectedPaymentDate: formData.expectedPaymentDate,
        paymentMethod: formData.paymentMethod,
        receivingAccount: formData.receivingAccount ? JSON.parse(formData.receivingAccount) : undefined,
        relatedApprovalId: formData.relatedApprovalId,
        invoiceFiles: formData.invoiceFiles ? JSON.parse(formData.invoiceFiles) : undefined,
        attachments: formData.attachments ? JSON.parse(formData.attachments) : undefined,
        attachmentFiles: attachmentFiles.length > 0 ? attachmentFiles : undefined,
        remark: formData.remark,
        // 兼容字段
        approvalAmount: formData.approvalAmount ? Number(formData.approvalAmount) : undefined,
        reason: formData.reason
      };

      const originatorUserId = user.userid;

      const approval = await this.approvalService.createPaymentApproval(
        approvalRequest,
        originatorUserId
      );

      return reply.send({
        success: true,
        data: {
          processInstanceId: approval.processInstanceId,
          approvalId: approval.id,
          uploadedFiles: attachmentFiles.length
        },
        message: `发起审批成功${attachmentFiles.length > 0 ? `，已上传${attachmentFiles.length}个附件` : ''}`
      });
    } catch (error) {
      console.error('发起带附件审批失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '发起审批失败'
      });
    }
  }

  /**
   * 上传审批相关文件到钉钉媒体库
   */
  async uploadApprovalFiles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { type } = request.params as { type: 'invoice' | 'attachment' };
      const user = (request as any).user;

      if (!user) {
        return reply.status(401).send({
          success: false,
          message: '用户未认证'
        });
      }

      const files = request.files();
      const uploadedFiles: any[] = [];
      const fileList: any[] = [];

      // 收集所有文件
      for await (const file of files) {
        // 验证文件
        const validation = type === 'invoice'
          ? FileUploadHelper.validateInvoiceFile(file.filename, file.file.bytesRead || 0)
          : FileUploadHelper.validateAttachmentFile(file.filename, file.file.bytesRead || 0);

        if (!validation.valid) {
          return reply.status(400).send({
            success: false,
            message: validation.error
          });
        }

        fileList.push(file);
      }

      if (fileList.length === 0) {
        return reply.status(400).send({
          success: false,
          message: '没有上传文件'
        });
      }

      // 上传文件到钉钉媒体库
      const dingTalkService = this.approvalService['dingTalkService'];
      const mediaIds = await dingTalkService.uploadAttachmentFiles(fileList);

      // 构建返回数据
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        const mediaId = mediaIds[i];

        uploadedFiles.push({
          filename: file.filename,
          originalName: file.filename,
          mediaId: mediaId,
          size: file.file.bytesRead || 0,
          mimeType: file.mimetype
        });
      }

      return reply.send({
        success: true,
        data: {
          files: uploadedFiles,
          mediaIds: mediaIds
        },
        message: `${type === 'invoice' ? '发票' : '附件'}文件上传到钉钉成功`
      });
    } catch (error) {
      console.error('文件上传到钉钉失败:', error);
      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '文件上传失败'
      });
    }
  }

  /**
   * 获取审批实例列表
   */
  async getApprovalInstances(request: FastifyRequest, reply: FastifyReply) {
    try {
      const querySchema = z.object({
        page: z.string().optional().transform(val => val ? parseInt(val) : 1),
        pageSize: z.string().optional().transform(val => val ? parseInt(val) : 20),
        weeklyBudgetId: z.string().optional(),
        status: z.nativeEnum(ApprovalStatus).optional(),
        originatorUserId: z.string().optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        sortBy: z.string().optional(),
        sortOrder: z.enum(['asc', 'desc']).optional()
      });

      const params = querySchema.parse(request.query);
      const result = await this.approvalService.getApprovalInstances(params);

      return reply.send({
        success: true,
        data: result,
        message: '获取审批列表成功'
      });
    } catch (error) {
      console.error('获取审批列表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取审批列表失败'
      });
    }
  }

  /**
   * 获取单个审批实例
   */
  async getApprovalInstance(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { id } = request.params as { id: string };
      const approval = await this.approvalService.getApprovalInstance(id);

      if (!approval) {
        return reply.status(404).send({
          success: false,
          message: '审批实例不存在'
        });
      }

      return reply.send({
        success: true,
        data: approval,
        message: '获取审批详情成功'
      });
    } catch (error) {
      console.error('获取审批详情失败:', error);

      return reply.status(500).send({
        success: false,
        message: '获取审批详情失败'
      });
    }
  }

  /**
   * 同步审批状态
   */
  async syncApprovalStatus(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { processInstanceId } = request.params as { processInstanceId: string };
      const approval = await this.approvalService.syncApprovalStatus(processInstanceId);

      if (!approval) {
        return reply.status(404).send({
          success: false,
          message: '审批实例不存在'
        });
      }

      return reply.send({
        success: true,
        data: approval,
        message: '同步审批状态成功'
      });
    } catch (error) {
      console.error('同步审批状态失败:', error);

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '同步审批状态失败'
      });
    }
  }

  /**
   * 获取审批统计信息
   */
  async getApprovalStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const querySchema = z.object({
        startDate: z.string().optional(),
        endDate: z.string().optional()
      });

      const params = querySchema.parse(request.query);
      const stats = await this.approvalService.getApprovalStats(params);

      return reply.send({
        success: true,
        data: stats,
        message: '获取审批统计成功'
      });
    } catch (error) {
      console.error('获取审批统计失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '获取审批统计失败'
      });
    }
  }

  /**
   * 处理审批回调
   */
  async handleApprovalCallback(request: FastifyRequest, reply: FastifyReply) {
    try {
      const query = request.query as { signature: string; timestamp: string; nonce: string };
      const body = request.body as { encrypt: string };

      console.log('收到审批回调:', {
        signature: query.signature,
        timestamp: query.timestamp,
        nonce: query.nonce,
        hasEncrypt: !!body.encrypt
      });

      const result = await this.approvalService.handleApprovalCallback({
        signature: query.signature,
        timestamp: query.timestamp,
        nonce: query.nonce,
        encrypt: body.encrypt
      });

      if (result.success) {
        // 生成加密响应
        const encryptedResponse = this.approvalService.generateCallbackResponse({
          success: true,
          message: result.message,
          data: result.data
        });

        return reply.send(encryptedResponse);
      } else {
        // 即使失败也要返回加密响应
        const encryptedResponse = this.approvalService.generateCallbackResponse({
          success: false,
          message: result.message
        });

        return reply.status(400).send(encryptedResponse);
      }
    } catch (error) {
      console.error('处理审批回调失败:', error);

      // 生成错误的加密响应
      const encryptedResponse = this.approvalService.generateCallbackResponse({
        success: false,
        message: '服务器内部错误'
      });

      return reply.status(500).send(encryptedResponse);
    }
  }

  /**
   * 批量同步审批状态
   */
  async batchSyncApprovalStatus(request: FastifyRequest, reply: FastifyReply) {
    try {
      const batchSyncSchema = z.object({
        processInstanceIds: z.array(z.string()).min(1, '至少需要一个审批实例ID')
      });

      const { processInstanceIds } = batchSyncSchema.parse(request.body);

      const results = await Promise.allSettled(
        processInstanceIds.map(id => this.approvalService.syncApprovalStatus(id))
      );

      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failureCount = results.filter(r => r.status === 'rejected').length;

      return reply.send({
        success: true,
        data: {
          total: processInstanceIds.length,
          success: successCount,
          failure: failureCount,
          results: results.map((result, index) => ({
            processInstanceId: processInstanceIds[index],
            status: result.status,
            data: result.status === 'fulfilled' ? result.value : undefined,
            error: result.status === 'rejected' ? result.reason?.message : undefined
          }))
        },
        message: `批量同步完成，成功${successCount}个，失败${failureCount}个`
      });
    } catch (error) {
      console.error('批量同步审批状态失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: '批量同步审批状态失败'
      });
    }
  }
}
