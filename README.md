# 钉钉微H5应用后端API服务

基于 Node.js + Fastify + TypeScript 构建的钉钉微H5应用后端服务，提供钉钉API集成和用户认证功能。

## 🚀 技术栈

- **Node.js** - JavaScript运行时
- **Fastify** - 高性能Web框架
- **TypeScript** - 类型安全的JavaScript
- **Zod** - 运行时类型验证
- **Axios** - HTTP客户端
- **Pino** - 高性能日志库

## 📦 功能特性

- ✅ 钉钉免登码用户认证
- ✅ 获取用户详细信息
- ✅ JSAPI签名生成
- ✅ 部门列表获取
- ✅ 类型安全的API设计
- ✅ 环境变量验证
- ✅ 结构化日志
- ✅ CORS支持

## 🛠️ 快速开始

### 🚀 一键启动（推荐）

#### 方式一：使用内存存储（快速体验）
```bash
npm run quick-start
```

#### 方式二：使用PostgreSQL数据库（生产推荐）
```bash
# 1. 设置数据库
npm run db:setup

# 2. 启动应用
npm run dev
```

快速启动脚本会自动：
- 检查项目文件和环境配置
- 安装必要的依赖包
- 设置PostgreSQL数据库（如果选择）
- 启动开发服务器
- 运行API接口测试
- 显示访问地址和使用提示

### 📱 演示页面

启动后可以访问以下演示页面：

- **🏠 首页**: `http://localhost:3000/index.html` - 基础功能演示
- **🎨 完整功能**: `http://localhost:3000/dingtalk-design-demo.html` - 全功能API演示
- **🔐 免登录演示**: `http://localhost:3000/dingtalk-login-demo.html` - 专门的免登录流程
- **📱 官方演示**: `http://localhost:3000/official-login-demo.html` - 基于官方文档的标准实现
- **🧪 API测试**: `http://localhost:3000/test-api.html` - 后端接口测试
- **🔑 签名测试**: `http://localhost:3000/signature-test.html` - JSAPI签名测试

### 🔧 手动启动

#### 1. 安装依赖

```bash
npm install
```

#### 2. 配置环境变量

复制环境变量模板：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的钉钉应用配置：

```env
# 数据库配置 (可选，不配置则使用内存存储)
DATABASE_URL="postgresql://postgres:password@localhost:5432/project_management?schema=public"

# 钉钉应用配置
DINGTALK_APP_KEY=your_app_key_here
DINGTALK_APP_SECRET=your_app_secret_here
DINGTALK_CORP_ID=your_corp_id_here
DINGTALK_AGENT_ID=your_agent_id_here
```

#### 3. 设置数据库（可选）

如果要使用PostgreSQL数据库：

```bash
# 自动设置数据库
npm run db:setup

# 或手动设置
npm run db:migrate
npm run db:generate
```

#### 4. 启动开发服务器

```bash
npm run dev
```

服务器将在 `http://localhost:3000` 启动。

#### 5. 构建生产版本

```bash
npm run build
npm start
```

## 📚 API文档

### 健康检查

```http
GET /api/health
```

### 用户认证

#### 通过免登码获取用户信息

```http
POST /api/auth/user-info
Content-Type: application/json

{
  "authCode": "钉钉免登码"
}
```

#### 获取JSAPI签名

```http
GET /api/auth/jsapi-signature?url=当前页面URL
```

#### 获取部门列表

```http
GET /api/auth/departments
```

## 🏗️ 项目结构

```
src/
├── config/          # 配置文件
│   └── env.ts       # 环境变量配置
├── controllers/     # 控制器
│   └── auth.ts      # 认证控制器
├── routes/          # 路由定义
│   └── auth.ts      # 认证路由
├── services/        # 服务层
│   └── dingtalk.ts  # 钉钉API服务
├── types/           # 类型定义
│   └── dingtalk.ts  # 钉钉相关类型
├── utils/           # 工具函数
├── middleware/      # 中间件
└── index.ts         # 应用入口
```

## 🔧 钉钉应用配置

1. 登录钉钉开放平台：https://open.dingtalk.com/
2. 创建企业内部应用
3. 获取 AppKey、AppSecret 和 CorpId
4. 配置应用权限：
   - 通讯录只读权限
   - 身份验证
5. 配置服务器出口IP（如果需要）

## 🌐 前端集成示例

### HTML页面集成

```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script>
</head>
<body>
    <script>
        // 钉钉免登
        dd.ready(() => {
            dd.runtime.permission.requestAuthCode({
                redirection: "none",
                onSuccess: (result) => {
                    // 发送免登码到后端
                    fetch('/api/auth/user-info', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            authCode: result.code
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('用户信息:', data);
                    });
                },
                onFail: (err) => {
                    console.error('获取免登码失败:', err);
                }
            });
        });
    </script>
</body>
</html>
```

## 🔒 安全注意事项

1. **环境变量保护**：确保 `.env` 文件不被提交到版本控制
2. **CORS配置**：生产环境应配置具体的允许域名
3. **HTTPS**：生产环境必须使用HTTPS
4. **访问令牌**：自动管理token过期和刷新

## 📝 开发说明

- 使用 `tsx` 直接运行TypeScript，无需编译步骤
- 支持热重载，修改代码自动重启
- 严格的TypeScript配置，确保类型安全
- 使用Zod进行运行时参数验证

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 🧪 测试

### 🔍 API接口测试

#### 命令行测试工具
```bash
# 运行完整的API测试套件
npm run test:api

# 或直接运行
node scripts/test-api-endpoints.js

# 指定服务器地址
API_BASE_URL=http://localhost:8080 npm run test:api
```

#### 网页界面测试
访问官方演示页面，点击"🔍 检查API状态"按钮进行实时测试。

### 📊 测试覆盖
- ✅ 健康检查接口 (`/api/health`)
- ✅ 应用配置接口 (`/api/app/config`)
- ✅ JSAPI签名接口 (`/api/auth/jsapi-signature`)
- ✅ 部门列表接口 (`/api/auth/departments`)
- ✅ 用户认证接口 (`/api/auth/user-info`)
- ✅ 增强版签名接口 (`/api/app/jsapi-signature/enhanced`)
- ✅ 应用统计接口 (`/api/app/stats`)
- ✅ 参数验证和错误处理

### 🛠️ 测试工具特性
- **彩色输出**：清晰的成功/失败状态显示
- **响应时间**：每个接口的详细性能数据
- **成功率统计**：整体API健康状况
- **错误诊断**：详细的错误信息和解决建议

## 🚀 部署

### 使用Docker
```bash
# 构建镜像
docker build -t cantv-ding-api .

# 运行容器
docker run -d \
  --name cantv-ding-api \
  -p 3000:3000 \
  --env-file .env \
  cantv-ding-api
```

### 使用Docker Compose
```bash
docker-compose up -d
```

### 使用部署脚本
```bash
chmod +x deploy.sh
./deploy.sh
```

## 📚 文档

### 📖 完整文档
- [钉钉免登录完整实现指南](docs/dingtalk-login-guide.md) - 详细的使用指南
- [钉钉官方免登录实现指南](docs/official-dingtalk-login-guide.md) - 基于官方文档的标准实现
- [签名问题修复指南](docs/signature-fix-guide.md) - 签名问题排查和解决
- [页面导航使用指南](docs/navigation-guide.md) - 演示页面使用说明
- [API接口测试指南](docs/api-testing-guide.md) - API测试工具使用方法
- [项目管理使用指南](docs/project-management-guide.md) - 项目管理功能使用说明
- [数据库设置指南](docs/database-setup-guide.md) - PostgreSQL数据库配置
- [数据库快速开始](docs/database-quick-start.md) - 数据库快速上手指南
- [项目实现总结](DINGTALK_IMPLEMENTATION.md) - 完整的实现总结

### 🔌 API接口文档
- [📖 完整API接口文档](docs/api-documentation.md) - 详细的API接口说明和示例
- [⚡ API快速参考](docs/api-quick-reference.md) - 快速查阅所有接口
- [🌐 在线API文档](http://localhost:3000/api-docs.html) - 交互式API测试工具
- [📋 OpenAPI规范](docs/openapi.yaml) - Swagger/OpenAPI 3.0规范文件
- [💻 TypeScript类型定义](docs/api-types.ts) - 前端开发类型定义
- [🔧 JavaScript API客户端](docs/api-client.js) - 即用型API客户端
- [📮 Postman测试集合](docs/postman-collection.json) - API测试集合

### 🔗 在线资源
- [钉钉开放平台](https://open.dingtalk.com/) - 官方开发文档
- [API健康检查](http://localhost:3000/api/health) - 服务状态检查

## 🔧 下一步

1. **配置钉钉应用**
   - 在钉钉开放平台创建企业内部应用
   - 获取AppKey、AppSecret、CorpId
   - 配置应用权限和域名白名单

2. **部署到生产环境**
   - 申请SSL证书，配置HTTPS
   - 配置域名和DNS解析
   - 设置服务器防火墙和安全组

3. **功能扩展**
   - 添加用户权限管理
   - 集成企业业务逻辑
   - 添加数据库存储
   - 实现消息推送功能

4. **监控和日志**
   - 配置应用监控
   - 设置错误告警
   - 优化日志记录

## 📄 许可证

ISC License
