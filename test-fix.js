// 测试修复后的项目API
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

async function testProjectsFix() {
  console.log('🧪 测试项目API修复...\n');

  try {
    // 1. 测试获取项目列表
    console.log('1. 测试获取项目列表...');
    const response = await fetch(`${BASE_URL}/test/projects`);
    const result = await response.json();
    
    console.log('响应状态:', response.status);
    console.log('响应结果:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ 获取项目列表成功');
      console.log(`📊 总项目数: ${result.data.total}`);
      console.log(`📄 当前页项目数: ${result.data.projects.length}`);
      
      if (result.data.projects.length > 0) {
        const firstProject = result.data.projects[0];
        console.log('\n📋 第一个项目的数据结构:');
        
        // 检查基本信息
        console.log('✅ 基本信息:', {
          id: firstProject.id ? '✅' : '❌',
          projectName: firstProject.projectName ? '✅' : '❌',
          documentType: firstProject.documentType ? '✅' : '❌',
          status: firstProject.status ? '✅' : '❌'
        });
        
        // 检查品牌信息
        console.log('✅ 品牌信息:', firstProject.brand ? {
          id: firstProject.brand.id ? '✅' : '❌',
          name: firstProject.brand.name ? '✅' : '❌'
        } : '❌ 品牌信息缺失');
        
        // 检查执行PM信息
        console.log('✅ 执行PM信息:', firstProject.executorPMInfo ? {
          userid: firstProject.executorPMInfo.userid ? '✅' : '❌',
          name: firstProject.executorPMInfo.name ? '✅' : '❌',
          department: firstProject.executorPMInfo.department ? '✅' : '❌'
        } : '❌ 执行PM信息缺失');
        
        // 检查内容媒介信息
        console.log('✅ 内容媒介信息:', firstProject.contentMediaInfo ? 
          `✅ ${firstProject.contentMediaInfo.length} 个内容媒介` : 
          '❌ 内容媒介信息缺失');
        
        // 检查预算信息
        console.log('✅ 预算信息:', firstProject.budget ? {
          planningBudget: typeof firstProject.budget.planningBudget === 'number' ? '✅' : '❌',
          influencerBudget: typeof firstProject.budget.influencerBudget === 'number' ? '✅' : '❌',
          adBudget: typeof firstProject.budget.adBudget === 'number' ? '✅' : '❌',
          otherBudget: typeof firstProject.budget.otherBudget === 'number' ? '✅' : '❌'
        } : '❌ 预算信息缺失');
        
        // 检查成本信息
        console.log('✅ 成本信息:', firstProject.cost ? {
          influencerCost: typeof firstProject.cost.influencerCost === 'number' ? '✅' : '❌',
          adCost: typeof firstProject.cost.adCost === 'number' ? '✅' : '❌',
          otherCost: typeof firstProject.cost.otherCost === 'number' ? '✅' : '❌',
          estimatedInfluencerRebate: typeof firstProject.cost.estimatedInfluencerRebate === 'number' ? '✅' : '❌'
        } : '❌ 成本信息缺失');
        
        // 检查利润信息
        console.log('✅ 利润信息:', firstProject.profit ? {
          profit: typeof firstProject.profit.profit === 'number' ? '✅' : '❌',
          grossMargin: typeof firstProject.profit.grossMargin === 'number' ? '✅' : '❌'
        } : '❌ 利润信息缺失');
        
        // 检查附件信息
        console.log('✅ 附件信息:', Array.isArray(firstProject.attachments) ? 
          `✅ ${firstProject.attachments.length} 个附件` : 
          '❌ 附件信息缺失');

        // 2. 测试获取单个项目
        console.log('\n2. 测试获取单个项目...');
        const projectResponse = await fetch(`${BASE_URL}/test/projects/${firstProject.id}`);
        const projectResult = await projectResponse.json();
        
        if (projectResult.success) {
          console.log('✅ 获取单个项目成功');
          const project = projectResult.data;
          console.log('单个项目的用户信息:', {
            executorPMInfo: project.executorPMInfo ? '✅ 存在' : '❌ 缺失',
            contentMediaInfo: project.contentMediaInfo ? `✅ ${project.contentMediaInfo.length} 个` : '❌ 缺失'
          });
        } else {
          console.log('❌ 获取单个项目失败:', projectResult.message);
        }
      } else {
        console.log('⚠️ 没有项目数据，无法测试详细信息');
      }
    } else {
      console.log('❌ 获取项目列表失败:', result.message);
    }

    // 3. 测试分页
    console.log('\n3. 测试分页功能...');
    const pageResponse = await fetch(`${BASE_URL}/test/projects?page=1&pageSize=5`);
    const pageResult = await pageResponse.json();
    
    if (pageResult.success) {
      console.log('✅ 分页功能正常');
      console.log(`📄 请求页面: ${pageResult.data.page}`);
      console.log(`📊 页面大小: ${pageResult.data.pageSize}`);
      console.log(`📈 总页数: ${pageResult.data.totalPages}`);
    } else {
      console.log('❌ 分页功能失败:', pageResult.message);
    }

    // 4. 测试过滤功能
    console.log('\n4. 测试过滤功能...');
    const filterResponse = await fetch(`${BASE_URL}/test/projects?documentType=project_initiation`);
    const filterResult = await filterResponse.json();
    
    if (filterResult.success) {
      console.log('✅ 过滤功能正常');
      console.log(`📋 过滤后项目数: ${filterResult.data.projects.length}`);
    } else {
      console.log('❌ 过滤功能失败:', filterResult.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🏁 测试完成');
}

// 运行测试
testProjectsFix();
