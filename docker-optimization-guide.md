# Docker 构建优化指南

## 🚀 已实现的优化

### 1. **Dockerfile 优化**

#### ✅ 层缓存优化
- **优化前**: 每次代码变更都会重新安装依赖
- **优化后**: 依赖文件单独复制，利用 Docker 层缓存
```dockerfile
# 优化：先复制依赖文件，利用Docker层缓存
COPY package.json pnpm-lock.yaml* ./
# 安装依赖（这层会被缓存，除非package.json改变）
RUN pnpm install --frozen-lockfile
# 最后复制源代码（避免代码变更影响依赖缓存）
COPY src ./src/
```

#### ✅ 减少镜像层数
- **优化前**: 多个 RUN 指令创建多个层
- **优化后**: 合并相关操作到单个 RUN 指令
```dockerfile
# 一次性安装系统依赖、配置npm和安装pnpm（减少层数）
RUN apk add --no-cache tzdata curl && \
    npm config set registry https://registry.npmmirror.com && \
    npm install -g pnpm@latest --registry=https://registry.npmmirror.com && \
    pnpm config set registry https://registry.npmmirror.com
```

#### ✅ 使用 frozen-lockfile
- 确保依赖版本一致性，避免重新解析依赖
- 加速安装过程

#### ✅ pnpm store prune
- 清理不必要的包缓存，减少镜像大小

### 2. **.dockerignore 优化**

#### ✅ 减少构建上下文
- 排除了 70+ 种不必要的文件和目录
- 显著减少传输到 Docker daemon 的数据量
- 加速构建上下文传输

### 3. **构建脚本优化**

#### ✅ 启用 BuildKit
```bash
export DOCKER_BUILDKIT=1
export BUILDKIT_PROGRESS=plain
```

#### ✅ 使用 BuildX（如果可用）
- 支持并行构建
- 高级缓存机制
- 跨平台构建支持

## 📊 性能提升预期

| 优化项目 | 预期提升 | 说明 |
|---------|---------|------|
| 层缓存优化 | 50-80% | 代码变更时不重新安装依赖 |
| 减少构建上下文 | 20-40% | 减少文件传输时间 |
| 合并 RUN 指令 | 10-20% | 减少层数和中间容器 |
| 使用 BuildKit | 15-30% | 并行构建和优化缓存 |
| frozen-lockfile | 10-15% | 跳过依赖解析 |

**总体预期提升**: 60-90% 的构建时间减少

## 🛠️ 使用方法

### 方法1：使用优化脚本（推荐）
```bash
# 给脚本执行权限
chmod +x docker-build-optimized.sh

# 构建镜像
./docker-build-optimized.sh

# 构建特定标签
./docker-build-optimized.sh v1.0.0

# 完全重新构建（清除缓存）
./docker-build-optimized.sh latest --no-cache

# 构建并推送到注册表
REGISTRY=your-registry.com ./docker-build-optimized.sh
```

### 方法2：直接使用 Docker 命令
```bash
# 启用 BuildKit
export DOCKER_BUILDKIT=1

# 构建镜像
docker build -t cantv-ding-backend:latest .

# 使用 BuildX（如果可用）
docker buildx build --platform linux/amd64 -t cantv-ding-backend:latest --load .
```

## 🔧 进一步优化建议

### 1. **系统级优化**

#### Docker 配置优化
```json
{
  "builder": {
    "gc": {
      "enabled": true,
      "defaultKeepStorage": "20GB"
    }
  },
  "experimental": true,
  "features": {
    "buildkit": true
  }
}
```

#### 增加 Docker 资源
- **内存**: 至少 4GB，推荐 8GB+
- **CPU**: 多核心显著提升并行构建速度
- **存储**: 使用 SSD，避免机械硬盘

### 2. **网络优化**

#### 使用国内镜像源
```bash
# npm 镜像
npm config set registry https://registry.npmmirror.com

# Docker 镜像
# 在 /etc/docker/daemon.json 中配置
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ]
}
```

### 3. **缓存策略**

#### 本地缓存
```bash
# 创建缓存目录
mkdir -p /tmp/.buildx-cache

# 使用缓存构建
docker buildx build \
  --cache-from type=local,src=/tmp/.buildx-cache \
  --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max \
  -t cantv-ding-backend:latest --load .
```

#### 远程缓存（CI/CD 环境）
```bash
# 使用注册表缓存
docker buildx build \
  --cache-from type=registry,ref=myregistry/myapp:buildcache \
  --cache-to type=registry,ref=myregistry/myapp:buildcache,mode=max \
  -t myapp:latest --push .
```

## 📈 监控构建性能

### 构建时间分析
```bash
# 启用详细输出
export BUILDKIT_PROGRESS=plain

# 分析构建步骤耗时
docker build --progress=plain -t cantv-ding-backend:latest . 2>&1 | grep -E "^#[0-9]+"
```

### 镜像大小分析
```bash
# 查看镜像层
docker history cantv-ding-backend:latest

# 分析镜像内容
docker run --rm -it wagoodman/dive cantv-ding-backend:latest
```

## 🚨 注意事项

1. **首次构建**: 仍需下载基础镜像和依赖，时间较长
2. **缓存失效**: package.json 变更会导致依赖层缓存失效
3. **磁盘空间**: 构建缓存会占用额外磁盘空间
4. **网络环境**: 网络速度直接影响下载时间

## 🎯 最佳实践

1. **开发环境**: 使用本地缓存，频繁构建
2. **CI/CD 环境**: 使用远程缓存，并行构建
3. **生产环境**: 使用多阶段构建，最小化镜像大小
4. **定期清理**: 清理不用的镜像和缓存

通过这些优化，您的 Docker 构建速度应该有显著提升！🚀
