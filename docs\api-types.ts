/**
 * 项目管理系统 API 类型定义
 * 
 * 使用方法：
 * 1. 复制此文件到你的前端项目中
 * 2. 根据需要导入相应的类型
 * 
 * 示例：
 * import { Project, Brand, CreateProjectRequest } from './api-types';
 */

// ==================== 基础类型 ====================

/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message: string;
  code?: string;
  details?: any;
}

/**
 * 分页响应结构
 */
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 分页查询参数
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

/**
 * 排序参数
 */
export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// ==================== 枚举类型 ====================

/**
 * 单据类型
 */
export enum DocumentType {
  PROJECT_INITIATION = 'project_initiation'
}

/**
 * 合同类型
 */
export enum ContractType {
  ANNUAL_FRAME = 'annual_frame',      // 年框
  QUARTERLY_FRAME = 'quarterly_frame', // 季框
  SINGLE = 'single',                  // 单次
  PO_ORDER = 'po_order',             // PO单
  JING_TASK = 'jing_task'            // 京任务
}

/**
 * 项目状态
 */
export enum ProjectStatus {
  DRAFT = 'draft',         // 草稿
  ACTIVE = 'active',       // 进行中
  COMPLETED = 'completed', // 已完成
  CANCELLED = 'cancelled'  // 已取消
}

/**
 * 品牌状态
 */
export enum BrandStatus {
  ACTIVE = 'active',       // 启用
  INACTIVE = 'inactive'    // 禁用
}

// ==================== 核心数据模型 ====================

/**
 * 用户信息
 */
export interface User {
  userid: string;
  name: string;
  avatar?: string;
  mobile?: string;
  email?: string;
  department?: string;
  position?: string;
  jobNumber?: string;
}

/**
 * 品牌
 */
export interface Brand {
  id: string;
  name: string;
  description?: string;
  logo?: string;
  status: BrandStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

/**
 * 项目预算
 */
export interface ProjectBudget {
  planningBudget: number;    // 项目规划预算
  influencerBudget: number;  // 达人预算
  adBudget: number;          // 投流预算
  otherBudget: number;       // 其他预算
}

/**
 * 项目成本
 */
export interface ProjectCost {
  influencerCost: number;              // 达人成本
  adCost: number;                      // 投流成本
  otherCost: number;                   // 其他成本
  estimatedInfluencerRebate: number;   // 预估达人返点
}

/**
 * 项目利润
 */
export interface ProjectProfit {
  profit: number;        // 项目利润 = 规划预算 - 总成本 + 返点
  grossMargin: number;   // 毛利率 = 利润 / 规划预算 * 100
}

/**
 * 项目执行周期
 */
export interface ProjectPeriod {
  startDate: string;  // 开始日期 (ISO 8601格式)
  endDate: string;    // 结束日期 (ISO 8601格式)
}

/**
 * 项目附件
 */
export interface Attachment {
  id: string;
  filename: string;      // 存储文件名
  originalName: string;  // 原始文件名
  size: number;          // 文件大小（字节）
  mimeType: string;      // MIME类型
  url: string;           // 文件访问URL
  uploadedBy: string;    // 上传者用户ID
  uploadedAt: string;    // 上传时间
}

/**
 * 项目
 */
export interface Project {
  id: string;
  documentType: DocumentType;
  brandId: string;
  brand?: Brand;                    // 关联的品牌信息
  projectName: string;
  period: ProjectPeriod;
  budget: ProjectBudget;
  cost: ProjectCost;
  profit: ProjectProfit;            // 计算得出的利润信息
  executorPM: string;               // 执行PM用户ID
  executorPMInfo?: User;            // 执行PM用户信息
  contentMediaIds: string[];        // 内容媒介用户ID列表
  contentMediaInfo?: User[];        // 内容媒介用户信息列表
  contractType: ContractType;
  settlementRules: string;          // 项目结算规则（富文本）
  kpi: string;                      // KPI（富文本）

  // 财务回款信息
  expectedPaymentMonth?: string;    // 预计回款月份 (YYYY-MM格式，如2024-03)
  paymentTermDays?: number;         // 账期天数 (如180表示T+180)

  attachments: Attachment[];        // 项目附件列表
  status: ProjectStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

// ==================== 请求参数类型 ====================

/**
 * 品牌查询参数
 */
export interface BrandQueryParams extends PaginationParams, SortParams {
  status?: BrandStatus;
  keyword?: string;
}

/**
 * 项目查询参数
 */
export interface ProjectQueryParams extends PaginationParams, SortParams {
  documentType?: DocumentType;
  brandId?: string;
  contractType?: ContractType;
  executorPM?: string;
  status?: ProjectStatus;
  keyword?: string;
  startDate?: string;  // YYYY-MM-DD格式
  endDate?: string;    // YYYY-MM-DD格式
}

/**
 * 创建品牌请求
 */
export interface CreateBrandRequest {
  name: string;
  description?: string;
  logo?: string;
}

/**
 * 更新品牌请求
 */
export interface UpdateBrandRequest {
  id: string;
  name?: string;
  description?: string;
  logo?: string;
  status?: BrandStatus;
}

/**
 * 创建项目请求
 */
export interface CreateProjectRequest {
  documentType: DocumentType;
  brandId: string;
  projectName: string;
  period: ProjectPeriod;
  budget: ProjectBudget;
  cost: ProjectCost;
  executorPM: string;
  contentMediaIds: string[];
  contractType: ContractType;
  settlementRules: string;
  kpi: string;
}

/**
 * 更新项目请求
 */
export interface UpdateProjectRequest {
  id: string;
  documentType?: DocumentType;
  brandId?: string;
  projectName?: string;
  period?: ProjectPeriod;
  budget?: Partial<ProjectBudget>;
  cost?: Partial<ProjectCost>;
  executorPM?: string;
  contentMediaIds?: string[];
  contractType?: ContractType;
  settlementRules?: string;
  kpi?: string;
  status?: ProjectStatus;
}

// ==================== 响应类型 ====================

/**
 * 品牌列表响应
 */
export interface BrandListResponse extends PaginatedResponse<Brand> {
  brands: Brand[];
}

/**
 * 项目列表响应
 */
export interface ProjectListResponse extends PaginatedResponse<Project> {
  projects: Project[];
}

/**
 * 文件上传响应
 */
export interface FileUploadResponse {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
}

/**
 * 按品牌统计
 */
export interface ProjectStatsByBrand {
  brandId: string;
  brandName: string;
  count: number;
  totalBudget: number;
}

/**
 * 按合同类型统计
 */
export interface ProjectStatsByContract {
  contractType: ContractType;
  count: number;
  totalBudget: number;
}

/**
 * 项目统计数据
 */
export interface ProjectStats {
  totalProjects: number;              // 总项目数
  activeProjects: number;             // 活跃项目数
  completedProjects: number;          // 已完成项目数
  totalBudget: number;                // 总预算
  totalProfit: number;                // 总利润
  averageGrossMargin: number;         // 平均毛利率
  projectsByBrand: ProjectStatsByBrand[];      // 按品牌统计
  projectsByContractType: ProjectStatsByContract[];  // 按合同类型统计
}

// ==================== API客户端接口 ====================

/**
 * API客户端接口定义
 */
export interface IProjectManagementAPI {
  // 品牌管理
  getBrands(params?: BrandQueryParams): Promise<BrandListResponse>;
  getBrand(id: string): Promise<Brand>;
  createBrand(data: CreateBrandRequest): Promise<Brand>;
  updateBrand(data: UpdateBrandRequest): Promise<Brand>;
  deleteBrand(id: string): Promise<void>;

  // 项目管理
  getProjects(params?: ProjectQueryParams): Promise<ProjectListResponse>;
  getProject(id: string): Promise<Project>;
  createProject(data: CreateProjectRequest): Promise<Project>;
  updateProject(data: UpdateProjectRequest): Promise<Project>;
  deleteProject(id: string): Promise<void>;

  // 统计分析
  getProjectStats(): Promise<ProjectStats>;

  // 文件上传
  uploadFile(file: File): Promise<FileUploadResponse>;
}

// ==================== 工具类型 ====================

/**
 * 深度可选类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 选择特定字段
 */
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P];
};

/**
 * 排除特定字段
 */
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

/**
 * 项目创建表单数据（排除计算字段）
 */
export type ProjectFormData = Omit<CreateProjectRequest, 'profit'>;

/**
 * 品牌选项（用于下拉框）
 */
export type BrandOption = Pick<Brand, 'id' | 'name' | 'status'>;

/**
 * 项目摘要（用于列表显示）
 */
export type ProjectSummary = Pick<Project, 
  'id' | 'projectName' | 'brandId' | 'status' | 'createdAt' | 'updatedAt'
> & {
  brand?: Pick<Brand, 'name'>;
  totalBudget: number;
  totalProfit: number;
};

// ==================== 常量定义 ====================

/**
 * 合同类型选项
 */
export const CONTRACT_TYPE_OPTIONS = [
  { value: ContractType.ANNUAL_FRAME, label: '年框' },
  { value: ContractType.QUARTERLY_FRAME, label: '季框' },
  { value: ContractType.SINGLE, label: '单次' },
  { value: ContractType.PO_ORDER, label: 'PO单' },
  { value: ContractType.JING_TASK, label: '京任务' }
] as const;

/**
 * 项目状态选项
 */
export const PROJECT_STATUS_OPTIONS = [
  { value: ProjectStatus.DRAFT, label: '草稿', color: '#6c757d' },
  { value: ProjectStatus.ACTIVE, label: '进行中', color: '#28a745' },
  { value: ProjectStatus.COMPLETED, label: '已完成', color: '#007bff' },
  { value: ProjectStatus.CANCELLED, label: '已取消', color: '#dc3545' }
] as const;

/**
 * 品牌状态选项
 */
export const BRAND_STATUS_OPTIONS = [
  { value: BrandStatus.ACTIVE, label: '启用', color: '#28a745' },
  { value: BrandStatus.INACTIVE, label: '禁用', color: '#6c757d' }
] as const;

/**
 * 单据类型选项
 */
export const DOCUMENT_TYPE_OPTIONS = [
  { value: DocumentType.PROJECT_INITIATION, label: '项目立项表' }
] as const;

// ==================== 验证函数 ====================

/**
 * 验证项目预算数据
 */
export function validateProjectBudget(budget: ProjectBudget): string[] {
  const errors: string[] = [];
  
  if (budget.planningBudget <= 0) {
    errors.push('项目规划预算必须大于0');
  }
  
  if (budget.influencerBudget < 0) {
    errors.push('达人预算不能为负数');
  }
  
  if (budget.adBudget < 0) {
    errors.push('投流预算不能为负数');
  }
  
  if (budget.otherBudget < 0) {
    errors.push('其他预算不能为负数');
  }
  
  const totalBudget = budget.influencerBudget + budget.adBudget + budget.otherBudget;
  if (totalBudget > budget.planningBudget) {
    errors.push('各项预算之和不能超过项目规划预算');
  }
  
  return errors;
}

/**
 * 验证项目成本数据
 */
export function validateProjectCost(cost: ProjectCost): string[] {
  const errors: string[] = [];
  
  if (cost.influencerCost < 0) {
    errors.push('达人成本不能为负数');
  }
  
  if (cost.adCost < 0) {
    errors.push('投流成本不能为负数');
  }
  
  if (cost.otherCost < 0) {
    errors.push('其他成本不能为负数');
  }
  
  if (cost.estimatedInfluencerRebate < 0) {
    errors.push('预估达人返点不能为负数');
  }
  
  return errors;
}

/**
 * 计算项目利润
 */
export function calculateProjectProfit(
  budget: ProjectBudget, 
  cost: ProjectCost
): ProjectProfit {
  const totalCost = cost.influencerCost + cost.adCost + cost.otherCost;
  const profit = budget.planningBudget - totalCost + cost.estimatedInfluencerRebate;
  const grossMargin = budget.planningBudget > 0 ? (profit / budget.planningBudget) * 100 : 0;
  
  return {
    profit: Math.round(profit * 100) / 100,
    grossMargin: Math.round(grossMargin * 100) / 100
  };
}
