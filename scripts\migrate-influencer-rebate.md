# 达人返点数据结构迁移方案

## 问题描述

当前系统中 `estimatedInfluencerRebate`（达人返点）被错误地放在了 `cost`（成本）对象中，但从业务逻辑上来说，达人返点应该算作收入而不是成本。

## 当前结构

```typescript
interface ProjectCost {
  influencerCost: number;      // 达人成本
  adCost: number;             // 投流成本
  otherCost: number;          // 其他成本
  estimatedInfluencerRebate: number; // ❌ 错误：返点不应该在成本中
}
```

## 目标结构

```typescript
interface ProjectCost {
  influencerCost: number;      // 达人成本
  adCost: number;             // 投流成本
  otherCost: number;          // 其他成本
}

interface ProjectIncome {
  estimatedInfluencerRebate: number; // ✅ 正确：返点应该在收入中
}

interface Project {
  // ... 其他字段
  cost: ProjectCost;
  income: ProjectIncome;
  // ... 其他字段
}
```

## 迁移步骤

### 1. 数据库迁移

由于数据库schema中 `estimatedInfluencerRebate` 是单独的字段，不需要修改数据库结构，只需要修改应用层的数据映射。

### 2. 类型定义更新

- [x] 更新 `ProjectCost` 接口，移除 `estimatedInfluencerRebate`
- [x] 创建 `ProjectIncome` 接口，包含 `estimatedInfluencerRebate`
- [x] 更新 `Project` 接口，添加 `income` 字段
- [x] 更新 `CreateProjectRequest` 和 `UpdateProjectRequest` 接口

### 3. 业务逻辑更新

- [ ] 更新利润计算方法，使用 `income.estimatedInfluencerRebate`
- [ ] 更新项目创建逻辑
- [ ] 更新项目更新逻辑
- [ ] 更新数据库映射逻辑
- [ ] 更新财务报表逻辑
- [ ] 更新API路由验证

### 4. 前端接口更新

- [ ] 更新API路由的请求验证schema
- [ ] 更新Swagger文档
- [ ] 更新API客户端代码

### 5. 测试更新

- [ ] 更新单元测试
- [ ] 更新集成测试
- [ ] 验证数据迁移正确性

## 利润计算公式

### 当前公式（正确）
```
项目利润 = 项目规划预算 - (达人成本 + 投流成本 + 其他成本) + 预估达人返点
```

### 新的代码实现
```typescript
// 之前
const profit = budget.planningBudget - totalCost + cost.estimatedInfluencerRebate;

// 之后
const profit = budget.planningBudget - totalCost + income.estimatedInfluencerRebate;
```

## 兼容性考虑

为了确保平滑迁移，可以考虑：

1. **临时兼容性方法**：在数据映射时同时支持新旧结构
2. **渐进式迁移**：先更新后端，再更新前端
3. **数据验证**：确保迁移后的数据计算结果一致

## 业务价值

1. **语义清晰**：达人返点作为收入更符合业务逻辑
2. **财务准确**：成本和收入分类更加准确
3. **报表清晰**：财务报表中成本和收入项目更加明确
4. **扩展性好**：未来可以更容易地添加其他收入类型

## 风险评估

- **低风险**：数据库结构不变，只是应用层重构
- **测试充分**：需要充分测试确保计算逻辑正确
- **向后兼容**：可以通过适配器模式保持API兼容性

## 实施建议

1. 先完成后端重构和测试
2. 确保所有计算结果与重构前一致
3. 更新API文档和前端代码
4. 部署后进行全面验证
