# -------------------------
# 第一阶段：安装 + 构建
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS builder

# 1) 切换到 Aliyun APK 源，更新索引；配置 npm 镜像源并安装 pnpm
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && apk update \
  && npm config set registry=https://registry.npmmirror.com \
  && npm install -g pnpm --registry=https://registry.npmmirror.com

WORKDIR /app

# 2) 安装所有依赖
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

# 3) 生成 Prisma 客户端 & 构建
COPY prisma ./prisma
RUN pnpm db:generate

COPY . .
RUN pnpm run build

# -------------------------
# 第二阶段：运行时环境
# -------------------------
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/library/node:20-alpine AS runner

# 1) 同样切换到 Aliyun APK 源并安装必要包
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && apk update \
  && apk add --no-cache tzdata curl

WORKDIR /app

# 2) 安装生产依赖
COPY package.json pnpm-lock.yaml ./
RUN npm config set registry=https://registry.npmmirror.com \
  && npm install -g pnpm --registry=https://registry.npmmirror.com \
  && pnpm install --prod

# 2.1) 复制 Prisma schema 并生成客户端
COPY prisma ./prisma/
RUN pnpm db:generate

# 3) 复制构建产物
COPY --from=builder /app/dist ./dist

# 4) 复制启动脚本和环境变量
COPY start.sh ./
COPY .env ./
COPY .env.production ./

# 5) 创建非 root 用户并修正权限
RUN addgroup -S nodejs \
  && adduser -S -G nodejs nodejs \
  && chown -R nodejs:nodejs /app \
  && chmod +x start.sh

USER nodejs

# 6) 暴露端口 & 健康检查 & 环境变量
EXPOSE 3000
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

ENV NODE_ENV=production \
  TZ=Asia/Shanghai

# 启动命令
CMD ["./start.sh"]
