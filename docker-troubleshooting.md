# Docker 构建故障排除指南

## 🚨 常见错误及解决方案

### 1. `pnpm install --frozen-lockfile` 失败

#### 错误信息
```
error: failed to solve: process "/bin/sh -c pnpm install --frozen-lockfile" did not complete successfully: exit code: 1
```

#### 原因分析
- `package.json` 和 `pnpm-lock.yaml` 不同步
- 缺少某些依赖项
- pnpm 版本不兼容
- 网络问题导致下载失败

#### 解决方案

##### 方案1：使用 `--prefer-frozen-lockfile`（推荐）
```dockerfile
# 替换
RUN pnpm install --frozen-lockfile

# 为
RUN pnpm install --prefer-frozen-lockfile
```

##### 方案2：添加回退机制
```dockerfile
RUN pnpm install --prefer-frozen-lockfile || \
    (echo "=== 安装失败，尝试不使用 lockfile ===" && pnpm install)
```

##### 方案3：本地修复依赖
```bash
# 删除 lock 文件并重新生成
rm pnpm-lock.yaml
pnpm install

# 或者更新依赖
pnpm update
```

### 2. 网络超时问题

#### 错误信息
```
ERR_PNPM_FETCH_404  GET https://registry.npmjs.org/xxx: Not Found - 404
```

#### 解决方案
```dockerfile
# 使用国内镜像源
RUN npm config set registry https://registry.npmmirror.com && \
    pnpm config set registry https://registry.npmmirror.com
```

### 3. 权限问题

#### 错误信息
```
EACCES: permission denied
```

#### 解决方案
```dockerfile
# 确保用户权限正确
RUN chown -R nodejs:nodejs /app && \
    chmod +x /app/start.sh
USER nodejs
```

### 4. 内存不足

#### 错误信息
```
JavaScript heap out of memory
```

#### 解决方案
```dockerfile
# 增加 Node.js 内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"
```

## 🔧 调试技巧

### 1. 启用详细日志
```dockerfile
# 在构建阶段添加调试信息
RUN echo "=== 检查文件 ===" && \
    ls -la && \
    echo "=== package.json 内容 ===" && \
    head -20 package.json && \
    echo "=== pnpm 版本 ===" && \
    pnpm --version
```

### 2. 分步构建
```bash
# 逐步构建，定位问题
docker build --target builder -t debug-builder .
docker run --rm -it debug-builder sh
```

### 3. 检查构建上下文
```bash
# 检查 .dockerignore 是否正确
cat .dockerignore

# 检查构建上下文大小
du -sh .
```

## 🛠️ 快速修复脚本

### 修复依赖问题
```bash
#!/bin/bash
echo "🔧 修复 Docker 构建依赖问题..."

# 1. 清理 pnpm 缓存
pnpm store prune

# 2. 删除 node_modules 和 lock 文件
rm -rf node_modules pnpm-lock.yaml

# 3. 重新安装依赖
pnpm install

# 4. 验证依赖
pnpm list

echo "✅ 依赖修复完成，可以重新构建 Docker 镜像"
```

### 清理 Docker 缓存
```bash
#!/bin/bash
echo "🧹 清理 Docker 构建缓存..."

# 清理构建缓存
docker builder prune -a -f

# 清理未使用的镜像
docker image prune -a -f

# 清理系统
docker system prune -a -f

echo "✅ Docker 缓存清理完成"
```

## 📋 构建前检查清单

- [ ] `package.json` 和 `pnpm-lock.yaml` 同步
- [ ] 所有依赖都能正常安装
- [ ] `.dockerignore` 文件配置正确
- [ ] 网络连接正常
- [ ] Docker 有足够的内存和磁盘空间
- [ ] 基础镜像可以正常拉取

## 🔍 常用调试命令

### 检查依赖
```bash
# 检查依赖是否有问题
pnpm audit

# 检查过期依赖
pnpm outdated

# 验证 lockfile
pnpm install --frozen-lockfile --dry-run
```

### 检查 Docker 环境
```bash
# 检查 Docker 版本
docker --version

# 检查可用空间
docker system df

# 检查运行中的容器
docker ps -a
```

### 测试构建
```bash
# 测试构建（不使用缓存）
docker build --no-cache -t test-build .

# 测试特定阶段
docker build --target builder -t test-builder .

# 交互式调试
docker run --rm -it test-builder sh
```

## 🚀 优化建议

### 1. 使用多阶段构建
```dockerfile
# 构建阶段
FROM node:20-alpine AS builder
# ... 构建逻辑

# 运行阶段
FROM node:20-alpine AS runner
# ... 运行逻辑
```

### 2. 优化层缓存
```dockerfile
# 先复制依赖文件
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

# 后复制源代码
COPY . .
RUN pnpm build
```

### 3. 减少镜像大小
```dockerfile
# 清理缓存
RUN pnpm install && pnpm store prune

# 使用 alpine 镜像
FROM node:20-alpine
```

## 📞 获取帮助

如果问题仍然存在，请：

1. **收集错误信息**：完整的错误日志
2. **检查环境**：Docker 版本、系统信息
3. **提供上下文**：Dockerfile、package.json、构建命令
4. **尝试简化**：最小化复现问题的 Dockerfile

记住：大多数 Docker 构建问题都与依赖管理、网络连接或权限相关。按照这个指南逐步排查，通常能快速解决问题！🎯
