import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { ApprovalService } from '../services/approval.js';
import { dingTalkEncryptService } from '../services/dingtalk-encrypt.js';

export async function dingTalkCallbackRoutes(fastify: FastifyInstance) {
  const approvalService = new ApprovalService();

  // 🎯 统一回调入口 - 钉钉后台配置这个地址即可
  fastify.post('/dingtalk/callback', {
    schema: {
      description: '钉钉统一回调入口（推荐使用）',
      tags: ['DingTalk'],
      querystring: {
        type: 'object',
        properties: {
          msg_signature: { type: 'string' },
          timestamp: { type: 'string' },
          nonce: { type: 'string' }
        },
        required: ['msg_signature', 'timestamp', 'nonce']
      },
      body: {
        type: 'object',
        properties: {
          encrypt: { type: 'string' }
        },
        required: ['encrypt']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            msg_signature: { type: 'string' },
            timeStamp: { type: 'string' },
            nonce: { type: 'string' },
            encrypt: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { msg_signature: string; timestamp: string; nonce: string };
      const body = request.body as { encrypt: string };

      console.log('🔔 收到钉钉回调:', {
        msg_signature: query.msg_signature,
        timestamp: query.timestamp,
        nonce: query.nonce,
        encrypt: body.encrypt?.substring(0, 50) + '...'
      });

      // 验证和解密回调数据
      const decryptResult = dingTalkEncryptService.processCallback({
        signature: query.msg_signature,
        timestamp: query.timestamp,
        nonce: query.nonce,
        encrypt: body.encrypt
      });

      if (!decryptResult.success) {
        console.error('❌ 回调验证失败:', decryptResult.error);
        const errorResponse = dingTalkEncryptService.generateCallbackResponse(
          JSON.stringify({
            success: false,
            message: '回调验证失败'
          })
        );
        return reply.send(errorResponse);
      }

      const callbackData = decryptResult.data;
      console.log('📨 回调数据:', callbackData);

      // 根据事件类型分发处理
      let responseData = { success: true, message: '回调处理成功' };

      switch (callbackData.EventType) {
        case 'check_url':
          // URL验证事件
          console.log('🔗 URL验证事件');
          // 对于check_url事件，直接返回"success"的加密响应
          const successResponse = dingTalkEncryptService.generateCallbackResponse('success');
          console.log('✅ URL验证成功');
          return reply.send(successResponse);

        case 'bpms_instance_change':
          // 审批实例状态变更
          console.log('📋 处理审批事件');
          const approvalResult = await approvalService.handleApprovalCallback({
            signature: query.msg_signature,
            timestamp: query.timestamp,
            nonce: query.nonce,
            encrypt: body.encrypt
          });
          responseData = approvalResult;
          break;
        case 'bpms_task_change':
          console.log('📋 处理任务事件');
          console.log('📋 处理审批事件');
          const approvalResult2 = await approvalService.handleApprovalCallback({
            signature: query.msg_signature,
            timestamp: query.timestamp,
            nonce: query.nonce,
            encrypt: body.encrypt
          });
          responseData = approvalResult2;
          break;
          break;
        case 'user_add_org':
          console.log('👤 用户加入企业事件');
          break;
        case 'user_modify_org':
          console.log('✏️ 用户信息修改事件');
          break;
        case 'user_leave_org':
          console.log('👋 用户离开企业事件');
          break;
        case 'org_admin_add':
          console.log('👑 管理员添加事件');
          break;
        case 'org_admin_remove':
          console.log('👑 管理员移除事件');
          break;
        case 'org_dept_create':
          console.log('🏢 部门创建事件');
          break;
        case 'org_dept_modify':
          console.log('🏢 部门修改事件');
          break;
        case 'org_dept_remove':
          console.log('🏢 部门删除事件');
          break;

        default:
          console.log(`❓ 未处理的事件类型: ${callbackData.EventType}`);
          responseData.message = '事件类型未处理，但回调验证成功';
      }

      // 生成加密响应
      const encryptedResponse = dingTalkEncryptService.generateCallbackResponse(JSON.stringify(responseData));

      console.log('✅ 回调处理完成:', responseData);
      return reply.send(encryptedResponse);
    } catch (error) {
      console.error('❌ 处理钉钉回调失败:', error);

      const errorResponse = dingTalkEncryptService.generateCallbackResponse(
        JSON.stringify({
          success: false,
          message: '服务器内部错误'
        })
      );

      return reply.status(500).send(errorResponse);
    }
  });

  // 🔍 回调地址验证（GET 请求）
  fastify.get('/dingtalk/callback', {
    schema: {
      description: '钉钉回调地址验证',
      tags: ['DingTalk'],
      querystring: {
        type: 'object',
        properties: {
          msg_signature: { type: 'string' },
          timestamp: { type: 'string' },
          nonce: { type: 'string' },
          echostr: { type: 'string' }
        },
        required: ['msg_signature', 'timestamp', 'nonce', 'echostr']
      },
      response: {
        200: {
          type: 'string'
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { msg_signature, timestamp, nonce, echostr } = request.query as {
        msg_signature: string;
        timestamp: string;
        nonce: string;
        echostr: string;
      };

      console.log('🔐 收到钉钉回调验证请求');

      // 验证签名
      const isValid = dingTalkEncryptService.verifySignature(msg_signature, timestamp, nonce, echostr);

      if (!isValid) {
        console.error('❌ 回调验证失败: 签名不匹配');
        return reply.status(400).send('签名验证失败');
      }

      // 解密 echostr
      const decryptedEchostr = dingTalkEncryptService.decrypt(echostr);

      if (!decryptedEchostr) {
        console.error('❌ 回调验证失败: 解密失败');
        return reply.status(400).send('解密失败');
      }

      console.log('✅ 回调验证成功');
      return reply.type('text/plain').send(decryptedEchostr);
    } catch (error) {
      console.error('❌ 处理钉钉回调验证失败:', error);
      return reply.status(500).send('服务器内部错误');
    }
  });

  // 钉钉审批回调接口
  fastify.post('/dingtalk/callback/approval', {
    schema: {
      description: '钉钉审批回调接口（加密）',
      tags: ['DingTalk'],
      querystring: {
        type: 'object',
        properties: {
          signature: { type: 'string' },
          timestamp: { type: 'string' },
          nonce: { type: 'string' }
        },
        required: ['signature', 'timestamp', 'nonce']
      },
      body: {
        type: 'object',
        properties: {
          encrypt: { type: 'string' }
        },
        required: ['encrypt']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            msg_signature: { type: 'string' },
            timeStamp: { type: 'string' },
            nonce: { type: 'string' },
            encrypt: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { signature: string; timestamp: string; nonce: string };
      const body = request.body as { encrypt: string };

      console.log('收到钉钉回调请求:', {
        signature: query.signature,
        timestamp: query.timestamp,
        nonce: query.nonce,
        encrypt: body.encrypt?.substring(0, 50) + '...'
      });

      // 处理回调
      const result = await approvalService.handleApprovalCallback({
        signature: query.signature,
        timestamp: query.timestamp,
        nonce: query.nonce,
        encrypt: body.encrypt
      });

      // 生成加密响应
      const responseData = {
        success: result.success,
        message: result.message,
        data: result.data
      };

      const encryptedResponse = approvalService.generateCallbackResponse(responseData);

      console.log('回调处理结果:', result);
      console.log('返回加密响应:', {
        msg_signature: encryptedResponse.msg_signature,
        timeStamp: encryptedResponse.timeStamp,
        nonce: encryptedResponse.nonce,
        encrypt: encryptedResponse.encrypt?.substring(0, 50) + '...'
      });

      return reply.send(encryptedResponse);
    } catch (error) {
      console.error('处理钉钉回调失败:', error);
      
      // 即使出错也要返回加密响应
      const errorResponse = approvalService.generateCallbackResponse({
        success: false,
        message: '服务器内部错误'
      });

      return reply.status(500).send(errorResponse);
    }
  });

  // 钉钉回调验证接口（GET 请求用于验证回调地址）
  fastify.get('/dingtalk/callback/approval', {
    schema: {
      description: '钉钉回调地址验证',
      tags: ['DingTalk'],
      querystring: {
        type: 'object',
        properties: {
          msg_signature: { type: 'string' },
          timestamp: { type: 'string' },
          nonce: { type: 'string' },
          echostr: { type: 'string' }
        },
        required: ['msg_signature', 'timestamp', 'nonce', 'echostr']
      },
      response: {
        200: {
          type: 'string'
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { msg_signature, timestamp, nonce, echostr } = request.query as {
        msg_signature: string;
        timestamp: string;
        nonce: string;
        echostr: string;
      };

      console.log('收到钉钉回调验证请求:', {
        msg_signature,
        timestamp,
        nonce,
        echostr: echostr.substring(0, 50) + '...'
      });

      // 验证签名
      const isValid = dingTalkEncryptService.verifySignature(msg_signature, timestamp, nonce, echostr);
      
      if (!isValid) {
        console.error('回调验证失败: 签名不匹配');
        return reply.status(400).send('签名验证失败');
      }

      // 解密 echostr
      const decryptedEchostr = dingTalkEncryptService.decrypt(echostr);
      
      if (!decryptedEchostr) {
        console.error('回调验证失败: 解密失败');
        return reply.status(400).send('解密失败');
      }

      console.log('回调验证成功，返回解密后的 echostr');
      return reply.type('text/plain').send(decryptedEchostr);
    } catch (error) {
      console.error('处理钉钉回调验证失败:', error);
      return reply.status(500).send('服务器内部错误');
    }
  });

  // 钉钉通用回调接口（处理其他类型的回调）
  fastify.post('/dingtalk/callback/general', {
    schema: {
      description: '钉钉通用回调接口',
      tags: ['DingTalk'],
      querystring: {
        type: 'object',
        properties: {
          signature: { type: 'string' },
          timestamp: { type: 'string' },
          nonce: { type: 'string' }
        },
        required: ['signature', 'timestamp', 'nonce']
      },
      body: {
        type: 'object',
        properties: {
          encrypt: { type: 'string' }
        },
        required: ['encrypt']
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = request.query as { signature: string; timestamp: string; nonce: string };
      const body = request.body as { encrypt: string };

      console.log('收到钉钉通用回调:', {
        signature: query.signature,
        timestamp: query.timestamp,
        nonce: query.nonce
      });

      // 验证和解密回调数据
      const decryptResult = dingTalkEncryptService.processCallback({
        signature: query.signature,
        timestamp: query.timestamp,
        nonce: query.nonce,
        encrypt: body.encrypt
      });

      if (!decryptResult.success) {
        console.error('通用回调验证失败:', decryptResult.error);
        const errorResponse = dingTalkEncryptService.generateCallbackResponse(
          JSON.stringify({
            success: false,
            message: '回调验证失败'
          })
        );
        return reply.send(errorResponse);
      }

      const callbackData = decryptResult.data;
      console.log('通用回调数据:', callbackData);

      // 根据事件类型处理不同的回调
      let responseData = { success: true, message: '回调处理成功' };

      switch (callbackData.EventType) {
        case 'check_url':
          console.log('URL验证事件');
          break;
        case 'user_add_org':
          console.log('用户加入企业事件');
          break;
        case 'user_modify_org':
          console.log('用户信息修改事件');
          break;
        case 'user_leave_org':
          console.log('用户离开企业事件');
          break;
        case 'org_admin_add':
          console.log('管理员添加事件');
          break;
        case 'org_admin_remove':
          console.log('管理员移除事件');
          break;
        case 'org_dept_create':
          console.log('部门创建事件');
          break;
        case 'org_dept_modify':
          console.log('部门修改事件');
          break;
        case 'org_dept_remove':
          console.log('部门删除事件');
          break;
        
        default:
          console.log(`未处理的事件类型: ${callbackData.EventType}`);
          responseData.message = '事件类型未处理，但回调验证成功';
      }

      // 生成加密响应
      const encryptedResponse = dingTalkEncryptService.generateCallbackResponse(JSON.stringify(responseData));
      return reply.send(encryptedResponse);
    } catch (error) {
      console.error('处理钉钉通用回调失败:', error);
      
      const errorResponse = dingTalkEncryptService.generateCallbackResponse(
        JSON.stringify({
          success: false,
          message: '服务器内部错误'
        })
      );

      return reply.status(500).send(errorResponse);
    }
  });

  // 回调配置测试接口
  fastify.get('/dingtalk/callback/test', {
    schema: {
      description: '测试钉钉回调配置',
      tags: ['DingTalk'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            config: {
              type: 'object',
              properties: {
                hasToken: { type: 'boolean' },
                hasAesKey: { type: 'boolean' },
                hasSuiteKey: { type: 'boolean' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const testData = { test: 'hello', timestamp: Date.now() };
      const randomStr = 'abcdefghijklmnop'; // 16位随机字符串
      const encrypted = dingTalkEncryptService.encrypt(randomStr, JSON.stringify(testData));

      let decrypted = null;
      if (encrypted) {
        decrypted = dingTalkEncryptService.decrypt(encrypted);
      }

      const isConfigValid = encrypted && decrypted && JSON.parse(decrypted).test === 'hello';

      return reply.send({
        success: isConfigValid,
        message: isConfigValid ? '回调配置正常' : '回调配置有问题',
        config: {
          hasToken: !!process.env.DINGTALK_CALLBACK_TOKEN,
          hasAesKey: !!process.env.DINGTALK_AES_KEY,
          hasSuiteKey: !!process.env.DINGTALK_SUITE_KEY
        }
      });
    } catch (error) {
      return reply.send({
        success: false,
        message: '配置测试失败: ' + (error instanceof Error ? error.message : '未知错误'),
        config: {
          hasToken: !!process.env.DINGTALK_CALLBACK_TOKEN,
          hasAesKey: !!process.env.DINGTALK_AES_KEY,
          hasSuiteKey: !!process.env.DINGTALK_SUITE_KEY
        }
      });
    }
  });
}
