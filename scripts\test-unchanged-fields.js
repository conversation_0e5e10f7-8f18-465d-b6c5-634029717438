// 测试未变更字段是否被正确排除
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testUnchangedFields() {
  try {
    console.log('🧪 测试未变更字段的处理...');
    
    // 1. 创建一个测试项目
    console.log('\n📝 创建测试项目...');
    
    // 先获取一个现有品牌ID
    const brands = await prisma.brand.findMany({ take: 1 });
    if (brands.length === 0) {
      console.log('❌ 没有找到品牌，无法测试');
      return;
    }
    
    const brandId = brands[0].id;
    
    const testProject = await prisma.project.create({
      data: {
        documentType: 'PROJECT_INITIATION',
        brandId: brandId,
        projectName: `测试未变更字段-${Date.now()}`,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        planningBudget: 100000,
        influencerBudget: 50000,
        adBudget: 30000,
        otherBudget: 20000,
        influencerCost: 45000,
        adCost: 25000,
        otherCost: 15000,
        estimatedInfluencerRebate: 5000,
        executorPM: 'test-user-001',
        contentMediaIds: ['test-user-002'],
        contractType: 'SINGLE',
        settlementRules: '测试结算规则',
        kpi: '测试KPI',
        createdBy: 'test-user-001',
        updatedBy: 'test-user-001'
      }
    });
    
    console.log(`✅ 测试项目创建成功: ${testProject.id}`);
    console.log(`   原始 planningBudget: ${testProject.planningBudget}`);
    console.log(`   原始 influencerBudget: ${testProject.influencerBudget}`);
    
    // 2. 模拟字段对比逻辑
    console.log('\n🔍 测试字段对比逻辑...');
    
    // 模拟更新前的数据
    const beforeData = {
      projectName: testProject.projectName,
      planningBudget: testProject.planningBudget,
      influencerBudget: testProject.influencerBudget,
      adBudget: testProject.adBudget,
      otherBudget: testProject.otherBudget,
      executorPM: testProject.executorPM,
      createdAt: testProject.createdAt,
      updatedAt: testProject.updatedAt
    };
    
    // 模拟更新后的数据 - 只改变项目名称，其他字段保持不变
    const afterData = {
      projectName: `更新后的项目名称-${Date.now()}`,
      planningBudget: testProject.planningBudget, // 保持不变
      influencerBudget: testProject.influencerBudget, // 保持不变
      adBudget: testProject.adBudget, // 保持不变
      otherBudget: testProject.otherBudget, // 保持不变
      executorPM: testProject.executorPM, // 保持不变
      createdAt: testProject.createdAt,
      updatedAt: new Date() // 这个会变，但应该被排除
    };
    
    console.log('📊 数据对比:');
    console.log(`   projectName: "${beforeData.projectName}" -> "${afterData.projectName}"`);
    console.log(`   planningBudget: ${beforeData.planningBudget} -> ${afterData.planningBudget}`);
    console.log(`   influencerBudget: ${beforeData.influencerBudget} -> ${afterData.influencerBudget}`);
    console.log(`   adBudget: ${beforeData.adBudget} -> ${afterData.adBudget}`);
    console.log(`   otherBudget: ${beforeData.otherBudget} -> ${afterData.otherBudget}`);
    console.log(`   executorPM: "${beforeData.executorPM}" -> "${afterData.executorPM}"`);
    
    // 3. 应用字段对比逻辑
    const excludeFields = new Set(['id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy']);
    const changedFields = [];
    const allKeys = new Set([...Object.keys(beforeData), ...Object.keys(afterData)]);
    
    for (const key of allKeys) {
      if (excludeFields.has(key)) {
        console.log(`   🚫 排除字段: ${key}`);
        continue;
      }
      
      const beforeValue = beforeData[key];
      const afterValue = afterData[key];
      
      console.log(`   🔍 检查字段 ${key}:`);
      console.log(`      前: ${JSON.stringify(beforeValue)}`);
      console.log(`      后: ${JSON.stringify(afterValue)}`);
      
      if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        console.log(`      ✅ 字段 ${key} 发生变更`);
        changedFields.push(key);
      } else {
        console.log(`      ⚪ 字段 ${key} 未变更`);
      }
    }
    
    console.log('\n📋 变更字段汇总:');
    console.log(`   变更字段数量: ${changedFields.length}`);
    console.log(`   变更字段列表: [${changedFields.join(', ')}]`);
    
    // 4. 验证结果
    const expectedChangedFields = ['projectName'];
    const unexpectedFields = ['planningBudget', 'influencerBudget', 'adBudget', 'otherBudget', 'executorPM'];
    
    console.log('\n✅ 验证结果:');
    
    // 检查是否只包含预期的变更字段
    const hasExpectedFields = expectedChangedFields.every(field => changedFields.includes(field));
    const hasUnexpectedFields = unexpectedFields.some(field => changedFields.includes(field));
    
    if (hasExpectedFields && !hasUnexpectedFields) {
      console.log('✅ 字段对比逻辑正确：只记录了实际变更的字段');
      console.log(`   ✅ 包含预期变更字段: ${expectedChangedFields.join(', ')}`);
      console.log(`   ✅ 排除未变更字段: ${unexpectedFields.join(', ')}`);
    } else {
      console.log('❌ 字段对比逻辑有误');
      if (!hasExpectedFields) {
        console.log(`   ❌ 缺少预期变更字段: ${expectedChangedFields.filter(f => !changedFields.includes(f)).join(', ')}`);
      }
      if (hasUnexpectedFields) {
        console.log(`   ❌ 包含了未变更字段: ${unexpectedFields.filter(f => changedFields.includes(f)).join(', ')}`);
      }
    }
    
    // 5. 测试数值相等的情况
    console.log('\n🔢 测试数值相等的特殊情况...');
    
    const numericTest = {
      before: { amount: 1, count: 100, rate: 0.15 },
      after: { amount: 1, count: 100, rate: 0.15 } // 完全相同
    };
    
    const numericChangedFields = [];
    for (const key of Object.keys(numericTest.before)) {
      const beforeValue = numericTest.before[key];
      const afterValue = numericTest.after[key];
      
      console.log(`   检查数值字段 ${key}: ${beforeValue} -> ${afterValue}`);
      
      if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        numericChangedFields.push(key);
        console.log(`      ✅ 数值字段 ${key} 发生变更`);
      } else {
        console.log(`      ⚪ 数值字段 ${key} 未变更`);
      }
    }
    
    if (numericChangedFields.length === 0) {
      console.log('✅ 数值相等测试通过：相同数值不被记录为变更');
    } else {
      console.log(`❌ 数值相等测试失败：错误记录了 ${numericChangedFields.join(', ')} 为变更`);
    }
    
    // 6. 清理测试数据
    console.log('\n🧹 清理测试数据...');
    await prisma.project.delete({
      where: { id: testProject.id }
    });
    console.log('✅ 测试数据清理完成');
    
    console.log('\n🎉 未变更字段测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testUnchangedFields();
