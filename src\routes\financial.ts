import { FastifyInstance } from 'fastify';
import { FinancialController } from '../controllers/financial.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';

export async function financialRoutes(fastify: FastifyInstance) {
  const financialController = new FinancialController();

  // 获取统计数据
  fastify.get('/financial/statistics', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取统计数据：项目总数、当月总收入、今年总收入及同比数据',
      tags: ['Financial'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalProjects: { type: 'number', description: '项目总数' },
                activeProjects: { type: 'number', description: '进行中项目数' },
                completedProjects: { type: 'number', description: '已完成项目数' },
                currentMonthRevenue: {
                  type: 'object',
                  properties: {
                    amount: { type: 'number', description: '当月总收入' },
                    previousAmount: { type: 'number', description: '上月总收入' },
                    growthRate: { type: 'number', description: '环比增长率(%)' },
                    period: { type: 'string', description: '当前月份(YYYY-MM)' }
                  }
                },
                currentYearRevenue: {
                  type: 'object',
                  properties: {
                    amount: { type: 'number', description: '今年总收入' },
                    previousAmount: { type: 'number', description: '去年总收入' },
                    growthRate: { type: 'number', description: '同比增长率(%)' },
                    period: { type: 'string', description: '当前年份(YYYY)' }
                  }
                },
                generatedAt: { type: 'string', format: 'date-time', description: '生成时间' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialController.getStatistics.bind(financialController));

  // 获取品牌财务汇总报表
  fastify.get('/financial/brands/summary', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取品牌财务汇总报表',
      tags: ['Financial'],
      querystring: {
        type: 'object',
        properties: {
          brandId: { type: 'string', description: '品牌ID过滤' },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          projectStatus: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['draft', 'active', 'completed', 'cancelled']
            },
            description: '项目状态过滤'
          },
          includeCompleted: { type: 'string', enum: ['true', 'false'], description: '是否包含已完成项目' },
          includeCancelled: { type: 'string', enum: ['true', 'false'], description: '是否包含已取消项目' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                summary: {
                  type: 'object',
                  properties: {
                    totalBrands: { type: 'number' },
                    totalOrderAmount: { type: 'number' },
                    totalExecutedAmount: { type: 'number' },
                    totalEstimatedProfit: { type: 'number' },
                    totalReceivedAmount: { type: 'number' },
                    totalPaidAmount: { type: 'number' },
                    overallProfitMargin: { type: 'number' }
                  }
                },
                brands: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      brandId: { type: 'string' },
                      brandName: { type: 'string' },
                      orderAmount: { type: 'number', description: '品牌下单金额' },
                      executedAmount: { type: 'number', description: '已执行金额（成本）' },
                      executingAmount: { type: 'number', description: '执行中项目金额' },
                      estimatedProfit: { type: 'number', description: '预估毛利' },
                      estimatedProfitMargin: { type: 'number', description: '预估毛利率' },
                      receivedAmount: { type: 'number', description: '已回款' },
                      unreceivedAmount: { type: 'number', description: '未回款' },
                      paidProjectAmount: { type: 'number', description: '已支付项目金额' },
                      unpaidProjectAmount: { type: 'number', description: '未支付项目金额' },
                      remarks: { type: 'string', description: '备注字段' },
                      projectCount: { type: 'number', description: '项目数量' },
                      activeProjectCount: { type: 'number', description: '进行中项目数量' },
                      completedProjectCount: { type: 'number', description: '已完成项目数量' }
                    }
                  }
                },
                generatedAt: { type: 'string', format: 'date-time' },
                reportPeriod: {
                  type: 'object',
                  properties: {
                    startDate: { type: 'string', format: 'date-time' },
                    endDate: { type: 'string', format: 'date-time' }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialController.getBrandFinancialSummary.bind(financialController));

  // 获取品牌财务详细报表
  fastify.get('/financial/brands/:brandId/detail', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取品牌财务详细报表',
      tags: ['Financial'],
      params: {
        type: 'object',
        required: ['brandId'],
        properties: {
          brandId: { type: 'string', description: '品牌ID' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          projectStatus: {
            type: 'array',
            items: {
              type: 'string',
              enum: ['draft', 'active', 'completed', 'cancelled']
            },
            description: '项目状态过滤'
          },
          includeCompleted: { type: 'string', enum: ['true', 'false'], description: '是否包含已完成项目' },
          includeCancelled: { type: 'string', enum: ['true', 'false'], description: '是否包含已取消项目' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                brandInfo: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    description: { type: 'string' },
                    logo: { type: 'string' }
                  }
                },
                summary: {
                  type: 'object',
                  properties: {
                    brandId: { type: 'string' },
                    brandName: { type: 'string' },
                    orderAmount: { type: 'number' },
                    executedAmount: { type: 'number' },
                    executingAmount: { type: 'number' },
                    estimatedProfit: { type: 'number' },
                    estimatedProfitMargin: { type: 'number' },
                    receivedAmount: { type: 'number' },
                    unreceivedAmount: { type: 'number' },
                    paidProjectAmount: { type: 'number' },
                    unpaidProjectAmount: { type: 'number' },
                    projectCount: { type: 'number' },
                    activeProjectCount: { type: 'number' },
                    completedProjectCount: { type: 'number' }
                  }
                },
                projects: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      projectName: { type: 'string' },
                      status: { type: 'string' },
                      documentType: { type: 'string' },
                      contractType: { type: 'string' },
                      period: {
                        type: 'object',
                        properties: {
                          startDate: { type: 'string' },
                          endDate: { type: 'string' }
                        }
                      },
                      budget: {
                        type: 'object',
                        properties: {
                          planningBudget: { type: 'number' },
                          totalBudget: { type: 'number' }
                        }
                      },
                      cost: {
                        type: 'object',
                        properties: {
                          totalCost: { type: 'number' },
                          estimatedInfluencerRebate: { type: 'number' }
                        }
                      },
                      profit: {
                        type: 'object',
                        properties: {
                          profit: { type: 'number' },
                          grossMargin: { type: 'number' }
                        }
                      },
                      revenue: {
                        type: 'object',
                        properties: {
                          plannedAmount: { type: 'number' },
                          receivedAmount: { type: 'number' },
                          unreceivedAmount: { type: 'number' }
                        }
                      },
                      weeklyBudgets: {
                        type: 'object',
                        properties: {
                          totalContractAmount: { type: 'number' },
                          paidAmount: { type: 'number' },
                          unpaidAmount: { type: 'number' }
                        }
                      },
                      executorPM: { type: 'string' },
                      executorPMInfo: {
                        type: 'object',
                        properties: {
                          userid: { type: 'string' },
                          name: { type: 'string' },
                          department: { type: 'string' }
                        }
                      },
                      contentMediaIds: {
                        type: 'array',
                        items: { type: 'string' }
                      },
                      contentMediaInfo: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            userid: { type: 'string' },
                            name: { type: 'string' },
                            department: { type: 'string' }
                          }
                        }
                      }
                    }
                  }
                },
                generatedAt: { type: 'string', format: 'date-time' },
                reportPeriod: {
                  type: 'object',
                  properties: {
                    startDate: { type: 'string', format: 'date-time' },
                    endDate: { type: 'string', format: 'date-time' }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, financialController.getBrandFinancialDetail.bind(financialController));
}
