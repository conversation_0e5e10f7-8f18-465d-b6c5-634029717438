#!/usr/bin/env node

/**
 * 测试部门管理功能
 */

import { DatabaseService } from '../dist/services/database.js';
import { DepartmentSyncService } from '../dist/services/departmentSync.js';
import { DingTalkService } from '../dist/services/dingtalk.js';

async function testDepartmentFeatures() {
  console.log('🧪 开始测试部门管理功能...');
  
  const databaseService = new DatabaseService();
  const dingTalkService = new DingTalkService();
  const departmentSyncService = new DepartmentSyncService(databaseService, dingTalkService);
  
  try {
    // 1. 测试从钉钉获取部门列表（只获取第一层）
    console.log('\n📝 步骤1: 测试从钉钉获取部门列表（第一层）...');
    try {
      const dingTalkDepartments = await dingTalkService.getDepartmentList();
      console.log(`✅ 从钉钉获取到 ${dingTalkDepartments?.length || 0} 个第一层部门`);

      if (dingTalkDepartments && dingTalkDepartments.length > 0) {
        console.log('📋 第一层部门示例:');
        dingTalkDepartments.slice(0, 3).forEach(dept => {
          console.log(`  - ${dept.name} (ID: ${dept.dept_id}, 父ID: ${dept.parent_id})`);
        });
      }
    } catch (error) {
      console.error('❌ 获取钉钉部门列表失败:', error.message);
    }

    // 1.5. 测试从钉钉递归获取所有层级部门列表
    console.log('\n📝 步骤1.5: 测试从钉钉递归获取所有层级部门列表...');
    try {
      const allDingTalkDepartments = await dingTalkService.getAllDepartments();
      console.log(`✅ 从钉钉递归获取到 ${allDingTalkDepartments?.length || 0} 个部门（包含所有层级）`);

      if (allDingTalkDepartments && allDingTalkDepartments.length > 0) {
        console.log('📋 所有层级部门示例:');
        allDingTalkDepartments.slice(0, 5).forEach(dept => {
          console.log(`  - ${dept.name} (ID: ${dept.dept_id}, 父ID: ${dept.parent_id})`);
        });
      }
    } catch (error) {
      console.error('❌ 递归获取钉钉部门列表失败:', error.message);
    }
    
    // 2. 测试部门同步
    console.log('\n📝 步骤2: 测试部门同步...');
    try {
      const syncResult = await departmentSyncService.syncAllDepartments();
      console.log(`✅ 部门同步完成: 成功 ${syncResult.success}, 失败 ${syncResult.failed}`);
      
      if (syncResult.errors.length > 0) {
        console.log('⚠️ 同步错误:');
        syncResult.errors.slice(0, 3).forEach(error => {
          console.log(`  - 部门 ${error.deptId}: ${error.error}`);
        });
      }
    } catch (error) {
      console.error('❌ 部门同步失败:', error.message);
    }
    
    // 3. 测试获取所有部门
    console.log('\n📝 步骤3: 测试获取所有部门...');
    try {
      const allDepartments = await departmentSyncService.getAllDepartments();
      console.log(`✅ 获取到 ${allDepartments.length} 个部门`);
      
      if (allDepartments.length > 0) {
        console.log('📋 部门列表示例:');
        allDepartments.slice(0, 5).forEach(dept => {
          console.log(`  - ${dept.name} (ID: ${dept.deptId}, 路径: ${dept.path})`);
        });
      }
    } catch (error) {
      console.error('❌ 获取所有部门失败:', error.message);
    }
    
    // 4. 测试搜索部门
    console.log('\n📝 步骤4: 测试搜索部门...');
    try {
      const searchResults = await departmentSyncService.searchDepartments('技术');
      console.log(`✅ 搜索"技术"找到 ${searchResults.length} 个部门`);
      
      if (searchResults.length > 0) {
        console.log('🔍 搜索结果:');
        searchResults.forEach(dept => {
          console.log(`  - ${dept.name} (ID: ${dept.deptId}, 路径: ${dept.path})`);
        });
      }
    } catch (error) {
      console.error('❌ 搜索部门失败:', error.message);
    }
    
    // 5. 测试获取部门信息
    console.log('\n📝 步骤5: 测试获取单个部门信息...');
    try {
      // 尝试获取根部门信息
      const deptInfo = await departmentSyncService.getDepartmentInfo(1);
      if (deptInfo) {
        console.log('✅ 获取部门信息成功:');
        console.log(`  - 名称: ${deptInfo.name}`);
        console.log(`  - ID: ${deptInfo.deptId}`);
        console.log(`  - 父ID: ${deptInfo.parentId}`);
        console.log(`  - 路径: ${deptInfo.path}`);
      } else {
        console.log('⚠️ 未找到部门信息');
      }
    } catch (error) {
      console.error('❌ 获取部门信息失败:', error.message);
    }
    
    // 6. 测试获取子部门
    console.log('\n📝 步骤6: 测试获取子部门...');
    try {
      const subDepartments = await departmentSyncService.getSubDepartments(1);
      console.log(`✅ 根部门下有 ${subDepartments.length} 个子部门`);
      
      if (subDepartments.length > 0) {
        console.log('📋 子部门列表:');
        subDepartments.slice(0, 5).forEach(dept => {
          console.log(`  - ${dept.name} (ID: ${dept.deptId})`);
        });
      }
    } catch (error) {
      console.error('❌ 获取子部门失败:', error.message);
    }
    
    // 7. 测试数据库直接操作
    console.log('\n📝 步骤7: 测试数据库直接操作...');
    try {
      const dbDepartments = await databaseService.getAllDepartments();
      console.log(`✅ 数据库中有 ${dbDepartments.length} 个部门`);
      
      if (dbDepartments.length > 0) {
        console.log('💾 数据库部门示例:');
        dbDepartments.slice(0, 3).forEach(dept => {
          console.log(`  - ${dept.name} (ID: ${dept.deptId}, 排序: ${dept.order})`);
        });
      }
    } catch (error) {
      console.error('❌ 数据库操作失败:', error.message);
    }
    
    console.log('\n✅ 部门管理功能测试完成');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在停止测试...');
  process.exit(0);
});

// 运行测试
testDepartmentFeatures()
  .then(() => {
    console.log('\n🎉 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  });
