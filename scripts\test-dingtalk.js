#!/usr/bin/env node

/**
 * 钉钉免登录功能测试脚本
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000';

// 测试用例
const tests = [
    {
        name: '健康检查',
        url: '/api/health',
        method: 'GET'
    },
    {
        name: '获取应用配置',
        url: '/api/app/config',
        method: 'GET'
    },
    {
        name: '获取JSAPI签名',
        url: '/api/auth/jsapi-signature?url=http://localhost:3000/test',
        method: 'GET'
    },
    {
        name: '获取增强版JSAPI签名',
        url: '/api/app/jsapi-signature/enhanced?url=http://localhost:3000/test',
        method: 'GET'
    },
    {
        name: '获取部门列表',
        url: '/api/auth/departments',
        method: 'GET'
    },
    {
        name: '获取部门用户',
        url: '/api/app/department/users?deptId=1&cursor=0&size=10',
        method: 'GET'
    },
    {
        name: '获取应用统计',
        url: '/api/app/stats',
        method: 'GET'
    }
];

// 执行测试
async function runTests() {
    console.log('🧪 开始测试钉钉API接口...\n');
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            console.log(`📋 测试: ${test.name}`);
            console.log(`🔗 URL: ${test.method} ${test.url}`);
            
            const response = await fetch(`${BASE_URL}${test.url}`, {
                method: test.method,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (response.ok) {
                console.log(`✅ 成功 (${response.status})`);
                console.log(`📄 响应: ${JSON.stringify(data, null, 2).substring(0, 200)}...`);
                passed++;
            } else {
                console.log(`❌ 失败 (${response.status})`);
                console.log(`📄 错误: ${JSON.stringify(data, null, 2)}`);
                failed++;
            }
        } catch (error) {
            console.log(`❌ 异常: ${error.message}`);
            failed++;
        }
        
        console.log('─'.repeat(60));
    }
    
    console.log(`\n📊 测试结果:`);
    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`📈 成功率: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
    
    if (failed === 0) {
        console.log('\n🎉 所有测试通过！');
    } else {
        console.log('\n⚠️  部分测试失败，请检查配置和服务状态');
    }
}

// 检查服务器状态
async function checkServer() {
    try {
        console.log('🔍 检查服务器状态...');
        const response = await fetch(`${BASE_URL}/api/health`);
        
        if (response.ok) {
            console.log('✅ 服务器运行正常\n');
            return true;
        } else {
            console.log('❌ 服务器响应异常\n');
            return false;
        }
    } catch (error) {
        console.log(`❌ 无法连接到服务器: ${error.message}`);
        console.log('💡 请确保服务器已启动 (npm run dev)\n');
        return false;
    }
}

// 主函数
async function main() {
    console.log('🚀 钉钉免登录功能测试工具');
    console.log('=' .repeat(60));
    
    const serverOk = await checkServer();
    
    if (serverOk) {
        await runTests();
    } else {
        console.log('🛑 测试中止：服务器不可用');
        process.exit(1);
    }
}

// 运行测试
main().catch(error => {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
});
