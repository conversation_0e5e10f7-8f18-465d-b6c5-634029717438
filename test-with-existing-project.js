// 使用现有项目测试收入管理API
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

async function testWithExistingProject() {
  console.log('🧪 使用现有项目测试收入管理API...\n');

  try {
    // 1. 获取现有项目
    console.log('1. 获取现有项目...');
    const projectsResponse = await fetch(`${BASE_URL}/test/projects`);
    const projectsResult = await projectsResponse.json();
    
    if (!projectsResult.success || projectsResult.data.projects.length === 0) {
      console.log('❌ 没有找到现有项目');
      return;
    }

    const projectId = projectsResult.data.projects[0].id;
    const projectName = projectsResult.data.projects[0].projectName;
    console.log('✅ 找到项目:', projectName, 'ID:', projectId);

    // 2. 创建项目收入
    console.log('\n2. 创建项目收入...');
    const revenueData = {
      title: '第一阶段里程碑收入',
      revenueType: 'milestone',
      plannedAmount: 500000,
      plannedDate: '2024-06-30',
      milestone: '项目第一阶段完成',
      paymentTerms: '收到发票后30天内付款',
      notes: '项目启动阶段的里程碑收入'
    };

    const createResponse = await fetch(`${BASE_URL}/test/projects/${projectId}/revenues`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(revenueData)
    });

    const createResult = await createResponse.json();
    console.log('创建收入响应状态:', createResponse.status);
    
    let revenueId = null;
    if (createResult.success && createResult.data) {
      revenueId = createResult.data.id;
      console.log('✅ 创建项目收入成功, ID:', revenueId);
      console.log('收入详情:', {
        title: createResult.data.title,
        type: createResult.data.revenueType,
        status: createResult.data.status,
        amount: createResult.data.plannedAmount,
        date: createResult.data.plannedDate
      });
    } else {
      console.log('❌ 创建项目收入失败:', createResult.message);
      return;
    }

    // 3. 获取收入列表
    console.log('\n3. 获取收入列表...');
    const listResponse = await fetch(`${BASE_URL}/test/revenues`);
    const listResult = await listResponse.json();
    
    if (listResult.success) {
      console.log('✅ 获取收入列表成功');
      console.log(`📊 总收入数: ${listResult.data.total}`);
      console.log(`📄 当前页收入数: ${listResult.data.revenues.length}`);
    } else {
      console.log('❌ 获取收入列表失败:', listResult.message);
    }

    // 4. 获取单个收入
    console.log('\n4. 获取单个收入...');
    const getResponse = await fetch(`${BASE_URL}/test/revenues/${revenueId}`);
    const getResult = await getResponse.json();
    
    if (getResult.success) {
      console.log('✅ 获取单个收入成功');
      console.log('收入详情:', {
        id: getResult.data.id,
        title: getResult.data.title,
        status: getResult.data.status,
        plannedAmount: getResult.data.plannedAmount,
        projectId: getResult.data.projectId
      });
    } else {
      console.log('❌ 获取单个收入失败:', getResult.message);
    }

    // 5. 更新收入状态
    console.log('\n5. 更新收入状态...');
    const updateData = {
      status: 'confirmed',
      actualAmount: 500000,
      confirmedDate: '2024-06-25',
      notes: '收入已确认，等待开票'
    };

    const updateResponse = await fetch(`${BASE_URL}/test/revenues/${revenueId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });

    const updateResult = await updateResponse.json();
    if (updateResult.success) {
      console.log('✅ 更新收入成功');
      console.log('更新后状态:', updateResult.data.status);
      console.log('实际金额:', updateResult.data.actualAmount);
      console.log('确认日期:', updateResult.data.confirmedDate);
    } else {
      console.log('❌ 更新收入失败:', updateResult.message);
    }

    // 6. 测试过滤功能
    console.log('\n6. 测试过滤功能...');
    const filterResponse = await fetch(`${BASE_URL}/test/revenues?status=confirmed&revenueType=milestone`);
    const filterResult = await filterResponse.json();
    
    if (filterResult.success) {
      console.log('✅ 过滤功能正常');
      console.log(`📋 过滤后收入数: ${filterResult.data.revenues.length}`);
    } else {
      console.log('❌ 过滤功能失败:', filterResult.message);
    }

    // 7. 获取收入统计
    console.log('\n7. 获取收入统计...');
    const statsResponse = await fetch(`${BASE_URL}/test/revenues/stats`);
    const statsResult = await statsResponse.json();
    
    if (statsResult.success) {
      console.log('✅ 收入统计功能正常');
      console.log('统计数据:', {
        totalPlannedRevenue: statsResult.data.totalPlannedRevenue,
        totalActualRevenue: statsResult.data.totalActualRevenue,
        totalInvoicedRevenue: statsResult.data.totalInvoicedRevenue,
        totalReceivedRevenue: statsResult.data.totalReceivedRevenue
      });
    } else {
      console.log('❌ 收入统计功能失败:', statsResult.message);
    }

    console.log('\n✅ 收入管理功能测试完成！');
    console.log('🎉 所有核心功能都正常工作');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testWithExistingProject();
