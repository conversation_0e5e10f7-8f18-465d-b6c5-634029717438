# Helm Chart 默认配置值

# 应用基本信息
app:
  name: cantv-ding-backend
  version: "1.0.0"

# 镜像配置
image:
  repository: registry.cn-hangzhou.aliyuncs.com/cantv-ding/cantv-ding-backend
  tag: "latest"
  pullPolicy: Always
  pullSecrets:
    - name: aliyun-registry-secret

# 副本数配置
replicaCount: 2

# 服务配置
service:
  type: ClusterIP
  port: 80
  targetPort: 3000
  annotations: {}

# Ingress配置
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  hosts:
    - host: api.cantv-ding.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: cantv-ding-tls
      hosts:
        - api.cantv-ding.com

# 资源限制
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"

# 健康检查
healthCheck:
  enabled: true
  path: /api/health
  port: 3000
  livenessProbe:
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  readinessProbe:
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# 环境变量
env:
  NODE_ENV: "production"
  PORT: "3000"

# 密钥配置
secrets:
  name: cantv-ding-secrets
  data:
    DATABASE_URL: ""
    DINGTALK_APP_KEY: ""
    DINGTALK_APP_SECRET: ""
    DINGTALK_CORP_ID: ""
    DINGTALK_AGENT_ID: ""
    JWT_SECRET: ""

# 存储卷配置
volumes:
  logs:
    enabled: true
    mountPath: /app/logs
    type: emptyDir

# 节点选择器
nodeSelector: {}

# 容忍度
tolerations: []

# 亲和性
affinity: {}

# Pod安全上下文
podSecurityContext:
  fsGroup: 1001

# 容器安全上下文
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL

# 自动扩缩容
autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Pod中断预算
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# 服务监控
monitoring:
  enabled: false
  serviceMonitor:
    enabled: false
    namespace: monitoring
    interval: 30s
    path: /metrics

# 网络策略
networkPolicy:
  enabled: false
