// 测试财务报表API
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

async function testFinancialAPI() {
  console.log('🧪 开始测试财务报表API...\n');

  try {
    // 1. 测试获取品牌财务汇总报表
    console.log('1. 测试获取品牌财务汇总报表...');
    const summaryResponse = await fetch(`${BASE_URL}/financial/brands/summary`);
    const summaryResult = await summaryResponse.json();
    
    console.log('汇总报表响应状态:', summaryResponse.status);
    if (summaryResult.success) {
      console.log('✅ 获取品牌财务汇总报表成功');
      console.log(`📊 总品牌数: ${summaryResult.data.summary.totalBrands}`);
      console.log(`💰 总下单金额: ¥${summaryResult.data.summary.totalOrderAmount.toLocaleString()}`);
      console.log(`📈 总预估毛利: ¥${summaryResult.data.summary.totalEstimatedProfit.toLocaleString()}`);
      console.log(`📊 整体毛利率: ${summaryResult.data.summary.overallProfitMargin}%`);
      
      console.log('\n品牌明细:');
      summaryResult.data.brands.forEach((brand, index) => {
        console.log(`${index + 1}. ${brand.brandName}`);
        console.log(`   下单金额: ¥${brand.orderAmount.toLocaleString()}`);
        console.log(`   预估毛利: ¥${brand.estimatedProfit.toLocaleString()}`);
        console.log(`   毛利率: ${brand.estimatedProfitMargin}%`);
        console.log(`   项目数: ${brand.projectCount} (进行中: ${brand.activeProjectCount}, 已完成: ${brand.completedProjectCount})`);
      });
    } else {
      console.log('❌ 获取品牌财务汇总报表失败:', summaryResult.message);
    }

    // 2. 测试带参数的汇总报表查询
    console.log('\n2. 测试带参数的汇总报表查询...');
    const filteredSummaryResponse = await fetch(`${BASE_URL}/financial/brands/summary?startDate=2024-01-01&endDate=2024-12-31&includeCompleted=true`);
    const filteredSummaryResult = await filteredSummaryResponse.json();
    
    console.log('过滤汇总报表响应状态:', filteredSummaryResponse.status);
    if (filteredSummaryResult.success) {
      console.log('✅ 获取过滤汇总报表成功');
      console.log(`📅 报表期间: ${filteredSummaryResult.data.reportPeriod.startDate} 至 ${filteredSummaryResult.data.reportPeriod.endDate}`);
    } else {
      console.log('❌ 获取过滤汇总报表失败:', filteredSummaryResult.message);
    }

    // 3. 测试获取品牌详细报表
    console.log('\n3. 测试获取品牌详细报表...');
    
    // 先获取第一个品牌的ID
    let brandId = 'brand-001'; // 默认品牌ID
    if (summaryResult.success && summaryResult.data.brands.length > 0) {
      brandId = summaryResult.data.brands[0].brandId;
    }
    
    const detailResponse = await fetch(`${BASE_URL}/financial/brands/${brandId}/detail`);
    const detailResult = await detailResponse.json();
    
    console.log('详细报表响应状态:', detailResponse.status);
    if (detailResult.success) {
      console.log('✅ 获取品牌详细报表成功');
      console.log(`🏢 品牌: ${detailResult.data.brandInfo.name}`);
      console.log(`📊 项目总数: ${detailResult.data.projects.length}`);
      
      console.log('\n项目明细:');
      detailResult.data.projects.slice(0, 3).forEach((project, index) => {
        console.log(`${index + 1}. ${project.projectName}`);
        console.log(`   状态: ${project.status}`);
        console.log(`   预算: ¥${project.budget.planningBudget.toLocaleString()}`);
        console.log(`   利润: ¥${project.profit.profit.toLocaleString()} (毛利率: ${project.profit.grossMargin}%)`);
        console.log(`   执行PM: ${project.executorPMInfo?.name || project.executorPM}`);
      });
      
      if (detailResult.data.projects.length > 3) {
        console.log(`   ... 还有 ${detailResult.data.projects.length - 3} 个项目`);
      }
      
      console.log('\n收入分析:');
      console.log(`📈 计划收入: ¥${detailResult.data.revenueAnalysis.totalPlannedRevenue.toLocaleString()}`);
      console.log(`💰 已收收入: ¥${detailResult.data.revenueAnalysis.totalReceivedRevenue.toLocaleString()}`);
      
      console.log('\n成本分析:');
      console.log(`📋 周预算总数: ${detailResult.data.costAnalysis.totalWeeklyBudgets}`);
      console.log(`💸 已支付: ¥${detailResult.data.costAnalysis.totalPaidAmount.toLocaleString()}`);
      console.log(`⏳ 未支付: ¥${detailResult.data.costAnalysis.totalUnpaidAmount.toLocaleString()}`);
    } else {
      console.log('❌ 获取品牌详细报表失败:', detailResult.message);
    }

    // 4. 测试错误情况
    console.log('\n4. 测试错误情况...');
    const errorResponse = await fetch(`${BASE_URL}/financial/brands/non-existent-brand/detail`);
    const errorResult = await errorResponse.json();
    
    console.log('错误测试响应状态:', errorResponse.status);
    if (!errorResult.success) {
      console.log('✅ 错误处理正常:', errorResult.message);
    } else {
      console.log('⚠️ 预期应该返回错误，但返回了成功');
    }

    console.log('\n🎉 财务报表API测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testFinancialAPI();
