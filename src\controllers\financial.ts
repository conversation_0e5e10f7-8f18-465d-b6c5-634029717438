import { FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { ProjectService } from '../services/project.js';
import { FinancialReportQueryParams, ProjectStatus } from '../types/project.js';

// 验证模式
const financialReportQuerySchema = z.object({
  brandId: z.string().optional(),
  startDate: z.string().optional().transform(str => str ? new Date(str) : undefined),
  endDate: z.string().optional().transform(str => str ? new Date(str) : undefined),
  projectStatus: z.array(z.nativeEnum(ProjectStatus)).optional(),
  includeCompleted: z.string().optional().transform(str => str === 'true'),
  includeCancelled: z.string().optional().transform(str => str === 'true')
});

export class FinancialController {
  private projectService: ProjectService;

  constructor() {
    this.projectService = new ProjectService();
  }

  /**
   * 获取品牌财务汇总报表
   */
  async getBrandFinancialSummary(request: FastifyRequest, reply: FastifyReply) {
    try {
      const queryParams = financialReportQuerySchema.parse(request.query);
      console.log('获取品牌财务汇总报表:', queryParams);

      const result = await this.projectService.getBrandFinancialSummary(queryParams);
      console.log('获取品牌财务汇总报表成功:', result);

      return reply.send({
        success: true,
        data: result,
        message: '获取品牌财务汇总报表成功'
      });
    } catch (error) {
      console.error('获取品牌财务汇总报表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取品牌财务汇总报表失败'
      });
    }
  }

  /**
   * 获取品牌财务详细报表
   */
  async getBrandFinancialDetail(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { brandId } = request.params as { brandId: string };
      const queryParams = financialReportQuerySchema.parse(request.query);
      console.log('获取品牌财务详细报表:', brandId, queryParams);

      const result = await this.projectService.getBrandFinancialDetail(brandId, queryParams);
      console.log('获取品牌财务详细报表成功:', result);

      return reply.send({
        success: true,
        data: result,
        message: '获取品牌财务详细报表成功'
      });
    } catch (error) {
      console.error('获取品牌财务详细报表失败:', error);

      if (error instanceof z.ZodError) {
        return reply.status(400).send({
          success: false,
          message: '参数验证失败',
          errors: error.errors
        });
      }

      return reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : '获取品牌财务详细报表失败'
      });
    }
  }
}
