// 简单的API测试脚本
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

async function simpleTest() {
  console.log('🧪 简单API测试...\n');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await fetch(`${BASE_URL}/health`);
    const healthResult = await healthResponse.json();
    console.log('健康检查结果:', healthResult);

    // 测试无认证的项目API
    console.log('\n2. 测试无认证的项目API...');
    const projectsResponse = await fetch(`${BASE_URL}/projects`);
    const projectsResult = await projectsResponse.json();
    console.log('项目API响应状态:', projectsResponse.status);
    console.log('项目API响应:', projectsResult);

    // 测试应用配置
    console.log('\n3. 测试应用配置...');
    const configResponse = await fetch(`${BASE_URL}/app/config`);
    const configResult = await configResponse.json();
    console.log('应用配置结果:', configResult);

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🏁 简单测试完成');
}

// 运行测试
simpleTest();
