<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉免登认证演示</title>
    <script src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 15px;
        }

        .auth-status {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }

        .auth-status.loading {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }

        .auth-status.success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .auth-status.error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .user-info {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .user-info h3 {
            margin-top: 0;
            color: #333;
        }

        .info-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
        }

        .info-label {
            font-weight: 500;
            width: 100px;
            color: #666;
        }

        .info-value {
            color: #333;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #40a9ff;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333;
        }

        .btn-secondary:hover {
            background-color: #e6e6e6;
        }

        .api-test {
            margin-top: 30px;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 4px;
        }

        .api-test h3 {
            margin-top: 0;
            color: #333;
        }

        .api-result {
            background-color: #f6f6f6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .warning {
            background-color: #fffbe6;
            border: 1px solid #ffe58f;
            color: #d48806;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }

        .warning h4 {
            margin-top: 0;
            color: #d48806;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🔐 钉钉免登认证演示</h1>

        <div id="authStatus" class="auth-status loading">
            正在进行钉钉免登认证...
        </div>

        <div class="warning">
            <h4>⚠️ 重要提示</h4>
            <p>此页面需要在钉钉客户端中打开才能正常工作。如果您在浏览器中直接访问，免登认证将会失败。</p>
            <p>在钉钉中，所有的项目和品牌数据访问都需要通过免登认证，确保数据安全。</p>
        </div>

        <div id="userInfo" class="user-info" style="display: none;">
            <h3>👤 当前用户信息</h3>
            <div class="info-item">
                <span class="info-label">用户ID:</span>
                <span class="info-value" id="userId">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">姓名:</span>
                <span class="info-value" id="userName">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">手机号:</span>
                <span class="info-value" id="userMobile">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">部门:</span>
                <span class="info-value" id="userDepts">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">职位:</span>
                <span class="info-value" id="userPosition">-</span>
            </div>
            <div class="info-item">
                <span class="info-label">权限:</span>
                <span class="info-value" id="userRole">-</span>
            </div>
        </div>

        <div class="api-test">
            <h3>🧪 API 测试</h3>
            <p>认证成功后，您可以测试以下受保护的API接口：</p>

            <button class="btn btn-primary" onclick="testAPI('/api/brands')">测试获取品牌列表</button>
            <button class="btn btn-primary" onclick="testAPI('/api/projects')">测试获取项目列表</button>
            <button class="btn btn-primary" onclick="testAPI('/api/departments')">测试获取部门列表</button>
            <button class="btn btn-primary" onclick="testAPI('/api/projects/stats')">测试获取项目统计</button>

            <div id="apiResult" class="api-result" style="display: none;"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn btn-secondary" onclick="logout()">登出</button>
            <button class="btn btn-secondary" onclick="location.reload()">重新认证</button>
            <button class="btn btn-primary" onclick="goToProjectForm()">前往项目创建</button>
        </div>
    </div>

    <!-- 钉钉 JSAPI -->
    <script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.15.0/dingtalk.open.js"></script>

    <script>
        initDingH5RemoteDebug();
        let authCode = null;
        let userInfo = null;
        let jsapiConfig = null;
        let accessToken = null;
        let refreshToken = null;

        // 页面加载时开始认证
        document.addEventListener('DOMContentLoaded', function () {
            // 先检查是否已有保存的token
            checkExistingAuth();
        });

        // 检查现有认证状态
        async function checkExistingAuth() {
            const savedToken = localStorage.getItem('accessToken');
            const savedRefreshToken = localStorage.getItem('refreshToken');
            const savedUserInfo = localStorage.getItem('userInfo');

            if (savedToken && savedUserInfo) {
                accessToken = savedToken;
                refreshToken = savedRefreshToken;
                userInfo = JSON.parse(savedUserInfo);

                console.log('发现已保存的认证信息，验证中...');

                // 验证token是否仍然有效
                try {
                    const response = await fetch('/api/auth/me', {
                        headers: {
                            'Authorization': 'Bearer ' + accessToken
                        }
                    });
                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            console.log('data', data)
                            console.log('已保存的认证信息有效');
                            showAuthSuccess();
                            displayUserInfo(userInfo);
                            return;
                        }
                    }
                } catch (error) {
                    console.log('验证已保存认证信息失败:', error);
                }

                // 如果验证失败，清除保存的信息
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                localStorage.removeItem('userInfo');
                accessToken = null;
                refreshToken = null;
                userInfo = null;
            }

            // 开始钉钉免登认证
            initDingTalkAuth();
        }

        // 初始化钉钉免登认证
        function initDingTalkAuth() {
            // 检查是否在钉钉环境中
            if (typeof dd === 'undefined') {
                showAuthError('请在钉钉客户端中打开此页面');
                return;
            }

            // 钉钉环境检查
            dd.ready(function () {
                console.log('钉钉环境准备就绪');

                // 获取免登码
                dd.runtime.permission.requestAuthCode({
                    corpId: 'dinge21dd1a7d6663db3a39a90f97fcb1e09', // 使用您的企业corpId
                    onSuccess: function (result) {
                        console.log('获取免登码成功:', result);
                        authCode = result.code;
                        authenticateWithServer(authCode);
                    },
                    onFail: function (err) {
                        console.error('获取免登码失败:', err);
                        showAuthError('获取免登码失败: ' + err.message);
                    }
                });
            });

            dd.error(function (err) {
                console.error('钉钉初始化失败:', err);
                showAuthError('钉钉初始化失败: ' + err.message);
            });
        }

        // 向服务器验证免登码
        async function authenticateWithServer(code) {
            try {
                console.log('向服务器发送免登码:', code);

                // 调用后端API验证免登码并获取JWT token
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        authCode: code
                    })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || '认证请求失败');
                }

                if (data.success) {
                    // 保存JWT token
                    accessToken = data.data.accessToken;
                    refreshToken = data.data.refreshToken;
                    userInfo = data.data.user;
                    authCode = code;

                    // 保存到localStorage
                    localStorage.setItem('accessToken', accessToken);
                    localStorage.setItem('refreshToken', refreshToken);
                    localStorage.setItem('userInfo', JSON.stringify(userInfo));

                    console.log('accessToken', accessToken);
                    console.log('登录成功，用户信息:', userInfo);
                    console.log('访问令牌已保存:', accessToken.substring(0, 20) + '...');

                    showAuthSuccess();
                    displayUserInfo(userInfo);
                } else {
                    throw new Error(data.message || '登录失败');
                }

            } catch (error) {
                console.error('服务器认证失败:', error);
                showAuthError('服务器认证失败: ' + error.message);
            }
        }

        // 显示认证成功
        function showAuthSuccess() {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.className = 'auth-status success';
            statusDiv.textContent = '✅ 钉钉免登认证成功！';
        }

        // 显示认证错误
        function showAuthError(message) {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.className = 'auth-status error';
            statusDiv.textContent = '❌ ' + message;
        }

        // 显示用户信息
        function displayUserInfo(user) {
            document.getElementById('userId').textContent = user.userid;
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userMobile').textContent = user.mobile || '未设置';
            document.getElementById('userDepts').textContent = user.deptIds ? user.deptIds.join(', ') : '未知';
            document.getElementById('userPosition').textContent = user.position || '未设置';

            let role = '普通用户';
            if (user.isBoss) role = '老板';
            else if (user.isAdmin) role = '管理员';
            document.getElementById('userRole').textContent = role;

            document.getElementById('userInfo').style.display = 'block';
        }

        // 测试API接口
        async function testAPI(endpoint) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '正在调用 ' + endpoint + '...';

            try {
                const headers = {};

                // 使用JWT token进行认证
                if (accessToken) {
                    headers['Authorization'] = 'Bearer ' + accessToken;
                } else {
                    throw new Error('缺少访问令牌，请先登录');
                }

                const response = await fetch(endpoint, {
                    method: 'GET',
                    headers: headers
                });

                const data = await response.json();

                // 如果token过期，尝试刷新
                if (response.status === 401 && data.code === 'INVALID_TOKEN') {
                    console.log('访问令牌已过期，尝试刷新...');
                    const refreshed = await refreshAccessToken();
                    if (refreshed) {
                        // 重新调用API
                        return testAPI(endpoint);
                    } else {
                        throw new Error('令牌刷新失败，请重新登录');
                    }
                }

                resultDiv.textContent = `请求: ${endpoint}\n状态: ${response.status}\n响应:\n${JSON.stringify(data, null, 2)}`;

                if (!response.ok) {
                    resultDiv.style.color = '#ff4d4f';
                } else {
                    resultDiv.style.color = '#52c41a';
                }

            } catch (error) {
                console.error('API调用失败:', error);
                resultDiv.textContent = `请求: ${endpoint}\n错误: ${error.message}`;
                resultDiv.style.color = '#ff4d4f';
            }
        }

        // 刷新访问令牌
        async function refreshAccessToken() {
            try {
                if (!refreshToken) {
                    console.error('缺少刷新令牌');
                    return false;
                }

                const response = await fetch('/api/auth/refresh', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        refreshToken: refreshToken
                    })
                });

                const data = await response.json();

                if (data.success) {
                    accessToken = data.data.accessToken;
                    localStorage.setItem('accessToken', accessToken);
                    console.log('访问令牌刷新成功');
                    return true;
                } else {
                    console.error('刷新令牌失败:', data.message);
                    return false;
                }
            } catch (error) {
                console.error('刷新令牌异常:', error);
                return false;
            }
        }

        // 用户登出
        async function logout() {
            try {
                if (accessToken) {
                    // 调用后端登出接口
                    await fetch('/api/auth/logout', {
                        method: 'POST',
                        headers: {
                            'Authorization': 'Bearer ' + accessToken
                        }
                    });
                }
            } catch (error) {
                console.error('登出请求失败:', error);
            }

            // 清除本地存储
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userInfo');

            // 重置变量
            accessToken = null;
            refreshToken = null;
            userInfo = null;
            authCode = null;

            // 刷新页面
            location.reload();
        }

        // 前往项目创建页面
        function goToProjectForm() {
            if (!accessToken) {
                alert('请先完成登录认证');
                return;
            }

            // 将认证信息传递给项目创建页面
            const url = '/project-form-demo.html?token=' + encodeURIComponent(accessToken);
            window.location.href = url;
        }
    </script>
</body>

</html>