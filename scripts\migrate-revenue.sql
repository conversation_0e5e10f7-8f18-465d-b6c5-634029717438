-- 项目收入管理数据库迁移脚本
-- 创建收入状态枚举
CREATE TYPE revenue_status AS ENUM (
  'PLANNED',     -- 计划中
  'CONFIRMED',   -- 已确认
  'INVOICED',    -- 已开票
  'RECEIVED',    -- 已收款
  'OVERDUE',     -- 逾期
  'CANCELLED'    -- 已取消
);

-- 创建收入类型枚举
CREATE TYPE revenue_type AS ENUM (
  'INFLUENCER_INCOME', -- 达人收入
  'PROJECT_INCOME',    -- 项目收入
  'OTHER'              -- 其他收入
);

-- 创建项目收入表
CREATE TABLE project_revenues (
  id VARCHAR(50) PRIMARY KEY,
  
  -- 基本信息
  title VARCHAR(200) NOT NULL,
  revenue_type revenue_type NOT NULL DEFAULT 'PROJECT_INCOME',
  status revenue_status NOT NULL DEFAULT 'PLANNED',
  
  -- 金额信息
  planned_amount DECIMAL(15, 2) NOT NULL,
  actual_amount DECIMAL(15, 2),
  invoice_amount DECIMAL(15, 2),
  
  -- 时间信息
  planned_date DATE NOT NULL,
  confirmed_date DATE,
  invoice_date DATE,
  received_date DATE,
  
  -- 业务信息
  milestone VARCHAR(200),
  invoice_number VARCHAR(100),
  payment_terms TEXT,
  notes TEXT,
  
  -- 关联项目
  project_id VARCHAR(50) NOT NULL,
  
  -- 审计字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by VARCHAR(50) NOT NULL,
  updated_by VARCHAR(50) NOT NULL,
  
  -- 外键约束
  CONSTRAINT fk_project_revenues_project 
    FOREIGN KEY (project_id) 
    REFERENCES projects(id) 
    ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_project_revenues_project_status ON project_revenues(project_id, status);
CREATE INDEX idx_project_revenues_planned_date ON project_revenues(planned_date);
CREATE INDEX idx_project_revenues_status ON project_revenues(status);
CREATE INDEX idx_project_revenues_type ON project_revenues(revenue_type);
CREATE INDEX idx_project_revenues_created_at ON project_revenues(created_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_project_revenues_updated_at 
  BEFORE UPDATE ON project_revenues 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- 插入示例数据
INSERT INTO project_revenues (
  id, title, revenue_type, status, planned_amount, planned_date, 
  milestone, payment_terms, notes, project_id, created_by, updated_by
) VALUES 
(
  'revenue-001',
  '项目启动阶段收入',
  'MILESTONE',
  'PLANNED',
  500000.00,
  '2024-03-31',
  '项目启动阶段完成，交付项目计划书',
  '收到发票后30天内付款',
  '项目第一阶段的里程碑收入',
  'project-001',
  'admin',
  'admin'
),
(
  'revenue-002',
  '项目执行阶段收入',
  'MILESTONE',
  'PLANNED',
  800000.00,
  '2024-06-30',
  '项目执行阶段完成，交付执行报告',
  '收到发票后30天内付款',
  '项目第二阶段的里程碑收入',
  'project-001',
  'admin',
  'admin'
),
(
  'revenue-003',
  '项目结项收入',
  'FINAL',
  'PLANNED',
  300000.00,
  '2024-08-31',
  '项目完成结项，交付最终报告',
  '收到发票后15天内付款',
  '项目最终收入',
  'project-001',
  'admin',
  'admin'
),
(
  'revenue-004',
  '月度服务费',
  'MONTHLY',
  'CONFIRMED',
  100000.00,
  '2024-02-29',
  '2月份服务完成',
  '月结30天',
  '2月份的月度服务费',
  'project-001',
  'admin',
  'admin'
);

-- 更新一条记录为已确认状态
UPDATE project_revenues 
SET 
  status = 'CONFIRMED',
  actual_amount = 100000.00,
  confirmed_date = '2024-02-28',
  updated_at = NOW()
WHERE id = 'revenue-004';

-- 创建收入统计视图
CREATE OR REPLACE VIEW revenue_stats_view AS
SELECT 
  -- 总体统计
  SUM(planned_amount) as total_planned_revenue,
  SUM(CASE WHEN actual_amount IS NOT NULL THEN actual_amount ELSE 0 END) as total_actual_revenue,
  SUM(CASE WHEN invoice_amount IS NOT NULL THEN invoice_amount ELSE 0 END) as total_invoiced_revenue,
  SUM(CASE WHEN status = 'RECEIVED' AND actual_amount IS NOT NULL THEN actual_amount ELSE 0 END) as total_received_revenue,
  
  -- 按状态统计
  COUNT(*) as total_count,
  COUNT(CASE WHEN status = 'PLANNED' THEN 1 END) as planned_count,
  COUNT(CASE WHEN status = 'CONFIRMED' THEN 1 END) as confirmed_count,
  COUNT(CASE WHEN status = 'INVOICED' THEN 1 END) as invoiced_count,
  COUNT(CASE WHEN status = 'RECEIVED' THEN 1 END) as received_count,
  COUNT(CASE WHEN status = 'OVERDUE' THEN 1 END) as overdue_count,
  COUNT(CASE WHEN status = 'CANCELLED' THEN 1 END) as cancelled_count
FROM project_revenues;

-- 创建月度收入趋势视图
CREATE OR REPLACE VIEW monthly_revenue_trend_view AS
SELECT 
  TO_CHAR(planned_date, 'YYYY-MM') as month,
  SUM(planned_amount) as planned_amount,
  SUM(CASE WHEN actual_amount IS NOT NULL THEN actual_amount ELSE 0 END) as actual_amount,
  COUNT(*) as revenue_count
FROM project_revenues
WHERE planned_date >= CURRENT_DATE - INTERVAL '12 months'
GROUP BY TO_CHAR(planned_date, 'YYYY-MM')
ORDER BY month;

-- 添加注释
COMMENT ON TABLE project_revenues IS '项目预计收入表';
COMMENT ON COLUMN project_revenues.title IS '收入标题/描述';
COMMENT ON COLUMN project_revenues.revenue_type IS '收入类型';
COMMENT ON COLUMN project_revenues.status IS '收入状态';
COMMENT ON COLUMN project_revenues.planned_amount IS '预计收入金额';
COMMENT ON COLUMN project_revenues.actual_amount IS '实际收入金额';
COMMENT ON COLUMN project_revenues.invoice_amount IS '开票金额';
COMMENT ON COLUMN project_revenues.planned_date IS '预计收入时间';
COMMENT ON COLUMN project_revenues.confirmed_date IS '确认收入时间';
COMMENT ON COLUMN project_revenues.invoice_date IS '开票时间';
COMMENT ON COLUMN project_revenues.received_date IS '实际收款时间';
COMMENT ON COLUMN project_revenues.milestone IS '里程碑描述';
COMMENT ON COLUMN project_revenues.invoice_number IS '发票号码';
COMMENT ON COLUMN project_revenues.payment_terms IS '付款条件';
COMMENT ON COLUMN project_revenues.notes IS '备注说明';

-- 验证数据
SELECT 
  'project_revenues' as table_name,
  COUNT(*) as record_count
FROM project_revenues;

SELECT 
  status,
  COUNT(*) as count,
  SUM(planned_amount) as total_planned,
  SUM(actual_amount) as total_actual
FROM project_revenues
GROUP BY status
ORDER BY status;
