import { DatabaseService } from '../services/database.js';
import { CreatePermissionData, PermissionService } from '../services/permissionService.js';
import { CreateRoleData, RoleService } from '../services/roleService.js';

// 系统权限定义
const SYSTEM_PERMISSIONS: CreatePermissionData[] = [
  // 用户管理权限
  { name: 'user.read', displayName: '查看用户', description: '查看用户信息', module: 'user', action: 'read', isSystem: true },
  { name: 'user.create', displayName: '创建用户', description: '创建新用户', module: 'user', action: 'create', isSystem: true },
  { name: 'user.update', displayName: '更新用户', description: '更新用户信息', module: 'user', action: 'update', isSystem: true },
  { name: 'user.delete', displayName: '删除用户', description: '删除用户', module: 'user', action: 'delete', isSystem: true },

  // 角色管理权限
  { name: 'role.read', displayName: '查看角色', description: '查看角色信息', module: 'role', action: 'read', isSystem: true },
  { name: 'role.create', displayName: '创建角色', description: '创建新角色', module: 'role', action: 'create', isSystem: true },
  { name: 'role.update', displayName: '更新角色', description: '更新角色信息', module: 'role', action: 'update', isSystem: true },
  { name: 'role.delete', displayName: '删除角色', description: '删除角色', module: 'role', action: 'delete', isSystem: true },
  { name: 'role.assign', displayName: '分配角色', description: '为用户或部门分配角色', module: 'role', action: 'assign', isSystem: true },

  // 权限管理权限
  { name: 'permission.read', displayName: '查看权限', description: '查看权限信息', module: 'permission', action: 'read', isSystem: true },
  { name: 'permission.create', displayName: '创建权限', description: '创建新权限', module: 'permission', action: 'create', isSystem: true },
  { name: 'permission.update', displayName: '更新权限', description: '更新权限信息', module: 'permission', action: 'update', isSystem: true },
  { name: 'permission.delete', displayName: '删除权限', description: '删除权限', module: 'permission', action: 'delete', isSystem: true },

  // 项目管理权限
  { name: 'project.read', displayName: '查看项目', description: '查看项目信息', module: 'project', action: 'read', isSystem: true },
  { name: 'project.create', displayName: '创建项目', description: '创建新项目', module: 'project', action: 'create', isSystem: true },
  { name: 'project.update', displayName: '更新项目', description: '更新项目信息', module: 'project', action: 'update', isSystem: true },
  { name: 'project.delete', displayName: '删除项目', description: '删除项目', module: 'project', action: 'delete', isSystem: true },
  { name: 'project.approve', displayName: '审批项目', description: '审批项目申请', module: 'project', action: 'approve', isSystem: true },

  // 品牌管理权限
  { name: 'brand.read', displayName: '查看品牌', description: '查看品牌信息', module: 'brand', action: 'read', isSystem: true },
  { name: 'brand.create', displayName: '创建品牌', description: '创建新品牌', module: 'brand', action: 'create', isSystem: true },
  { name: 'brand.update', displayName: '更新品牌', description: '更新品牌信息', module: 'brand', action: 'update', isSystem: true },
  { name: 'brand.delete', displayName: '删除品牌', description: '删除品牌', module: 'brand', action: 'delete', isSystem: true },

  // 财务管理权限
  { name: 'finance.read', displayName: '查看财务', description: '查看财务信息', module: 'finance', action: 'read', isSystem: true },
  { name: 'finance.create', displayName: '创建财务记录', description: '创建财务记录', module: 'finance', action: 'create', isSystem: true },
  { name: 'finance.update', displayName: '更新财务记录', description: '更新财务记录', module: 'finance', action: 'update', isSystem: true },
  { name: 'finance.delete', displayName: '删除财务记录', description: '删除财务记录', module: 'finance', action: 'delete', isSystem: true },
  { name: 'finance.approve', displayName: '财务审批', description: '审批财务申请', module: 'finance', action: 'approve', isSystem: true },

  // 系统管理权限
  { name: 'system.config', displayName: '系统配置', description: '管理系统配置', module: 'system', action: 'config', isSystem: true },
  { name: 'system.log', displayName: '系统日志', description: '查看系统日志', module: 'system', action: 'log', isSystem: true },
  { name: 'system.backup', displayName: '系统备份', description: '执行系统备份', module: 'system', action: 'backup', isSystem: true },

  // 部门管理权限
  { name: 'department.read', displayName: '查看部门', description: '查看部门信息', module: 'department', action: 'read', isSystem: true },
  { name: 'department.create', displayName: '创建部门', description: '创建新部门', module: 'department', action: 'create', isSystem: true },
  { name: 'department.update', displayName: '更新部门', description: '更新部门信息', module: 'department', action: 'update', isSystem: true },
  { name: 'department.delete', displayName: '删除部门', description: '删除部门', module: 'department', action: 'delete', isSystem: true },

  // 供应商管理权限
  { name: 'supplier.read', displayName: '查看供应商', description: '查看供应商信息', module: 'supplier', action: 'read', isSystem: true },
  { name: 'supplier.create', displayName: '创建供应商', description: '创建新供应商', module: 'supplier', action: 'create', isSystem: true },
  { name: 'supplier.update', displayName: '更新供应商', description: '更新供应商信息', module: 'supplier', action: 'update', isSystem: true },
  { name: 'supplier.delete', displayName: '删除供应商', description: '删除供应商', module: 'supplier', action: 'delete', isSystem: true },

  // 预算管理权限
  { name: 'budget.read', displayName: '查看预算', description: '查看预算信息', module: 'budget', action: 'read', isSystem: true },
  { name: 'budget.create', displayName: '创建预算', description: '创建新预算', module: 'budget', action: 'create', isSystem: true },
  { name: 'budget.update', displayName: '更新预算', description: '更新预算信息', module: 'budget', action: 'update', isSystem: true },
  { name: 'budget.delete', displayName: '删除预算', description: '删除预算', module: 'budget', action: 'delete', isSystem: true },
  { name: 'budget.approve', displayName: '预算审批', description: '审批预算申请', module: 'budget', action: 'approve', isSystem: true },

  // 报表权限
  { name: 'report.read', displayName: '查看报表', description: '查看各类报表', module: 'report', action: 'read', isSystem: true },
  { name: 'report.export', displayName: '导出报表', description: '导出报表数据', module: 'report', action: 'export', isSystem: true },
];

// 系统角色定义
const SYSTEM_ROLES: CreateRoleData[] = [
  {
    name: 'super_admin',
    displayName: '超级管理员',
    description: '拥有系统所有权限的超级管理员',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'admin',
    displayName: '管理员',
    description: '系统管理员，拥有大部分管理权限',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'PMO',
    displayName: 'PM',
    description: '项目管理人员，负责项目的创建和管理',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'PM',
    displayName: 'PMO',
    description: '项目管理人员',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'finance_manager',
    displayName: '财务经理',
    description: '财务管理人员，负责财务审批和管理',
    isSystem: true,
    createdBy: 'system',
  },
  {
    name: 'user',
    displayName: '普通用户',
    description: '普通用户，拥有基本的查看权限',
    isSystem: true,
    createdBy: 'system',
  },
];

// 角色权限映射
const ROLE_PERMISSIONS: { [roleName: string]: string[] } = {
  super_admin: SYSTEM_PERMISSIONS.map(p => p.name), // 超级管理员拥有所有权限
  admin: [
    // 用户管理
    'user.read', 'user.create', 'user.update',
    // 角色管理
    'role.read', 'role.create', 'role.update', 'role.assign',
    // 权限管理
    'permission.read',
    // 项目管理
    'project.read', 'project.create', 'project.update', 'project.delete',
    // 品牌管理
    'brand.read', 'brand.create', 'brand.update', 'brand.delete',
    // 部门管理
    'department.read', 'department.create', 'department.update',
    // 供应商管理
    'supplier.read', 'supplier.create', 'supplier.update', 'supplier.delete',
    // 预算管理
    'budget.read', 'budget.create', 'budget.update',
    // 报表
    'report.read', 'report.export',
    // 系统管理
    'system.config', 'system.log',
  ],
  PMO: [
    // 项目管理
    'project.read', 'project.create', 'project.update',
    // 品牌管理
    'brand.read', 'brand.create', 'brand.update', 'brand.delete',
    // 供应商管理
    'supplier.read', 'supplier.create', 'supplier.update',
    // 预算管理
    'budget.read', 'budget.create', 'budget.update', 'budget.delete',
    // 报表
    'report.read', 'report.export',
    // 用户查看
    'user.read',
    // 部门查看
    'department.read',
  ],
  PM: [
    // 项目管理
    'project.read',
    // 品牌管理
    'brand.read',
    // 供应商管理
    'supplier.read',
    // 预算管理
    'budget.read', 'budget.create', 'budget.update',
    // 报表
    'report.read', 'report.export',
    // 用户查看
    'user.read',
    // 部门查看
    'department.read',
  ],
  finance_manager: [
    // 财务管理
    'finance.read', 'finance.create', 'finance.update', 'finance.approve',
    // 预算管理
    'budget.read', 'budget.approve',
    // 项目查看
    'project.read',
    // 报表
    'report.read', 'report.export',
    // 用户查看
    'user.read',
  ],
  user: [
    // 基本查看权限
    'project.read',
    'brand.read',
    'user.read',
    'department.read',
    'report.read',
  ],
};

export class PermissionInitializer {
  private databaseService: DatabaseService;
  private permissionService: PermissionService;
  private roleService: RoleService;

  constructor() {
    this.databaseService = new DatabaseService();
    this.permissionService = new PermissionService(this.databaseService);
    this.roleService = new RoleService(this.databaseService);
  }

  /**
   * 初始化系统权限和角色
   */
  async initialize(): Promise<void> {
    try {
      console.log('🚀 开始初始化系统权限和角色...');

      // 先检查是否已经初始化过
      const [existingRoleCount, existingPermissionCount] = await Promise.all([
        this.databaseService.client.role.count(),
        this.databaseService.client.permission.count()
      ]);

      if (existingRoleCount > 0 && existingPermissionCount > 0) {
        console.log('✅ 权限系统已存在，跳过初始化');
        return;
      }

      // 1. 创建系统权限
      console.log('📋 创建系统权限...');
      const permissions = await this.permissionService.createPermissionsBatch(SYSTEM_PERMISSIONS);
      console.log(`✅ 成功处理 ${permissions.length} 个权限`);

      // 2. 创建系统角色
      console.log('👥 创建系统角色...');
      const roles = [];
      for (const roleData of SYSTEM_ROLES) {
        try {
          const role = await this.roleService.createRole(roleData);
          roles.push(role);
          console.log(`✅ 创建角色: ${role.displayName}`);
        } catch (error: any) {
          if (error.message.includes('Unique constraint')) {
            console.log(`⚠️  角色已存在: ${roleData.displayName}`);
            const existingRole = await this.databaseService.client.role.findUnique({
              where: { name: roleData.name },
            });
            if (existingRole) {
              roles.push(existingRole);
            }
          } else {
            throw error;
          }
        }
      }

      // 3. 为角色分配权限
      console.log('🔗 为角色分配权限...');
      for (const role of roles) {
        const permissionNames = ROLE_PERMISSIONS[role.name];
        if (permissionNames && permissionNames.length > 0) {
          // 检查是否已经分配过权限
          const existingAssignments = await this.databaseService.client.rolePermission.count({
            where: { roleId: role.id }
          });

          if (existingAssignments > 0) {
            console.log(`⚠️  角色 ${role.displayName} 已有权限分配，跳过`);
            continue;
          }

          // 获取权限ID
          const rolePermissions = await this.databaseService.client.permission.findMany({
            where: { name: { in: permissionNames } },
            select: { id: true },
          });

          const permissionIds = rolePermissions.map(p => p.id);

          await this.roleService.assignPermissions({
            roleId: role.id,
            permissionIds,
            assignedBy: 'system',
          });

          console.log(`✅ 为角色 ${role.displayName} 分配了 ${permissionIds.length} 个权限`);
        }
      }

      console.log('🎉 系统权限和角色初始化完成！');
    } catch (error) {
      console.error('❌ 初始化系统权限和角色失败:', error);
      throw error;
    }
  }

  /**
   * 清理并重新初始化
   */
  async reinitialize(): Promise<void> {
    try {
      console.log('🧹 清理现有数据...');
      
      // 删除非系统角色权限关联
      await this.databaseService.client.rolePermission.deleteMany({
        where: {
          role: { isSystem: true },
        },
      });

      // 重新初始化
      await this.initialize();
    } catch (error) {
      console.error('❌ 重新初始化失败:', error);
      throw error;
    }
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const initializer = new PermissionInitializer();
  
  const command = process.argv[2];
  
  if (command === 'reinit') {
    initializer.reinitialize()
      .then(() => {
        console.log('✅ 重新初始化完成');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ 重新初始化失败:', error);
        process.exit(1);
      });
  } else {
    initializer.initialize()
      .then(() => {
        console.log('✅ 初始化完成');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ 初始化失败:', error);
        process.exit(1);
      });
  }
}
