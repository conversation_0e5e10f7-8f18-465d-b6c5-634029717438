import { FastifyReply, FastifyRequest } from 'fastify';
import { z } from 'zod';
import { AssignPermissionData, AssignRoleDepartmentData, CreateRoleData, RoleService } from '../services/roleService.js';

// 请求验证schemas
const createRoleSchema = z.object({
  name: z.string().min(1).max(100),
  displayName: z.string().min(1).max(100),
  description: z.string().optional(),
  isSystem: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

const updateRoleSchema = z.object({
  displayName: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
});

const assignPermissionsSchema = z.object({
  permissionIds: z.array(z.string()),
});

const getRolesQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  pageSize: z.string().transform(Number).optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  isSystem: z.string().transform(val => val === 'true').optional(),
  search: z.string().optional(),
});

const getRoleUsersQuerySchema = z.object({
  page: z.string().transform(Number).optional(),
  pageSize: z.string().transform(Number).optional(),
  includeInherited: z.string().transform(val => val === 'true').optional(),
});

const assignRoleDepartmentsSchema = z.object({
  deptIds: z.array(z.number()),
  expiresAt: z.string().transform(val => val ? new Date(val) : undefined).optional(),
});


export class RoleController {
  constructor(private roleService: RoleService) {}

  /**
   * 创建角色
   */
  async createRole(request: FastifyRequest, reply: FastifyReply) {
    try {
      const body = createRoleSchema.parse(request.body);
      const user = request.user!;

      const roleData: CreateRoleData = {
        ...body,
        createdBy: user.userid,
      };

      const role = await this.roleService.createRole(roleData);

      return reply.send({
        success: true,
        data: role,
        message: '角色创建成功',
      });
    } catch (error: any) {
      console.error('创建角色失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '创建角色失败',
      });
    }
  }

  /**
   * 更新角色
   */
  async updateRole(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };
      const body = updateRoleSchema.parse(request.body);

      const role = await this.roleService.updateRole(roleId, body);

      return reply.send({
        success: true,
        data: role,
        message: '角色更新成功',
      });
    } catch (error: any) {
      console.error('更新角色失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '更新角色失败',
      });
    }
  }

  /**
   * 删除角色
   */
  async deleteRole(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };

      await this.roleService.deleteRole(roleId);

      return reply.send({
        success: true,
        message: '角色删除成功',
      });
    } catch (error: any) {
      console.error('删除角色失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '删除角色失败',
      });
    }
  }

  /**
   * 获取角色详情
   */
  async getRole(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };

      const role = await this.roleService.getRoleWithPermissions(roleId);

      if (!role) {
        return reply.status(404).send({
          success: false,
          message: '角色不存在',
        });
      }

      return reply.send({
        success: true,
        data: role,
        message: '获取角色详情成功',
      });
    } catch (error: any) {
      console.error('获取角色详情失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取角色详情失败',
      });
    }
  }

  /**
   * 获取角色列表
   */
  async getRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const query = getRolesQuerySchema.parse(request.query);

      const result = await this.roleService.getRoles(query);

      return reply.send({
        success: true,
        data: result,
        message: '获取角色列表成功',
      });
    } catch (error: any) {
      console.error('获取角色列表失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取角色列表失败',
      });
    }
  }

  /**
   * 为角色分配权限
   */
  async assignPermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };
      const body = assignPermissionsSchema.parse(request.body);
      const user = request.user!;

      const assignData: AssignPermissionData = {
        roleId,
        permissionIds: body.permissionIds,
        assignedBy: user.userid,
      };

      await this.roleService.assignPermissions(assignData);

      return reply.send({
        success: true,
        message: '权限分配成功',
      });
    } catch (error: any) {
      console.error('分配权限失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '分配权限失败',
      });
    }
  }

  /**
   * 获取角色的权限列表
   */
  async getRolePermissions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };

      const permissions = await this.roleService.getRolePermissions(roleId);

      return reply.send({
        success: true,
        data: {
          permissions,
          total: permissions.length,
        },
        message: '获取角色权限成功',
      });
    } catch (error: any) {
      console.error('获取角色权限失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取角色权限失败',
      });
    }
  }

  /**
   * 检查角色名称是否可用
   */
  async checkRoleName(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { name } = request.query as { name: string };
      const { roleId } = request.params as { roleId?: string };

      if (!name) {
        return reply.status(400).send({
          success: false,
          message: '角色名称不能为空',
        });
      }

      const isAvailable = await this.roleService.isRoleNameAvailable(name, roleId);

      return reply.send({
        success: true,
        data: {
          name,
          available: isAvailable,
        },
        message: isAvailable ? '角色名称可用' : '角色名称已存在',
      });
    } catch (error: any) {
      console.error('检查角色名称失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '检查角色名称失败',
      });
    }
  }

  /**
   * 获取角色关联的部门列表
   */
  async getRoleDepartments(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };

      const departments = await this.roleService.getRoleDepartments(roleId);

      return reply.send({
        success: true,
        data: {
          roleId,
          departments,
          total: departments.length,
        },
        message: '获取角色部门列表成功',
      });
    } catch (error: any) {
      console.error('获取角色部门失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取角色部门失败',
      });
    }
  }

  /**
   * 获取角色关联的用户列表（包括直接分配和部门继承）
   */
  async getRoleUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };
      const query = getRoleUsersQuerySchema.parse(request.query);

      const result = await this.roleService.getRoleUsers(roleId, query);

      return reply.send({
        success: true,
        data: {
          roleId,
          ...result,
        },
        message: '获取角色用户列表成功',
      });
    } catch (error: any) {
      console.error('获取角色用户失败:', error);
      return reply.status(500).send({
        success: false,
        message: error.message || '获取角色用户失败',
      });
    }
  }

  async assignRoleDepartments(request: FastifyRequest, reply: FastifyReply) {
    try {
      const { roleId } = request.params as { roleId: string };
      const body = assignRoleDepartmentsSchema.parse(request.body);
      const user = request.user!;

      const assignData: AssignRoleDepartmentData = {
        roleId,
        deptIds: body.deptIds,
        assignedBy: user.userid,
        expiresAt: body.expiresAt,
      };
      await this.roleService.assignRoleDepartments(assignData);

      return reply.send({
        success: true,
        message: '角色部门关联成功',
      });
    } catch (error: any) {
      console.error('角色部门关联失败:', error);
      return reply.status(400).send({
        success: false,
        message: error.message || '角色部门关联失败',
      });
    }
  }

}
