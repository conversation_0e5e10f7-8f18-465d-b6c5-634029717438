import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function debugBrands() {
  try {
    console.log('🔍 调试品牌数据...');
    
    // 查询所有品牌
    const brands = await prisma.brand.findMany();
    console.log('📊 数据库中的品牌数据:');
    console.log(JSON.stringify(brands, null, 2));
    
    // 查询品牌数量
    const count = await prisma.brand.count();
    console.log(`📈 品牌总数: ${count}`);
    
    // 查询第一个品牌的详细信息
    if (brands.length > 0) {
      const firstBrand = await prisma.brand.findUnique({
        where: { id: brands[0].id }
      });
      console.log('🔍 第一个品牌详细信息:');
      console.log(JSON.stringify(firstBrand, null, 2));
    }
    
  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugBrands();
