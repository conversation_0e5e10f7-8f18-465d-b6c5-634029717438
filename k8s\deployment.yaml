# Kubernetes 部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cantv-ding-backend
  namespace: cantv-ding-prod
  labels:
    app: cantv-ding-backend
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cantv-ding-backend
  template:
    metadata:
      labels:
        app: cantv-ding-backend
        version: v1
    spec:
      containers:
      - name: cantv-ding-backend
        image: registry.cn-hangzhou.aliyuncs.com/cantv-ding/cantv-ding-backend:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets
              key: database-url
        - name: DINGTALK_APP_KEY
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets
              key: dingtalk-app-key
        - name: DINGTALK_APP_SECRET
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets
              key: dingtalk-app-secret
        - name: DINGTALK_CORP_ID
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets
              key: dingtalk-corp-id
        - name: DINGTALK_AGENT_ID
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets
              key: dingtalk-agent-id
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: cantv-ding-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
      volumes:
      - name: app-logs
        emptyDir: {}
      imagePullSecrets:
      - name: aliyun-registry-secret

---
apiVersion: v1
kind: Service
metadata:
  name: cantv-ding-backend-service
  namespace: cantv-ding-prod
  labels:
    app: cantv-ding-backend
spec:
  selector:
    app: cantv-ding-backend
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cantv-ding-backend-ingress
  namespace: cantv-ding-prod
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.cantv-ding.com
    secretName: cantv-ding-tls
  rules:
  - host: api.cantv-ding.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cantv-ding-backend-service
            port:
              number: 80

---
apiVersion: v1
kind: Secret
metadata:
  name: cantv-ding-secrets
  namespace: cantv-ding-prod
type: Opaque
data:
  # 注意：这些值需要base64编码
  # 实际部署时请替换为真实的值
  database-url: cG9zdGdyZXNxbDovL3VzZXI6cGFzc3dvcmRAaG9zdDpwb3J0L2RhdGFiYXNl
  dingtalk-app-key: eW91ci1kaW5ndGFsay1hcHAta2V5
  dingtalk-app-secret: eW91ci1kaW5ndGFsay1hcHAtc2VjcmV0
  dingtalk-corp-id: eW91ci1kaW5ndGFsay1jb3JwLWlk
  dingtalk-agent-id: eW91ci1kaW5ndGFsay1hZ2VudC1pZA==
  jwt-secret: eW91ci1qd3Qtc2VjcmV0LWtleQ==
