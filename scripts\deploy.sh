#!/bin/bash

# CanTV钉钉后端服务部署脚本 - Docker Compose 版本
# 使用方法: ./scripts/deploy.sh [dev|prod] [version]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 1 ]; then
    log_error "使用方法: $0 [dev|prod] [version]"
    log_info "示例: $0 dev latest"
    log_info "示例: $0 prod v1.0.0"
    exit 1
fi

ENVIRONMENT=$1
VERSION=${2:-latest}

# 验证环境参数
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "环境参数必须是 'dev' 或 'prod'"
    exit 1
fi

# 配置变量
APP_NAME="cantv-ding-backend"
DOCKER_REGISTRY="registry.cn-hangzhou.aliyuncs.com"
DOCKER_NAMESPACE="cantv-ding"
IMAGE_NAME="${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/${APP_NAME}"

if [ "$ENVIRONMENT" = "dev" ]; then
    COMPOSE_FILE="docker-compose.yml"
    SERVICE_PORT="3001"
    DOMAIN="dev-api.cantv-ding.com"
else
    COMPOSE_FILE="docker-compose.prod.yml"
    SERVICE_PORT="3000"
    DOMAIN="api.cantv-ding.com"
fi

log_info "开始部署 ${APP_NAME} 到 ${ENVIRONMENT} 环境"
log_info "镜像版本: ${VERSION}"
log_info "Docker Compose 文件: ${COMPOSE_FILE}"

# 检查 Docker 和 Docker Compose
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装或不在PATH中"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose 未安装或不在PATH中"
    exit 1
fi

# 检查环境变量文件
ENV_FILE=".env.${ENVIRONMENT}"
if [ ! -f "$ENV_FILE" ]; then
    log_warning "环境变量文件 ${ENV_FILE} 不存在，请创建并配置必要的环境变量"
    log_info "示例内容:"
    log_info "DATABASE_URL=****************************************/cantv_ding"
    log_info "DINGTALK_APP_KEY=your_app_key"
    log_info "DINGTALK_APP_SECRET=your_app_secret"
    log_info "DINGTALK_CORP_ID=your_corp_id"
    log_info "DINGTALK_AGENT_ID=your_agent_id"
    log_info "JWT_SECRET=your_jwt_secret"
fi

# 设置镜像标签环境变量
export IMAGE_TAG=$VERSION
export NODE_ENV=$ENVIRONMENT

# 登录 Docker Registry（如果需要）
if [ -n "$DOCKER_USERNAME" ] && [ -n "$DOCKER_PASSWORD" ]; then
    log_info "登录 Docker Registry..."
    echo "$DOCKER_PASSWORD" | docker login "$DOCKER_REGISTRY" -u "$DOCKER_USERNAME" --password-stdin
fi

# 拉取最新镜像
log_info "拉取镜像 ${IMAGE_NAME}:${VERSION}"
docker pull "${IMAGE_NAME}:${VERSION}"

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p logs nginx/logs nginx/ssl

# 停止现有服务
log_info "停止现有服务..."
docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down || true

# 启动服务
log_info "启动服务..."
docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d

# 等待服务启动
log_info "等待服务启动..."
sleep 30

# 健康检查
log_info "执行健康检查..."
HEALTH_URL="http://localhost:${SERVICE_PORT}/api/health"

# 重试健康检查
for i in {1..10}; do
    if curl -f "$HEALTH_URL" &> /dev/null; then
        log_success "健康检查通过"
        break
    else
        log_warning "健康检查失败，重试 $i/10..."
        sleep 10
    fi

    if [ $i -eq 10 ]; then
        log_error "健康检查失败，请检查服务状态"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs cantv-ding-backend
        exit 1
    fi
done

# 显示服务状态
log_info "服务状态:"
docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps

# 显示访问信息
log_success "部署完成!"
if [ "$ENVIRONMENT" = "prod" ]; then
    log_info "应用访问地址: https://${DOMAIN}"
    log_info "健康检查地址: https://${DOMAIN}/api/health"
else
    log_info "应用访问地址: http://${DOMAIN}:${SERVICE_PORT}"
    log_info "健康检查地址: http://localhost:${SERVICE_PORT}/api/health"
fi

# 显示有用的命令
log_info "有用的命令:"
log_info "查看服务日志: docker-compose -f ${COMPOSE_FILE} --env-file ${ENV_FILE} logs -f cantv-ding-backend"
log_info "查看所有日志: docker-compose -f ${COMPOSE_FILE} --env-file ${ENV_FILE} logs -f"
log_info "停止服务: docker-compose -f ${COMPOSE_FILE} --env-file ${ENV_FILE} down"
log_info "重启服务: docker-compose -f ${COMPOSE_FILE} --env-file ${ENV_FILE} restart cantv-ding-backend"

log_success "部署脚本执行完成!"
