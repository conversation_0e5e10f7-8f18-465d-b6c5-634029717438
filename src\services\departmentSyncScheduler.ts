/**
 * 部门同步调度器
 * 定时同步钉钉部门数据到本地数据库
 */

import cron from 'node-cron';
import { DatabaseService } from './database.js';
import { DepartmentSyncService } from './departmentSync.js';
import { DingTalkService } from './dingtalk.js';

class DepartmentSyncScheduler {
  private databaseService: DatabaseService;
  private dingTalkService: DingTalkService;
  private departmentSyncService: DepartmentSyncService;
  private isRunning = false;
  private lastSyncTime: Date | null = null;
  private syncStats = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    lastError: null as string | null
  };

  constructor() {
    this.databaseService = new DatabaseService();
    this.dingTalkService = new DingTalkService();
    this.departmentSyncService = new DepartmentSyncService(
      this.databaseService,
      this.dingTalkService,
      {
        syncIntervalHours: 24, // 24小时同步一次
        batchSize: 50,
        maxRetries: 3
      }
    );

    this.initializeScheduler();
  }

  /**
   * 初始化调度器
   */
  private initializeScheduler(): void {
    // 每天凌晨2点执行部门同步
    cron.schedule('0 2 * * *', async () => {
      await this.performSync();
    }, {
      timezone: 'Asia/Shanghai'
    });

    // 每6小时执行一次增量同步（检查是否有新部门或变更）
    cron.schedule('0 */6 * * *', async () => {
      await this.performIncrementalSync();
    }, {
      timezone: 'Asia/Shanghai'
    });

    console.log('📅 部门同步调度器已启动');
    console.log('⏰ 完整同步: 每天凌晨2点');
    console.log('⏰ 增量同步: 每6小时');

    // 启动时执行一次同步（延迟30秒，等待服务完全启动）
    setTimeout(async () => {
      console.log('🚀 执行启动时部门同步...');
      await this.performSync();
    }, 30000);
  }

  /**
   * 执行完整同步
   */
  private async performSync(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 部门同步正在进行中，跳过本次同步');
      return;
    }

    this.isRunning = true;
    this.syncStats.totalSyncs++;

    try {
      console.log('🔄 开始部门数据同步...');
      const startTime = Date.now();

      const result = await this.departmentSyncService.syncAllDepartments();

      const duration = Date.now() - startTime;
      this.lastSyncTime = new Date();
      this.syncStats.successfulSyncs++;
      this.syncStats.lastError = null;

      console.log(`✅ 部门同步完成 (耗时: ${duration}ms)`);
      console.log(`📊 同步结果: 成功 ${result.success}, 失败 ${result.failed}`);

      if (result.errors.length > 0) {
        console.log('⚠️ 同步过程中的错误:');
        result.errors.forEach(error => {
          console.log(`  - 部门 ${error.deptId}: ${error.error}`);
        });
      }

    } catch (error) {
      this.syncStats.failedSyncs++;
      this.syncStats.lastError = error instanceof Error ? error.message : String(error);
      
      console.error('❌ 部门同步失败:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 执行增量同步
   */
  private async performIncrementalSync(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 部门同步正在进行中，跳过增量同步');
      return;
    }

    try {
      console.log('🔄 开始部门增量同步...');

      // 递归获取钉钉所有层级的部门列表
      const dingTalkDepartments = await this.dingTalkService.getAllDepartments();

      if (!dingTalkDepartments || dingTalkDepartments.length === 0) {
        console.log('📭 未获取到钉钉部门数据，跳过增量同步');
        return;
      }

      // 获取本地部门列表
      const localDepartments = await this.databaseService.getAllDepartments();
      const localDeptIds = new Set(localDepartments.map(dept => dept.deptId));

      // 找出新增的部门
      const newDepartments = dingTalkDepartments.filter(dept => !localDeptIds.has(dept.dept_id));

      if (newDepartments.length > 0) {
        console.log(`📝 发现 ${newDepartments.length} 个新部门，开始同步...`);
        
        // 同步新部门
        let syncedCount = 0;
        for (const dept of newDepartments) {
          try {
            const departmentInfo = {
              deptId: dept.dept_id,
              name: dept.name,
              parentId: dept.parent_id || 1,
              createDeptGroup: dept.create_dept_group || false,
              autoAddUser: dept.auto_add_user || false,
              fromUnionOrg: false, // 钉钉API中没有这个字段，使用默认值
              tags: undefined, // 钉钉API中没有这个字段
              order: dept.order || 0,
              deptManagerUseridList: [], // 钉钉API中没有这个字段，需要单独获取
              outerDept: false, // 钉钉API中没有这个字段，使用默认值
              outerPermitDepts: [], // 钉钉API中没有这个字段
              outerPermitUsers: [], // 钉钉API中没有这个字段
              orgDeptOwner: undefined, // 钉钉API中没有这个字段
              deptPerimits: 0, // 钉钉API中没有这个字段
              userPerimits: 0, // 钉钉API中没有这个字段
              outerDeptOnlySelf: false, // 钉钉API中没有这个字段
              sourceIdentifier: undefined, // 钉钉API中没有这个字段
              ext: undefined, // 钉钉API中没有这个字段
              hideSceneConfig: undefined, // 钉钉API中没有这个字段
            };

            await this.databaseService.upsertDepartment(departmentInfo);
            syncedCount++;
            console.log(`✅ 新部门同步成功: ${dept.name}(${dept.dept_id})`);
          } catch (error) {
            console.error(`❌ 新部门同步失败: ${dept.name}(${dept.dept_id})`, error);
          }
        }

        console.log(`✅ 增量同步完成，成功同步 ${syncedCount}/${newDepartments.length} 个新部门`);
      } else {
        console.log('📋 未发现新部门，增量同步完成');
      }

    } catch (error) {
      console.error('❌ 部门增量同步失败:', error);
    }
  }

  /**
   * 手动触发同步
   */
  public async triggerSync(): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    if (this.isRunning) {
      return {
        success: false,
        message: '部门同步正在进行中，请稍后再试'
      };
    }

    try {
      await this.performSync();
      return {
        success: true,
        message: '部门同步已完成',
        data: this.getStats()
      };
    } catch (error) {
      return {
        success: false,
        message: '部门同步失败',
        data: {
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  /**
   * 获取同步统计信息
   */
  public getStats(): {
    isRunning: boolean;
    lastSyncTime: Date | null;
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    lastError: string | null;
  } {
    return {
      isRunning: this.isRunning,
      lastSyncTime: this.lastSyncTime,
      ...this.syncStats
    };
  }

  /**
   * 获取部门同步服务实例
   */
  public getDepartmentSyncService(): DepartmentSyncService {
    return this.departmentSyncService;
  }
}

// 创建全局实例
const departmentSyncScheduler = new DepartmentSyncScheduler();

// 导出实例和类
export { departmentSyncScheduler, DepartmentSyncScheduler };

