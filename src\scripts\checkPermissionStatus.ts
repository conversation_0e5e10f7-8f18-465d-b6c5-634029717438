import { DatabaseService } from '../services/database.js';

/**
 * 权限系统状态检查工具
 */
export class PermissionStatusChecker {
  private databaseService: DatabaseService;

  constructor() {
    this.databaseService = new DatabaseService();
  }

  /**
   * 检查权限系统状态
   */
  async checkStatus(): Promise<{
    isInitialized: boolean;
    roleCount: number;
    permissionCount: number;
    rolePermissionCount: number;
    systemRoles: string[];
    missingSystemRoles: string[];
  }> {
    try {
      console.log('🔍 检查权限系统状态...');

      // 获取基本统计信息
      const [roleCount, permissionCount, rolePermissionCount] = await Promise.all([
        this.databaseService.client.role.count(),
        this.databaseService.client.permission.count(),
        this.databaseService.client.rolePermission.count()
      ]);

      console.log(`📊 角色数量: ${roleCount}`);
      console.log(`📊 权限数量: ${permissionCount}`);
      console.log(`📊 角色权限关联数量: ${rolePermissionCount}`);

      // 检查系统角色
      const systemRoles = await this.databaseService.client.role.findMany({
        where: { isSystem: true },
        select: { name: true, displayName: true }
      });

      const systemRoleNames = systemRoles.map(r => r.name);
      console.log(`🔧 系统角色: ${systemRoleNames.join(', ')}`);

      // 预期的系统角色
      const expectedSystemRoles = ['super_admin', 'admin', 'PMO', 'PM', 'finance_manager', 'user'];
      const missingSystemRoles = expectedSystemRoles.filter(role => !systemRoleNames.includes(role));

      if (missingSystemRoles.length > 0) {
        console.log(`⚠️  缺失的系统角色: ${missingSystemRoles.join(', ')}`);
      }

      // 判断是否已初始化
      const isInitialized = roleCount > 0 && permissionCount > 0 && missingSystemRoles.length === 0;

      if (isInitialized) {
        console.log('✅ 权限系统已完整初始化');
      } else {
        console.log('❌ 权限系统未完整初始化');
      }

      return {
        isInitialized,
        roleCount,
        permissionCount,
        rolePermissionCount,
        systemRoles: systemRoleNames,
        missingSystemRoles
      };

    } catch (error) {
      console.error('❌ 检查权限系统状态失败:', error);
      throw error;
    }
  }

  /**
   * 检查特定角色的权限分配
   */
  async checkRolePermissions(roleName: string): Promise<void> {
    try {
      const role = await this.databaseService.client.role.findUnique({
        where: { name: roleName },
        include: {
          rolePermissions: {
            include: {
              permission: true
            }
          }
        }
      });

      if (!role) {
        console.log(`❌ 角色 '${roleName}' 不存在`);
        return;
      }

      console.log(`\n🔍 角色 '${role.displayName}' (${role.name}) 的权限:`);
      console.log(`📊 权限数量: ${role.rolePermissions.length}`);

      if (role.rolePermissions.length > 0) {
        console.log('📋 权限列表:');
        role.rolePermissions.forEach(rp => {
          console.log(`  - ${rp.permission.displayName} (${rp.permission.name})`);
        });
      } else {
        console.log('⚠️  该角色没有分配任何权限');
      }

    } catch (error) {
      console.error(`❌ 检查角色 '${roleName}' 权限失败:`, error);
      throw error;
    }
  }

  /**
   * 生成权限系统报告
   */
  async generateReport(): Promise<void> {
    try {
      console.log('\n📋 生成权限系统详细报告...\n');

      const status = await this.checkStatus();

      console.log('\n=== 权限系统状态报告 ===');
      console.log(`初始化状态: ${status.isInitialized ? '✅ 已初始化' : '❌ 未初始化'}`);
      console.log(`角色总数: ${status.roleCount}`);
      console.log(`权限总数: ${status.permissionCount}`);
      console.log(`角色权限关联总数: ${status.rolePermissionCount}`);

      if (status.missingSystemRoles.length > 0) {
        console.log(`\n⚠️  缺失的系统角色:`);
        status.missingSystemRoles.forEach(role => {
          console.log(`  - ${role}`);
        });
      }

      console.log('\n=== 系统角色权限详情 ===');
      for (const roleName of status.systemRoles) {
        await this.checkRolePermissions(roleName);
      }

      console.log('\n=== 报告生成完成 ===\n');

    } catch (error) {
      console.error('❌ 生成权限系统报告失败:', error);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.databaseService.client.$disconnect();
  }
}

// 如果直接运行此脚本
if (process.argv[1] && import.meta.url.endsWith(process.argv[1].replace(/\\/g, '/'))) {
  const checker = new PermissionStatusChecker();
  
  const command = process.argv[2] || 'status';
  
  const runCommand = async () => {
    try {
      switch (command) {
        case 'status':
          await checker.checkStatus();
          break;
        case 'report':
          await checker.generateReport();
          break;
        case 'role':
          const roleName = process.argv[3];
          if (!roleName) {
            console.error('❌ 请指定角色名称: npm run check:permissions role <role_name>');
            process.exit(1);
          }
          await checker.checkRolePermissions(roleName);
          break;
        default:
          console.log('用法:');
          console.log('  npm run check:permissions status  - 检查权限系统状态');
          console.log('  npm run check:permissions report  - 生成详细报告');
          console.log('  npm run check:permissions role <name> - 检查特定角色权限');
          break;
      }
    } catch (error) {
      console.error('❌ 执行失败:', error);
      process.exit(1);
    } finally {
      await checker.cleanup();
    }
  };

  runCommand();
}
