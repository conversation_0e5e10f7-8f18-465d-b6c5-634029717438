/**
 * 钉钉Stream推送服务 V2 - 更新版本
 * 基于已验证的simple版本实现，集成审批服务
 * 
 * 参考: https://github.com/open-dingtalk/dingtalk-stream-sdk-nodejs
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { ApprovalService } from './approval.js';

// 钉钉API常量
const GET_TOKEN_URL = 'https://oapi.dingtalk.com/gettoken';
const GATEWAY_URL = 'https://api.dingtalk.com/v1.0/gateway/connections/open';

// 消息接口定义
export interface DWClientDownStream {
  specVersion: string;
  type: string;
  headers: {
    appId: string;
    connectionId: string;
    contentType: string;
    messageId: string;
    time: string;
    topic: string;
    eventType?: string;
    eventBornTime?: string;
    eventId?: string;
    eventCorpId?: string;
    eventUnifiedAppId?: string;
  };
  data: string;
}

export class DingTalkStreamServiceV2 extends EventEmitter {
  private approvalService: ApprovalService;
  private ws: WebSocket | null = null;
  private isConnected = false;
  private clientId: string;
  private clientSecret: string;
  private accessToken: string = '';
  private dwUrl: string = '';
  private userDisconnect = false;
  private reconnectInterval = 5000;
  private autoReconnect = true;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor() {
    super();
    this.approvalService = new ApprovalService();
    this.clientId = process.env.DINGTALK_APP_KEY || '';
    this.clientSecret = process.env.DINGTALK_APP_SECRET || '';
    
    if (!this.clientId || !this.clientSecret) {
      throw new Error('缺少钉钉应用配置信息: DINGTALK_APP_KEY 或 DINGTALK_APP_SECRET');
    }
  }

  /**
   * 启动Stream服务
   */
  async start(): Promise<void> {
    try {
      console.log('🚀 启动钉钉Stream服务V2...');
      
      // 1. 获取访问令牌
      console.log('🔑 获取访问令牌...');
      await this.getAccessToken();
      console.log('✅ 访问令牌获取成功');

      // 2. 获取Stream连接信息
      console.log('📡 获取Stream连接信息...');
      await this.getEndpoint();
      console.log('✅ Stream连接信息获取成功');

      // 3. 建立WebSocket连接
      console.log('🔗 建立WebSocket连接...');
      await this.connectWebSocket();
      console.log('✅ WebSocket连接建立成功');

      console.log('✅ 钉钉Stream服务V2启动成功');
    } catch (error) {
      console.error('❌ 启动钉钉Stream服务V2失败:', error);
      throw error;
    }
  }

  /**
   * 停止Stream服务
   */
  async stop(): Promise<void> {
    console.log('🛑 停止钉钉Stream服务V2...');
    
    this.userDisconnect = true;
    this.isConnected = false;
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    console.log('✅ 钉钉Stream服务V2已停止');
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<void> {
    try {
      const url = `${GET_TOKEN_URL}?appkey=${this.clientId}&appsecret=${this.clientSecret}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`获取访问令牌失败: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.errcode !== 0) {
        throw new Error(`获取访问令牌失败: ${data.errmsg}`);
      }
      
      this.accessToken = data.access_token;
    } catch (error) {
      console.error('❌ 获取访问令牌失败:', error);
      throw error;
    }
  }

  /**
   * 获取Stream端点
   */
  private async getEndpoint(): Promise<void> {
    try {
      const requestBody = {
        clientId: this.clientId,
        clientSecret: this.clientSecret,
        ua: 'DingTalkStream/2.0.0',
        subscriptions: [
          {
            type: 'CALLBACK',
            topic: '*'
          }
        ]
      };

      console.log('📤 请求Stream端点:', {
        clientId: this.clientId.substring(0, 8) + '...',
        clientSecret: '***',
        subscriptions: requestBody.subscriptions
      });

      const response = await fetch(GATEWAY_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('📥 响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ 响应内容:', errorText);
        throw new Error(`获取Stream端点失败: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('📥 响应数据:', data);

      if (!data.endpoint || !data.ticket) {
        throw new Error('响应中缺少endpoint或ticket字段');
      }

      // 构建WebSocket URL
      this.dwUrl = `${data.endpoint}?ticket=${data.ticket}`;
      console.log('✅ Stream端点构建成功');
    } catch (error) {
      console.error('❌ 获取Stream端点失败:', error);
      throw error;
    }
  }

  /**
   * 建立WebSocket连接
   */
  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('🔗 连接到WebSocket端点:', this.dwUrl);

      this.ws = new WebSocket(this.dwUrl);

      // 连接超时处理
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket连接超时'));
      }, 10000);

      this.ws.on('open', () => {
        clearTimeout(timeout);
        this.isConnected = true;
        this.reconnectAttempts = 0;
        console.log('✅ WebSocket连接已建立');
        this.setupEventHandlers();
        resolve();
      });

      this.ws.on('error', (error) => {
        clearTimeout(timeout);
        console.error('❌ WebSocket连接错误:', error);
        reject(error);
      });

      this.ws.on('close', (code, reason) => {
        this.isConnected = false;
        console.log(`🔌 WebSocket连接已关闭: ${code} ${reason.toString()}`);
        
        // 自动重连
        if (this.autoReconnect && !this.userDisconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      });
    });
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.on('message', (data: WebSocket.Data) => {
      try {
        const message = data.toString();
        this.handleMessage(message);
      } catch (error) {
        console.error('❌ 处理消息失败:', error);
      }
    });

    this.ws.on('ping', () => {
      console.log('💓 收到ping');
      if (this.ws) {
        this.ws.pong();
      }
    });
  }

  /**
   * 处理收到的消息
   */
  private handleMessage(data: string): void {
    try {
      console.log('📥 收到原始消息:', data.substring(0, 200));
      
      const message = JSON.parse(data) as DWClientDownStream;
      console.log('📨 解析后的消息:', {
        type: message.type,
        topic: message.headers?.topic,
        eventType: message.headers?.eventType,
        messageId: message.headers?.messageId
      });

      // 根据消息类型处理
      switch (message.type) {
        case 'SYSTEM':
          this.onSystem(message);
          break;
        case 'EVENT':
          this.onEvent(message);
          break;
        case 'CALLBACK':
          this.onCallback(message);
          break;
        default:
          console.log(`❓ 未处理的消息类型: ${message.type}`);
      }
    } catch (error) {
      console.error('❌ 处理消息失败:', error);
    }
  }

  /**
   * 处理系统消息
   */
  private onSystem(message: DWClientDownStream): void {
    const topic = message.headers.topic;
    console.log('🔧 处理系统消息:', topic);

    switch (topic) {
      case 'CONNECTED':
        console.log('✅ 连接已建立');
        break;
      case 'REGISTERED':
        console.log('✅ 注册成功');
        break;
      case 'ping':
        console.log('💓 收到ping，发送pong');
        this.sendResponse(message.headers, message.data);
        break;
      case 'KEEPALIVE':
        console.log('💓 保活消息');
        break;
      default:
        console.log(`❓ 未处理的系统消息: ${topic}`);
    }
  }

  /**
   * 处理事件消息
   */
  private onEvent(message: DWClientDownStream): void {
    console.log('📋 处理事件消息:', message.headers.eventType);
    
    // 发送ACK确认
    this.sendResponse(message.headers.messageId, { status: 'SUCCESS' });
    
    // 触发事件
    this.emit('event', message);
  }

  /**
   * 处理回调消息
   */
  private async onCallback(message: DWClientDownStream): Promise<void> {
    console.log('📞 处理回调消息:', message.headers.topic);
    
    // 发送ACK确认
    this.sendResponse(message.headers.messageId, 'OK');
    
    // 根据事件类型处理
    if (message.headers.eventType === 'bpms_instance_change') {
      console.log('📋 收到审批状态变更事件');
      
      try {
        // 解析审批数据
        const approvalData = JSON.parse(message.data);
        
        // 调用审批服务处理
        const result = await this.approvalService.handleApprovalStatusChange({
          processInstanceId: approvalData.processInstanceId,
          result: approvalData.result || 'unknown',
          type: approvalData.type || 'unknown',
          staffId: approvalData.staffId || '',
          createTime: approvalData.createTime || Date.now(),
          finishTime: approvalData.finishTime,
          corpId: approvalData.corpId || ''
        });

        console.log('✅ 审批状态变更处理结果:', result);
        this.emit('approval_change', approvalData);
      } catch (error) {
        console.error('❌ 处理审批状态变更失败:', error);
      }
    }
    
    // 触发通用回调事件
    this.emit('callback', message);
    this.emit(message.headers.topic, message);
  }

  /**
   * 发送响应消息
   */
  private sendResponse(headers: any, data: any): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ WebSocket未连接，无法发送响应');
      return;
    }

    try {
      const response = {
        code: 200,
        headers,
        data: data
      };

      this.ws.send(JSON.stringify(response));
      console.log('📤 发送响应:', response);
    } catch (error) {
      console.error('❌ 发送响应失败:', error);
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectInterval * this.reconnectAttempts;
    
    console.log(`🔄 ${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);
    
    setTimeout(async () => {
      try {
        await this.start();
      } catch (error) {
        console.error('❌ 重连失败:', error);
      }
    }, delay);
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    reconnectAttempts: number;
    readyState?: number;
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      readyState: this.ws?.readyState
    };
  }
}
