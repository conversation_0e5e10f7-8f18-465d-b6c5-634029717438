#!/usr/bin/env node

/**
 * Redis 连接和功能测试脚本
 */

import { redisService } from '../src/services/redis.js';
import { jwtService } from '../src/services/jwt.js';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

async function testRedisConnection() {
  log('\n🔗 测试 Redis 连接...', colors.cyan);
  
  try {
    await redisService.connect();
    
    if (redisService.isReady()) {
      success('Redis 连接成功');
      
      // 测试 ping
      const pingResult = await redisService.ping();
      success(`Ping 响应: ${pingResult}`);
      
      return true;
    } else {
      error('Redis 连接失败');
      return false;
    }
  } catch (err) {
    error(`Redis 连接错误: ${err.message}`);
    return false;
  }
}

async function testBasicOperations() {
  log('\n🧪 测试基本 Redis 操作...', colors.cyan);
  
  try {
    // 测试 SET/GET
    const testKey = 'test:redis:basic';
    const testValue = 'Hello Redis!';
    
    await redisService.set(testKey, testValue, 60); // 60秒过期
    success('SET 操作成功');
    
    const getValue = await redisService.get(testKey);
    if (getValue === testValue) {
      success('GET 操作成功');
    } else {
      error(`GET 操作失败: 期望 "${testValue}", 实际 "${getValue}"`);
    }
    
    // 测试 EXISTS
    const exists = await redisService.exists(testKey);
    if (exists) {
      success('EXISTS 操作成功');
    } else {
      error('EXISTS 操作失败');
    }
    
    // 测试 TTL
    const ttl = await redisService.ttl(testKey);
    if (ttl > 0) {
      success(`TTL 操作成功: ${ttl} 秒`);
    } else {
      warning(`TTL 操作结果: ${ttl}`);
    }
    
    // 测试 DEL
    const deleted = await redisService.del(testKey);
    if (deleted > 0) {
      success('DEL 操作成功');
    } else {
      error('DEL 操作失败');
    }
    
    return true;
  } catch (err) {
    error(`基本操作测试失败: ${err.message}`);
    return false;
  }
}

async function testHashOperations() {
  log('\n📊 测试 Hash 操作...', colors.cyan);
  
  try {
    const hashKey = 'test:redis:hash';
    
    // 测试 HSET
    await redisService.hset(hashKey, 'field1', 'value1');
    await redisService.hset(hashKey, 'field2', 'value2');
    success('HSET 操作成功');
    
    // 测试 HGET
    const value1 = await redisService.hget(hashKey, 'field1');
    if (value1 === 'value1') {
      success('HGET 操作成功');
    } else {
      error(`HGET 操作失败: ${value1}`);
    }
    
    // 测试 HGETALL
    const allFields = await redisService.hgetall(hashKey);
    if (allFields.field1 === 'value1' && allFields.field2 === 'value2') {
      success('HGETALL 操作成功');
    } else {
      error('HGETALL 操作失败');
    }
    
    // 清理
    await redisService.del(hashKey);
    
    return true;
  } catch (err) {
    error(`Hash 操作测试失败: ${err.message}`);
    return false;
  }
}

async function testJWTIntegration() {
  log('\n🔐 测试 JWT + Redis 集成...', colors.cyan);
  
  try {
    // 创建测试用户会话
    const testUser = {
      userid: 'test_user_001',
      name: '测试用户',
      mobile: '13800138000',
      deptIds: [1, 2],
      isAdmin: false,
      isBoss: false
    };
    
    // 生成认证响应
    const authResponse = await jwtService.generateAuthResponse(testUser);
    success('JWT 认证响应生成成功');
    info(`Access Token: ${authResponse.accessToken.substring(0, 50)}...`);
    
    // 验证访问令牌
    const payload = await jwtService.verifyAccessToken(authResponse.accessToken);
    if (payload && payload.userid === testUser.userid) {
      success('JWT 令牌验证成功');
    } else {
      error('JWT 令牌验证失败');
    }
    
    // 获取用户会话
    const session = await jwtService.getUserSession(testUser.userid);
    if (session && session.userid === testUser.userid) {
      success('用户会话获取成功');
    } else {
      error('用户会话获取失败');
    }
    
    // 刷新令牌
    const refreshResult = await jwtService.refreshAccessToken(authResponse.refreshToken);
    if (refreshResult && refreshResult.accessToken) {
      success('令牌刷新成功');
    } else {
      error('令牌刷新失败');
    }
    
    // 登出用户
    await jwtService.logout(testUser.userid, authResponse.accessToken);
    success('用户登出成功');
    
    // 验证会话已删除
    const deletedSession = await jwtService.getUserSession(testUser.userid);
    if (!deletedSession) {
      success('用户会话已正确删除');
    } else {
      warning('用户会话可能未完全删除');
    }
    
    return true;
  } catch (err) {
    error(`JWT 集成测试失败: ${err.message}`);
    return false;
  }
}

async function testSessionStats() {
  log('\n📈 测试会话统计...', colors.cyan);
  
  try {
    // 创建多个测试会话
    const testUsers = [
      { userid: 'admin_001', name: '管理员1', mobile: '13800138001', deptIds: [1], isAdmin: true, isBoss: false },
      { userid: 'boss_001', name: '老板1', mobile: '13800138002', deptIds: [1], isAdmin: false, isBoss: true },
      { userid: 'user_001', name: '用户1', mobile: '13800138003', deptIds: [2], isAdmin: false, isBoss: false },
    ];
    
    for (const user of testUsers) {
      await jwtService.generateAuthResponse(user);
    }
    success(`创建了 ${testUsers.length} 个测试会话`);
    
    // 获取统计信息
    const stats = await jwtService.getActiveSessionsStats();
    success('会话统计获取成功');
    info(`总会话数: ${stats.totalSessions}`);
    info(`管理员会话: ${stats.adminSessions}`);
    info(`老板会话: ${stats.bossSessions}`);
    info(`最近活跃会话: ${stats.recentSessions}`);
    info(`Redis 状态: ${stats.redisEnabled ? '已启用' : '未启用'}`);
    
    // 清理测试会话
    for (const user of testUsers) {
      await jwtService.logout(user.userid);
    }
    success('测试会话已清理');
    
    return true;
  } catch (err) {
    error(`会话统计测试失败: ${err.message}`);
    return false;
  }
}

async function testRedisInfo() {
  log('\n📋 获取 Redis 信息...', colors.cyan);
  
  try {
    if (!redisService.isReady()) {
      warning('Redis 未连接，跳过信息获取');
      return true;
    }
    
    const info = await redisService.info();
    const lines = info.split('\n').filter(line => line && !line.startsWith('#'));
    
    success('Redis 信息获取成功');
    
    // 显示关键信息
    const keyInfo = lines.filter(line => 
      line.includes('redis_version') ||
      line.includes('connected_clients') ||
      line.includes('used_memory_human') ||
      line.includes('keyspace_hits') ||
      line.includes('keyspace_misses')
    );
    
    keyInfo.forEach(line => {
      info(`  ${line}`);
    });
    
    return true;
  } catch (err) {
    error(`Redis 信息获取失败: ${err.message}`);
    return false;
  }
}

async function runAllTests() {
  log('🚀 开始 Redis 功能测试', colors.bright);
  
  const tests = [
    { name: 'Redis 连接', fn: testRedisConnection },
    { name: '基本操作', fn: testBasicOperations },
    { name: 'Hash 操作', fn: testHashOperations },
    { name: 'JWT 集成', fn: testJWTIntegration },
    { name: '会话统计', fn: testSessionStats },
    { name: 'Redis 信息', fn: testRedisInfo }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (err) {
      error(`测试 "${test.name}" 发生异常: ${err.message}`);
      failed++;
    }
  }
  
  log('\n📊 测试结果汇总', colors.bright);
  success(`通过: ${passed} 项`);
  if (failed > 0) {
    error(`失败: ${failed} 项`);
  }
  
  // 清理并关闭连接
  try {
    await redisService.disconnect();
    info('Redis 连接已关闭');
  } catch (err) {
    warning(`关闭 Redis 连接时出错: ${err.message}`);
  }
  
  process.exit(failed > 0 ? 1 : 0);
}

// 运行测试
runAllTests().catch(err => {
  error(`测试运行失败: ${err.message}`);
  process.exit(1);
});
