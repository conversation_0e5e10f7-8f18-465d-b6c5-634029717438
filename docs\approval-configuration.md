# 钉钉审批流程配置指南

## 🎯 概述

本文档介绍如何配置钉钉对公付款审批流程，包括环境变量配置、钉钉后台设置和代码配置。

## 📋 环境变量配置

### 1. 审批流程代码配置

在 `.env` 文件中配置审批流程代码：

```env
# 审批配置
# 对公付款审批流程代码（从钉钉后台获取）
APPROVAL_PROCESS_CODE_PAYMENT=PROC-PAYMENT-001
# 费用报销审批流程代码
APPROVAL_PROCESS_CODE_EXPENSE=PROC-EXPENSE-001
# 合同审批流程代码
APPROVAL_PROCESS_CODE_CONTRACT=PROC-CONTRACT-001
```

### 2. 获取审批流程代码

1. **登录钉钉管理后台**
   - 访问 [钉钉开放平台](https://open.dingtalk.com)
   - 进入企业管理后台

2. **创建审批模板**
   - 工作台 → 审批 → 管理后台
   - 创建新的审批模板
   - 设置表单字段（参考下方字段配置）

3. **获取流程代码**
   - 在审批模板详情页面
   - 复制流程代码（格式：PROC-xxxxxxxx）
   - 更新环境变量

## 🔧 钉钉审批模板配置

### 对公付款审批模板字段

| 字段名称 | 字段类型 | 是否必填 | 说明 |
|---------|---------|---------|------|
| 申请人 | 联系人 | 是 | 自动填充当前用户 |
| 申请部门 | 部门 | 是 | 自动填充用户部门 |
| 关联审批单 | 单行文本 | 否 | 关联的其他审批单号 |
| 所属项目 | 单行文本 | 是 | 项目名称 |
| 付款事由 | 多行文本 | 是 | 详细的付款原因 |
| 合同签署主体 | 单选 | 是 | 公司A/公司B/子公司/其他 |
| 付款总额 | 数字 | 是 | 申请付款的金额 |
| 期望付款时间 | 日期 | 是 | 希望完成付款的日期 |
| 付款方式 | 单选 | 是 | 银行转账/网银支付/支票/现金/其他 |
| 收款账号 | 明细 | 是 | 包含账户名称、账号、开户银行等 |
| 发票文件 | 图片 | 否 | 上传发票图片或PDF |
| 附件 | 附件 | 否 | 合同、协议等相关文件 |
| 备注 | 多行文本 | 否 | 其他说明信息 |

### 审批流程设置

1. **审批人配置**
   ```
   发起人 → 直接主管 → 财务主管 → 总经理
   ```

2. **条件分支**
   - 金额 ≤ 1万：直接主管审批
   - 金额 1万-10万：直接主管 + 财务主管
   - 金额 > 10万：直接主管 + 财务主管 + 总经理

3. **抄送人员**
   - 财务部门
   - 项目负责人
   - 相关业务人员

## ⚙️ 代码配置说明

### 1. 审批配置文件

`src/config/approval.ts` 包含所有审批相关的配置：

```typescript
export const APPROVAL_CONFIG = {
  // 审批流程代码（从环境变量读取）
  PROCESS_CODES: {
    PAYMENT: env.APPROVAL_PROCESS_CODE_PAYMENT,
    EXPENSE: env.APPROVAL_PROCESS_CODE_EXPENSE,
    CONTRACT: env.APPROVAL_PROCESS_CODE_CONTRACT,
  },

  // 表单字段映射
  FORM_FIELDS: {
    PAYMENT: {
      APPLICANT: 'applicant',
      PAYMENT_REASON: 'paymentReason',
      TOTAL_AMOUNT: 'totalAmount',
      // ... 其他字段
    }
  },

  // 状态映射
  STATUS_MAPPING: {
    DINGTALK_TO_SYSTEM: {
      'COMPLETED': 'APPROVED',
      'TERMINATED': 'REJECTED',
      // ... 其他状态
    }
  }
};
```

### 2. 配置辅助类

`ApprovalConfigHelper` 提供了便捷的配置操作方法：

```typescript
// 获取流程代码
const processCode = ApprovalConfigHelper.getProcessCode('PAYMENT');

// 状态转换
const systemStatus = ApprovalConfigHelper.mapDingTalkStatusToSystem('COMPLETED');

// 验证配置
const isValid = ApprovalConfigHelper.isValidProcessCode('PROC-PAYMENT-001');
```

## 🔄 配置更新流程

### 1. 开发环境

1. 修改 `.env.dev` 文件
2. 重启开发服务器
3. 测试审批流程

### 2. 生产环境

1. 更新 `.env.prod` 文件
2. 重新部署应用
3. 验证配置生效

### 3. Docker环境

1. 更新 `docker-compose.yml` 中的环境变量
2. 重新构建和启动容器：
   ```bash
   docker-compose down
   docker-compose up -d --build
   ```

## 🧪 配置验证

### 1. 环境变量验证

系统启动时会自动验证环境变量：

```typescript
// 在 src/config/env.ts 中定义验证规则
const envSchema = z.object({
  APPROVAL_PROCESS_CODE_PAYMENT: z.string().default('PROC-PAYMENT-001'),
  // ... 其他配置
});
```

### 2. 配置测试

```bash
# 测试审批流程代码是否有效
curl -X POST http://localhost:3000/api/weekly-budgets/approval \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{"weeklyBudgetId": "test", "totalAmount": 1000, ...}'
```

## 📝 最佳实践

### 1. 配置管理

- **环境隔离**：开发、测试、生产使用不同的流程代码
- **版本控制**：配置变更要有版本记录
- **备份恢复**：重要配置要有备份机制

### 2. 安全考虑

- **敏感信息**：流程代码不包含敏感信息，可以明文存储
- **访问控制**：只有授权人员可以修改审批配置
- **审计日志**：记录配置变更的操作日志

### 3. 监控告警

- **配置验证**：启动时验证配置的有效性
- **错误监控**：监控审批流程的错误率
- **性能监控**：监控审批接口的响应时间

## 🚨 常见问题

### 1. 流程代码无效

**问题**：审批发起失败，提示流程代码无效

**解决**：
1. 检查钉钉后台审批模板是否存在
2. 确认流程代码拼写正确
3. 验证应用权限是否包含审批管理

### 2. 字段映射错误

**问题**：审批表单字段显示异常

**解决**：
1. 检查钉钉模板字段名称
2. 更新 `FORM_FIELDS` 配置
3. 重启应用使配置生效

### 3. 状态同步异常

**问题**：审批状态更新不及时

**解决**：
1. 检查钉钉回调配置
2. 验证状态映射规则
3. 查看错误日志定位问题

## 📞 技术支持

如果遇到配置问题，请：

1. 查看应用日志：`docker logs cantv-ding-backend`
2. 检查配置文件：确认环境变量设置正确
3. 联系技术支持：提供详细的错误信息和配置内容

---

通过以上配置，您可以灵活地管理钉钉审批流程，实现不同环境下的审批流程隔离和统一管理。
