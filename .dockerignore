# 优化Docker构建速度 - 排除不必要的文件

# Node.js 依赖和构建产物
node_modules
dist
build
.next
out

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
logs

# 开发工具和编辑器
.vscode
.idea
*.swp
*.swo
*~

# Git 相关
.git
.gitignore
.gitattributes

# 测试相关
coverage
.nyc_output
test-results
playwright-report
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts
test/
tests/
__tests__/

# 缓存目录
.cache
.parcel-cache
.eslintcache
.tmp
tmp/
temp/

# 环境变量文件（除了生产环境）
.env.local
.env.development
.env.test
.env*.local

# Docker 相关
Dockerfile*
.dockerignore
docker-compose*.yml

# 文档
README.md
CHANGELOG.md
LICENSE
*.md
docs/

# 开发配置文件
.eslintrc*
.prettierrc*
jest.config.*
vitest.config.*
playwright.config.*

# TypeScript 构建信息
*.tsbuildinfo

# 操作系统文件
.DS_Store
Thumbs.db

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 上传文件
uploads/
public/uploads/

# 备份和压缩文件
*.bak
*.backup
*.zip
*.tar.gz
*.rar

# 保留必要的文件
!.env.prod
!start.sh
