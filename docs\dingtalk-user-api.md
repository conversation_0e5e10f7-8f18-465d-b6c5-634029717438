# 钉钉用户管理API文档

## 概述

本文档描述了项目管理系统中集成的钉钉用户管理API，用于获取钉钉组织架构中的部门和用户信息，支持项目中的人员选择功能。

## 基础信息

- **Base URL**: `http://localhost:3000/api`
- **认证方式**: 钉钉企业内部应用认证
- **数据格式**: JSON

## API接口

### 1. 获取部门列表

获取钉钉组织架构中的部门列表。

**请求**
```http
GET /departments
```

**响应示例**
```json
{
  "success": true,
  "data": [
    {
      "dept_id": 1,
      "name": "总公司",
      "parent_id": 0,
      "order": 1
    },
    {
      "dept_id": 2,
      "name": "技术部",
      "parent_id": 1,
      "order": 1
    },
    {
      "dept_id": 3,
      "name": "市场部",
      "parent_id": 1,
      "order": 2
    }
  ],
  "message": "获取部门列表成功"
}
```

### 2. 获取部门用户列表

获取指定部门下的用户列表。

**请求**
```http
GET /departments/{deptId}/users?cursor={cursor}&size={size}
```

**路径参数**
- `deptId` (string, required): 部门ID

**查询参数**
- `cursor` (string, optional): 分页游标，默认为0
- `size` (string, optional): 每页数量，默认为100

**响应示例**
```json
{
  "success": true,
  "data": {
    "list": [
      {
        "userid": "6157664557692733",
        "name": "周奥",
        "avatar": "",
        "mobile": "18068581226",
        "email": "<EMAIL>",
        "department": [1, 2],
        "position": "高级工程师",
        "job_number": "E001"
      }
    ],
    "has_more": false,
    "next_cursor": 0
  },
  "message": "获取部门用户列表成功"
}
```

### 3. 搜索用户

根据关键字搜索用户。

**请求**
```http
GET /users/search?keyword={keyword}&deptId={deptId}
```

**查询参数**
- `keyword` (string, required): 搜索关键字（姓名、手机号、工号等）
- `deptId` (string, optional): 限制搜索范围的部门ID

**响应示例**
```json
{
  "success": true,
  "data": [
    {
      "userid": "6157664557692733",
      "name": "周奥",
      "avatar": "",
      "mobile": "18068581226",
      "email": "<EMAIL>",
      "department": [1, 2],
      "position": "高级工程师",
      "job_number": "E001"
    }
  ],
  "message": "搜索用户成功"
}
```

### 4. 获取用户详情

获取指定用户的详细信息。

**请求**
```http
GET /users/{userid}
```

**路径参数**
- `userid` (string, required): 用户ID

**响应示例**
```json
{
  "success": true,
  "data": {
    "userid": "6157664557692733",
    "name": "周奥",
    "avatar": "",
    "mobile": "18068581226",
    "email": "<EMAIL>",
    "department": [1, 2],
    "position": "高级工程师",
    "job_number": "E001"
  },
  "message": "获取用户详情成功"
}
```

## 错误响应

所有API在出错时都会返回统一的错误格式：

```json
{
  "success": false,
  "message": "错误描述",
  "errors": [
    {
      "code": "error_code",
      "message": "详细错误信息",
      "path": ["field_name"]
    }
  ]
}
```

**常见错误码**
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 使用示例

### JavaScript前端集成

```javascript
// 获取部门列表
async function getDepartments() {
  try {
    const response = await fetch('/api/departments');
    const data = await response.json();
    
    if (data.success) {
      console.log('部门列表:', data.data);
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('获取部门列表失败:', error);
  }
}

// 获取部门用户
async function getDepartmentUsers(deptId) {
  try {
    const response = await fetch(`/api/departments/${deptId}/users`);
    const data = await response.json();
    
    if (data.success) {
      console.log('用户列表:', data.data.list);
      return data.data.list;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
}

// 搜索用户
async function searchUsers(keyword) {
  try {
    const response = await fetch(`/api/users/search?keyword=${encodeURIComponent(keyword)}`);
    const data = await response.json();
    
    if (data.success) {
      console.log('搜索结果:', data.data);
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('搜索用户失败:', error);
  }
}

// 获取用户详情
async function getUserDetail(userid) {
  try {
    const response = await fetch(`/api/users/${userid}`);
    const data = await response.json();
    
    if (data.success) {
      console.log('用户详情:', data.data);
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('获取用户详情失败:', error);
  }
}
```

### 用户选择器组件

项目中提供了一个完整的用户选择器组件 `UserSelector`，支持：

- 单选和多选模式
- 实时搜索
- 用户头像显示
- 已选用户管理
- 表单验证

使用方法：

```javascript
// 单选模式（执行PM）
const pmSelector = new UserSelector(
  'executorPMInput',     // 输入框ID
  'executorPMDropdown',  // 下拉框ID
  'executorPMSelected',  // 已选用户容器ID
  false                  // 单选模式
);

// 多选模式（内容媒介）
const mediaSelector = new UserSelector(
  'contentMediaInput',
  'contentMediaDropdown',
  'contentMediaSelected',
  true                   // 多选模式
);

// 获取选中的用户
const selectedPM = pmSelector.getSelectedUsers();
const selectedMedia = mediaSelector.getSelectedUsers();
```

## 注意事项

1. **权限要求**: 需要配置钉钉企业内部应用的相关权限
2. **频率限制**: 钉钉API有调用频率限制，建议合理缓存数据
3. **数据同步**: 用户信息可能会发生变化，建议定期更新缓存
4. **错误处理**: 需要妥善处理网络错误和API错误
5. **用户体验**: 建议添加加载状态和错误提示

## 相关链接

- [钉钉开放平台文档](https://open.dingtalk.com/)
- [项目管理API文档](./project-api.md)
- [前端组件文档](./frontend-components.md)
