<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DingTalk Design CLI 集成示例</title>
    <script src="https://g.alicdn.com/dingding/dingtalk-jsapi/3.0.25/dingtalk.open.js"></script>
    <script src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .nav-menu {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-link {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.9);
            color: #1890ff;
            font-weight: bold;
        }

        .content {
            padding: 30px;
        }

        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .api-card {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .api-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .api-card h3 {
            color: #1890ff;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .api-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .btn {
            background: linear-gradient(135deg, #1890ff, #36cfc9);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status.loading {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }

        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }

        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }

        .result-panel {
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .result-panel h4 {
            color: #262626;
            margin-bottom: 15px;
        }

        .code-block {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: #262626;
            font-weight: 500;
        }

        .input-group input,
        .input-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .input-group textarea {
            resize: vertical;
            min-height: 80px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DingTalk Design CLI</h1>
            <p>钉钉前端框架配套API接口演示</p>

            <!-- 导航菜单 -->
            <div class="nav-menu">
                <a href="index.html" class="nav-link">🏠 首页</a>
                <a href="dingtalk-design-demo.html" class="nav-link active">🎨 完整功能</a>
                <a href="dingtalk-login-demo.html" class="nav-link">🔐 免登录演示</a>
                <a href="test-api.html" class="nav-link">🧪 API测试</a>
                <a href="signature-test.html" class="nav-link">🔑 签名测试</a>
                <a href="official-login-demo.html" class="nav-link">📱 官方演示</a>
            </div>
        </div>

        <div class="content">
            <div id="status" class="status loading">
                正在初始化钉钉环境...
            </div>

            <div class="api-grid">
                <!-- 应用配置 -->
                <div class="api-card">
                    <h3>📱 应用配置</h3>
                    <p>获取前端初始化所需的应用配置信息，包括AppKey、CorpId、JSAPI列表等。</p>
                    <button class="btn" onclick="getAppConfig()">获取应用配置</button>
                </div>

                <!-- 用户认证 -->
                <div class="api-card">
                    <h3>🔐 用户认证</h3>
                    <p>通过钉钉免登码获取当前用户的详细信息。</p>
                    <button class="btn" onclick="authenticateUser()">用户认证</button>
                </div>

                <!-- 部门用户 -->
                <div class="api-card">
                    <h3>👥 部门用户</h3>
                    <p>获取指定部门的用户列表，支持分页查询。</p>
                    <div class="input-group">
                        <label>部门ID:</label>
                        <input type="number" id="deptId" value="1" min="1">
                    </div>
                    <button class="btn" onclick="getDepartmentUsers()">获取部门用户</button>
                </div>

                <!-- 工作通知 -->
                <div class="api-card">
                    <h3>📢 工作通知</h3>
                    <p>向指定用户发送工作通知消息。</p>
                    <div class="input-group">
                        <label>用户ID (逗号分隔):</label>
                        <input type="text" id="userIds" placeholder="user1,user2">
                    </div>
                    <div class="input-group">
                        <label>通知内容:</label>
                        <textarea id="notificationContent" placeholder="请输入通知内容"></textarea>
                    </div>
                    <button class="btn" onclick="sendNotification()">发送通知</button>
                </div>

                <!-- JSAPI签名 -->
                <div class="api-card">
                    <h3>🔑 JSAPI签名</h3>
                    <p>获取增强版JSAPI签名，使用真实的jsapi_ticket。</p>
                    <button class="btn" onclick="getEnhancedSignature()">获取增强签名</button>
                </div>

                <!-- 用户权限 -->
                <div class="api-card">
                    <h3>🛡️ 用户权限</h3>
                    <p>获取当前用户的权限和角色信息。</p>
                    <button class="btn" onclick="getUserPermissions()">获取权限</button>
                </div>

                <!-- 应用统计 -->
                <div class="api-card">
                    <h3>📊 应用统计</h3>
                    <p>获取应用的统计信息，如用户数、部门数等。</p>
                    <button class="btn" onclick="getAppStats()">获取统计</button>
                </div>

                <!-- 部门列表 -->
                <div class="api-card">
                    <h3>🏢 部门列表</h3>
                    <p>获取企业的部门组织架构信息。</p>
                    <button class="btn" onclick="getDepartments()">获取部门列表</button>
                </div>
            </div>

            <div id="resultPanel" class="result-panel">
                <h4>📋 API响应结果</h4>
                <div id="resultContent" class="code-block"></div>
            </div>
        </div>
    </div>

    <script>
        initDingH5RemoteDebug();
        let isDingTalkEnv = false;
        let isReady = false;
        let appConfig = null;
        let currentUser = null;

        // 检查钉钉环境
        function checkDingTalkEnvironment() {
            if (typeof dd !== 'undefined') {
                isDingTalkEnv = true;
                initDingTalk();
            } else {
                setTimeout(() => {
                    if (typeof dd !== 'undefined') {
                        isDingTalkEnv = true;
                        initDingTalk();
                    } else {
                        updateStatus('error', '❌ 未检测到钉钉环境，部分功能将使用模拟数据');
                        isReady = true;
                    }
                }, 2000);
            }
        }

        // 初始化钉钉
        async function initDingTalk() {
            try {
                updateStatus('loading', '正在获取应用配置...');

                // 1. 获取应用配置
                appConfig = await getAppConfigData();

                // 2. 获取JSAPI签名
                const signature = await getJSAPISignatureData();
                console.log('JSAPI签名:', signature);

                // 3. 配置钉钉JSAPI
                dd.config({
                    agentId: appConfig.agentId,
                    corpId: appConfig.corpId,
                    timeStamp: signature.timeStamp,
                    nonceStr: signature.nonceStr,
                    signature: signature.signature,
                    jsApiList: appConfig.jsApiList || [
                        'runtime.permission.requestAuthCode',
                        'biz.contact.choose',
                        'device.notification.confirm',
                        'device.notification.alert',
                        'device.notification.prompt',
                        'biz.ding.post'
                    ]
                });

                dd.ready(() => {
                    isReady = true;
                    updateStatus('success', '✅ 钉钉环境初始化成功！');
                    // 自动尝试免登录
                    autoLogin();
                });

                dd.error((err) => {
                    console.error('钉钉JSAPI错误:', err);
                    updateStatus('error', `❌ 钉钉环境初始化失败: ${err.message || JSON.stringify(err)}`);
                });

            } catch (error) {
                console.error('初始化钉钉失败:', error);
                updateStatus('error', `❌ 初始化失败: ${error.message}`);
            }
        }

        // 自动免登录
        async function autoLogin() {
            if (!isDingTalkEnv || !isReady) {
                return;
            }

            try {
                updateStatus('loading', '正在进行免登录验证...');
                await authenticateUser();
            } catch (error) {
                console.error('自动登录失败:', error);
                updateStatus('error', '自动登录失败，请手动点击用户认证按钮');
            }
        }

        // 更新状态
        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
        }

        // 显示结果
        function showResult(data) {
            const panel = document.getElementById('resultPanel');
            const content = document.getElementById('resultContent');
            content.textContent = JSON.stringify(data, null, 2);
            panel.style.display = 'block';
            panel.scrollIntoView({ behavior: 'smooth' });
        }

        // API调用函数
        async function apiCall(url, options = {}) {
            try {
                updateStatus('loading', '正在请求API...');
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();

                if (data.success) {
                    updateStatus('success', '✅ API调用成功');
                } else {
                    updateStatus('error', `❌ API调用失败: ${data.message}`);
                }

                showResult(data);
                return data;
            } catch (error) {
                updateStatus('error', `❌ 请求失败: ${error.message}`);
                showResult({ error: error.message });
            }
        }

        // 获取应用配置数据（内部使用）
        async function getAppConfigData() {
            try {
                const response = await fetch('/api/app/config', {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();

                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || '获取应用配置失败');
                }
            } catch (error) {
                console.error('获取应用配置失败:', error);
                throw error;
            }
        }

        // 清理URL，移除钉钉调试参数
        function cleanUrl(url) {
            try {
                const urlObj = new URL(url);

                // 移除钉钉调试相关的参数
                const debugParams = [
                    'dd_debug_h5',
                    'dd_debug_v1',
                    'dd_debug_os',
                    'dd_debug_v2',
                    'dd_debug_unifiedAppId',
                    'dd_debug_token',
                    'dd_debug_uuid',
                    'dd_debug_pid'
                ];

                debugParams.forEach(param => {
                    urlObj.searchParams.delete(param);
                });

                return urlObj.toString();
            } catch (error) {
                console.warn('URL清理失败:', error);
                return url;
            }
        }

        // 获取JSAPI签名数据（内部使用）
        async function getJSAPISignatureData() {
            try {
                // 清理当前URL，移除调试参数
                const cleanedUrl = cleanUrl(window.location.href);
                console.log('原始URL:', window.location.href);
                console.log('清理后URL:', cleanedUrl);

                const currentUrl = encodeURIComponent(cleanedUrl);
                const response = await fetch(`/api/auth/jsapi-signature?url=${currentUrl}`, {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();

                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || '获取JSAPI签名失败');
                }
            } catch (error) {
                console.error('获取JSAPI签名失败:', error);
                throw error;
            }
        }

        // 具体API调用函数
        async function getAppConfig() {
            await apiCall('/api/app/config');
        }

        async function authenticateUser() {
            if (!isDingTalkEnv) {
                const mockData = {
                    success: false,
                    message: '需要在钉钉环境中使用',
                    mockData: {
                        userid: 'mock_user',
                        name: '模拟用户',
                        mobile: '138****8888'
                    }
                };
                showResult(mockData);
                updateStatus('error', '❌ 需要在钉钉环境中使用');
                return;
            }

            if (!isReady) {
                updateStatus('error', '❌ 钉钉环境尚未准备就绪，请稍后再试');
                return;
            }

            try {
                updateStatus('loading', '正在获取免登授权码...');

                dd.runtime.permission.requestAuthCode({
                    corpId: appConfig.corpId,
                    redirection: "none",
                    onSuccess: async (result) => {
                        try {
                            console.log('获取到免登码:', result);
                            updateStatus('loading', '正在验证用户身份...');

                            const userInfo = await apiCall('/api/auth/user-info', {
                                method: 'POST',
                                body: JSON.stringify({ authCode: result.code })
                            });

                            if (userInfo && userInfo.success) {
                                currentUser = userInfo.data;
                                updateStatus('success', `✅ 登录成功！欢迎 ${currentUser.name || currentUser.userid}`);

                                // 显示用户信息
                                showUserInfo(currentUser);
                            }
                        } catch (error) {
                            console.error('用户认证失败:', error);
                            updateStatus('error', `❌ 用户认证失败: ${error.message}`);
                        }
                    },
                    onFail: (err) => {
                        console.error('获取免登码失败:', err);
                        updateStatus('error', `❌ 获取免登码失败: ${err.message || JSON.stringify(err)}`);
                        showResult({
                            success: false,
                            message: '获取免登码失败',
                            error: err
                        });
                    }
                });
            } catch (error) {
                console.error('免登录过程出错:', error);
                updateStatus('error', `❌ 免登录过程出错: ${error.message}`);
            }
        }

        // 显示用户信息
        function showUserInfo(userInfo) {
            const userInfoHtml = `
                <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px; padding: 15px; margin-top: 10px;">
                    <h4 style="color: #0369a1; margin: 0 0 10px 0;">👤 当前用户信息</h4>
                    <p><strong>用户ID:</strong> ${userInfo.userid || 'N/A'}</p>
                    <p><strong>姓名:</strong> ${userInfo.name || 'N/A'}</p>
                    <p><strong>手机号:</strong> ${userInfo.mobile || 'N/A'}</p>
                    <p><strong>邮箱:</strong> ${userInfo.email || 'N/A'}</p>
                    <p><strong>职位:</strong> ${userInfo.position || 'N/A'}</p>
                    <p><strong>工号:</strong> ${userInfo.job_number || 'N/A'}</p>
                </div>
            `;

            // 在状态区域下方显示用户信息
            const statusEl = document.getElementById('status');
            let userInfoEl = document.getElementById('userInfo');
            if (!userInfoEl) {
                userInfoEl = document.createElement('div');
                userInfoEl.id = 'userInfo';
                statusEl.parentNode.insertBefore(userInfoEl, statusEl.nextSibling);
            }
            userInfoEl.innerHTML = userInfoHtml;
        }

        async function getDepartmentUsers() {
            const deptId = document.getElementById('deptId').value;
            await apiCall(`/api/app/department/users?deptId=${deptId}&cursor=0&size=10`);
        }

        async function sendNotification() {
            const userIds = document.getElementById('userIds').value.split(',').map(id => id.trim()).filter(id => id);
            const content = document.getElementById('notificationContent').value;

            if (!userIds.length || !content) {
                updateStatus('error', '请填写用户ID和通知内容');
                return;
            }

            await apiCall('/api/app/notification/send', {
                method: 'POST',
                body: JSON.stringify({
                    userIds,
                    title: '工作通知',
                    content,
                    messageType: 'text'
                })
            });
        }

        async function getEnhancedSignature() {
            const currentUrl = encodeURIComponent(window.location.href);
            await apiCall(`/api/app/jsapi-signature/enhanced?url=${currentUrl}`);
        }

        async function getUserPermissions() {
            await apiCall('/api/app/user/permissions');
        }

        async function getAppStats() {
            await apiCall('/api/app/stats');
        }

        async function getDepartments() {
            await apiCall('/api/auth/departments');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            checkDingTalkEnvironment();
        });
    </script>
</body>

</html>