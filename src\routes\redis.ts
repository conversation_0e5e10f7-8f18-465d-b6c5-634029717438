import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { jwtAuthMiddleware } from '../middleware/auth.js';
import { jwtService } from '../services/jwt.js';
import { redisService } from '../services/redis.js';

export async function redisRoutes(fastify: FastifyInstance) {
  
  // Redis 健康检查
  fastify.get('/redis/health', {
    schema: {
      description: 'Redis 健康检查',
      tags: ['Redis'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                connected: { type: 'boolean' },
                ping: { type: 'string' },
                info: { type: 'string' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const isConnected = redisService.isReady();
      let ping = 'N/A';
      let info = 'N/A';

      if (isConnected) {
        try {
          ping = await redisService.ping();
          info = await redisService.info();
        } catch (error) {
          console.error('Redis 操作失败:', error);
        }
      }

      return reply.send({
        success: true,
        data: {
          connected: isConnected,
          ping,
          info: info.split('\n').slice(0, 10).join('\n') // 只显示前10行信息
        },
        message: isConnected ? 'Redis 连接正常' : 'Redis 未连接'
      });
    } catch (error) {
      return reply.status(500).send({
        success: false,
        message: 'Redis 健康检查失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // 获取 Redis 统计信息（需要认证）
  fastify.get('/redis/stats', {
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取 Redis 统计信息',
      tags: ['Redis'],
      headers: {
        type: 'object',
        properties: {
          authorization: {
            type: 'string',
            description: 'Bearer token'
          }
        },
        required: ['authorization']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                connected: { type: 'boolean' },
                sessionKeys: { type: 'number' },
                tokenKeys: { type: 'number' },
                blacklistKeys: { type: 'number' },
                totalKeys: { type: 'number' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const isConnected = redisService.isReady();
      
      if (!isConnected) {
        return reply.send({
          success: true,
          data: {
            connected: false,
            sessionKeys: 0,
            tokenKeys: 0,
            blacklistKeys: 0,
            totalKeys: 0
          },
          message: 'Redis 未连接'
        });
      }

      // 获取各类键的数量
      const [sessionKeys, refreshTokenKeys, blacklistKeys] = await Promise.all([
        redisService.keys('session:user:*'),
        redisService.keys('jwt:refresh:*'),
        redisService.keys('jwt:blacklist:*')
      ]);

      const totalKeys = sessionKeys.length + refreshTokenKeys.length + blacklistKeys.length;

      return reply.send({
        success: true,
        data: {
          connected: true,
          sessionKeys: sessionKeys.length,
          tokenKeys: refreshTokenKeys.length,
          blacklistKeys: blacklistKeys.length,
          totalKeys
        },
        message: 'Redis 统计信息获取成功'
      });
    } catch (error) {
      return reply.status(500).send({
        success: false,
        message: 'Redis 统计信息获取失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // 清理过期会话（管理员功能）
  fastify.post('/redis/cleanup', {
    
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '清理过期的 Redis 数据',
      tags: ['Redis'],
      headers: {
        type: 'object',
        properties: {
          authorization: {
            type: 'string',
            description: 'Bearer token'
          }
        },
        required: ['authorization']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                deletedKeys: { type: 'number' },
                details: {
                  type: 'object',
                  properties: {
                    expiredSessions: { type: 'number' },
                    expiredTokens: { type: 'number' }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // 检查用户权限（这里应该检查是否为管理员）
      const user = (request as any).user;
      if (!user.isAdmin) {
        return reply.status(403).send({
          success: false,
          message: '权限不足，仅管理员可执行此操作'
        });
      }

      if (!redisService.isReady()) {
        return reply.send({
          success: true,
          data: {
            deletedKeys: 0,
            details: {
              expiredSessions: 0,
              expiredTokens: 0
            }
          },
          message: 'Redis 未连接，无需清理'
        });
      }

      // 清理内存中的过期会话
      jwtService.cleanExpiredSessions();

      // Redis 会自动处理过期键，这里主要是统计信息
      const [sessionKeys, tokenKeys] = await Promise.all([
        redisService.keys('session:user:*'),
        redisService.keys('jwt:refresh:*')
      ]);

      return reply.send({
        success: true,
        data: {
          deletedKeys: 0, // Redis 自动清理过期键
          details: {
            expiredSessions: 0,
            expiredTokens: 0
          }
        },
        message: `清理完成，当前活跃会话: ${sessionKeys.length}, 刷新令牌: ${tokenKeys.length}`
      });
    } catch (error) {
      return reply.status(500).send({
        success: false,
        message: 'Redis 清理失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // 获取会话详情（管理员功能）
  fastify.get('/redis/sessions', {
    
    preHandler: [jwtAuthMiddleware],
    schema: {
      description: '获取所有活跃会话信息',
      tags: ['Redis'],
      headers: {
        type: 'object',
        properties: {
          authorization: {
            type: 'string',
            description: 'Bearer token'
          }
        },
        required: ['authorization']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                sessions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      userid: { type: 'string' },
                      name: { type: 'string' },
                      loginTime: { type: 'number' },
                      lastActiveTime: { type: 'number' },
                      isAdmin: { type: 'boolean' },
                      isBoss: { type: 'boolean' }
                    }
                  }
                },
                stats: {
                  type: 'object',
                  properties: {
                    totalSessions: { type: 'number' },
                    adminSessions: { type: 'number' },
                    bossSessions: { type: 'number' },
                    recentSessions: { type: 'number' },
                    redisEnabled: { type: 'boolean' }
                  }
                }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      // 检查用户权限
      const user = (request as any).user;
      if (!user.isAdmin) {
        return reply.status(403).send({
          success: false,
          message: '权限不足，仅管理员可查看会话信息'
        });
      }

      // 获取会话统计
      const stats = await jwtService.getActiveSessionsStats();
      
      // 获取详细会话信息
      let sessions: any[] = [];
      
      if (redisService.isReady()) {
        try {
          const sessionKeys = await redisService.keys('session:user:*');
          const sessionPromises = sessionKeys.map(async (key) => {
            const sessionData = await redisService.get(key);
            return sessionData ? JSON.parse(sessionData) : null;
          });
          
          const sessionResults = await Promise.all(sessionPromises);
          sessions = sessionResults.filter(session => session !== null);
        } catch (error) {
          console.error('获取 Redis 会话失败:', error);
        }
      }

      return reply.send({
        success: true,
        data: {
          sessions,
          stats
        },
        message: '会话信息获取成功'
      });
    } catch (error) {
      return reply.status(500).send({
        success: false,
        message: '获取会话信息失败',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });
}
