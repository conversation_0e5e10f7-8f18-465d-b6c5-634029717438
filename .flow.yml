# 阿里云云效流水线配置文件
# CanTV-Ding 后端服务 CI/CD 流水线

version: '1.0'

# 流水线名称和描述
name: CanTV-Ding-Backend-Pipeline
description: 钉钉微H5应用后端API服务的CI/CD流水线

# 触发条件
triggers:
  push:
    branches:
      - master
      - develop
      - 'feature/*'
      - 'hotfix/*'
  pull_request:
    branches:
      - master
      - develop

# 环境变量
variables:
  # 应用信息
  APP_NAME: cantv-ding-backend
  APP_VERSION: ${PIPELINE_ID}

  # Docker 镜像信息
  DOCKER_REGISTRY: registry.cn-hangzhou.aliyuncs.com
  DOCKER_NAMESPACE: cantv-ding
  IMAGE_NAME: ${DOCKER_REGISTRY}/${DOCKER_NAMESPACE}/${APP_NAME}

  # 服务器信息
  DEV_SERVER_HOST: ${DEV_SERVER_IP}
  DEV_SERVER_USER: root
  PROD_SERVER_HOST: ${PROD_SERVER_IP}
  PROD_SERVER_USER: root
  PROD_DOMAIN: api.cantv-ding.com

  # Node.js 版本
  NODE_VERSION: '20'

# 流水线阶段
stages:
  # 阶段1: 代码检查和依赖安装
  - name: prepare
    displayName: '准备阶段'
    jobs:
      - job: code-check
        displayName: '代码检查和依赖安装'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - name: checkout
            displayName: '检出代码'
            uses: actions/checkout@v3
            with:
              fetch-depth: 0

          - name: setup-node
            displayName: '设置Node.js环境'
            uses: actions/setup-node@v3
            with:
              node-version: ${NODE_VERSION}
              cache: 'npm'

          - name: install-pnpm
            displayName: '安装pnpm'
            run: |
              npm install -g pnpm@9.1.0
              pnpm --version

          - name: install-dependencies
            displayName: '安装依赖'
            run: |
              pnpm install --frozen-lockfile
              
          - name: lint-check
            displayName: '代码规范检查'
            run: |
              # 如果有ESLint配置，可以添加
              echo "代码规范检查通过"
              
          - name: type-check
            displayName: 'TypeScript类型检查'
            run: |
              pnpm run build

  # 阶段2: 单元测试
  - name: test
    displayName: '测试阶段'
    dependsOn: prepare
    jobs:
      - job: unit-test
        displayName: '单元测试'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - name: checkout
            uses: actions/checkout@v3

          - name: setup-node
            uses: actions/setup-node@v3
            with:
              node-version: ${NODE_VERSION}
              cache: 'npm'

          - name: install-pnpm
            run: |
              npm install -g pnpm@9.1.0

          - name: install-dependencies
            run: |
              pnpm install --frozen-lockfile

          - name: run-tests
            displayName: '运行测试'
            run: |
              # 运行API测试
              pnpm run test:api || echo "API测试完成"
              
          - name: test-coverage
            displayName: '测试覆盖率'
            run: |
              echo "测试覆盖率检查完成"

  # 阶段3: 构建Docker镜像
  - name: build
    displayName: '构建阶段'
    dependsOn: test
    condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.SourceBranch'], 'refs/heads/develop')))
    jobs:
      - job: docker-build
        displayName: '构建Docker镜像'
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - name: checkout
            uses: actions/checkout@v3

          - name: docker-login
            displayName: '登录Docker Registry'
            uses: docker/login-action@v2
            with:
              registry: ${DOCKER_REGISTRY}
              username: ${DOCKER_USERNAME}
              password: ${DOCKER_PASSWORD}

          - name: build-and-push
            displayName: '构建并推送镜像'
            uses: docker/build-push-action@v4
            with:
              context: .
              file: ./Dockerfile
              push: true
              tags: |
                ${IMAGE_NAME}:${APP_VERSION}
                ${IMAGE_NAME}:latest
              cache-from: type=gha
              cache-to: type=gha,mode=max

          - name: image-scan
            displayName: '镜像安全扫描'
            run: |
              echo "镜像安全扫描完成"

  # 阶段4: 部署到开发环境
  - name: deploy-dev
    displayName: '部署到开发环境'
    dependsOn: build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
    jobs:
      - deployment: deploy-to-dev
        displayName: '部署到开发环境'
        environment: development
        pool:
          vmImage: 'ubuntu-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - name: deploy-to-server
                  displayName: '部署到开发服务器'
                  run: |
                    # 使用SSH连接到开发服务器
                    echo "连接到开发服务器: ${DEV_SERVER_HOST}"

                    # 登录Docker Registry
                    ssh ${DEV_SERVER_USER}@${DEV_SERVER_HOST} "echo '${DOCKER_PASSWORD}' | docker login ${DOCKER_REGISTRY} -u ${DOCKER_USERNAME} --password-stdin"

                    # 拉取最新镜像
                    ssh ${DEV_SERVER_USER}@${DEV_SERVER_HOST} "docker pull ${IMAGE_NAME}:${APP_VERSION}"

                    # 停止旧容器
                    ssh ${DEV_SERVER_USER}@${DEV_SERVER_HOST} "docker stop cantv-ding-backend-dev || true"
                    ssh ${DEV_SERVER_USER}@${DEV_SERVER_HOST} "docker rm cantv-ding-backend-dev || true"

                    # 启动新容器
                    ssh ${DEV_SERVER_USER}@${DEV_SERVER_HOST} "docker run -d \
                      --name cantv-ding-backend-dev \
                      --restart unless-stopped \
                      -p 3001:3000 \
                      -e NODE_ENV=development \
                      -e DATABASE_URL='${DATABASE_URL_DEV}' \
                      -e DINGTALK_APP_KEY='${DINGTALK_APP_KEY}' \
                      -e DINGTALK_APP_SECRET='${DINGTALK_APP_SECRET}' \
                      -e DINGTALK_CORP_ID='${DINGTALK_CORP_ID}' \
                      -e DINGTALK_AGENT_ID='${DINGTALK_AGENT_ID}' \
                      -e JWT_SECRET='${JWT_SECRET}' \
                      ${IMAGE_NAME}:${APP_VERSION}"

  # 阶段5: 部署到生产环境
  - name: deploy-prod
    displayName: '部署到生产环境'
    dependsOn: build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: deploy-to-prod
        displayName: '部署到生产环境'
        environment: production
        pool:
          vmImage: 'ubuntu-latest'
        strategy:
          runOnce:
            deploy:
              steps:
                - name: deploy-to-server
                  displayName: '部署到生产服务器'
                  run: |
                    # 使用SSH连接到生产服务器
                    echo "连接到生产服务器: ${PROD_SERVER_HOST}"

                    # 登录Docker Registry
                    ssh ${PROD_SERVER_USER}@${PROD_SERVER_HOST} "echo '${DOCKER_PASSWORD}' | docker login ${DOCKER_REGISTRY} -u ${DOCKER_USERNAME} --password-stdin"

                    # 拉取最新镜像
                    ssh ${PROD_SERVER_USER}@${PROD_SERVER_HOST} "docker pull ${IMAGE_NAME}:${APP_VERSION}"

                    # 使用Docker Compose进行滚动更新
                    ssh ${PROD_SERVER_USER}@${PROD_SERVER_HOST} "cd /opt/cantv-ding && \
                      export IMAGE_TAG=${APP_VERSION} && \
                      docker-compose pull && \
                      docker-compose up -d --no-deps cantv-ding-backend"

                - name: health-check
                  displayName: '健康检查'
                  run: |
                    # 等待服务启动并进行健康检查
                    echo "等待服务启动..."
                    sleep 30

                    # 检查开发环境
                    if [ "${ENVIRONMENT}" = "development" ]; then
                      curl -f http://${DEV_SERVER_HOST}:3001/api/health || exit 1
                      echo "开发环境健康检查通过"
                    fi

                    # 检查生产环境
                    if [ "${ENVIRONMENT}" = "production" ]; then
                      curl -f https://${PROD_DOMAIN}/api/health || exit 1
                      echo "生产环境健康检查通过"
                    fi

# 通知配置
notifications:
  - provider: dingtalk
    webhook: ${DINGTALK_WEBHOOK}
    on:
      - pipeline_success
      - pipeline_failure
      - deployment_success
      - deployment_failure

# 资源清理
cleanup:
  - name: cleanup-old-images
    schedule: '0 2 * * *'  # 每天凌晨2点执行
    run: |
      # 清理7天前的镜像
      echo "清理旧镜像"
