/**
 * 财务导出报表服务
 * 生成多Sheet Excel报表，包含项目汇总、品牌详情、品牌汇总等
 */

import ExcelJS from 'exceljs';
import {
  Brand,
  ContractSigningStatus,
  ContractType,
  Project,
  ProjectRevenue,
  ProjectStatus,
  Supplier,
  WeeklyBudget
} from '../types/project.js';
import { DatabaseService } from './database.js';
import { ProjectService } from './project.js';

export interface FinancialExportParams {
  year: number;                    // 导出年份
  brandIds?: string[];             // 指定品牌ID列表（可选）
  includeCompleted?: boolean;      // 是否包含已完成项目
  includeCancelled?: boolean;      // 是否包含已取消项目
}

export interface ProjectSummaryRow {
  orderDate: string;               // 下单时间
  brandName: string;               // 品牌
  category: string;                // 品类
  executiveProject: string;        // 执行项目
  projectName: string;             // 项目名称
  contractSigningStatus: string;   // 合同签署情况
  contractType: string;            // 合同类型
  planningAmount: number;          // 项目规划金额
  taxExcludedAmount: number;       // 不含税金额
  projectProgress: string;         // 项目进度
  expectedPaymentTime: string;     // 预计回款时间
  paymentCycle: string;            // 回款周期
  executionCycle: string;          // 执行项目周期
  receivedAmount: number;          // 已回款额
  unreceivedAmount: number;        // 未回款额
  paymentStatus: string;           // 回款状态
  purchaseCost: number;            // 采购成本（去税）
  rebate: number;                  // 返点
  intermediary: number;            // 居间
  grossProfit: number;             // 毛利
  grossProfitRate: number;         // 毛利率
  remarks: string;                 // 备注
  supplierName: string;            // 供应商公司名称
  serviceContent: string;          // 服务内容
  supplierCost: number;            // 供应商采购成本
  invoiceTaxRate: string;          // 专票税率
  supplierTaxExcluded: number;     // 不含税金额
  paidAmount: number;              // 已付金额
  unpaidAmount: number;            // 未付款金额
  junePayable: number;             // 6月应付
  remainingCost: number;           // 剩余成本
  julyExpectedPayment: number;     // 7月预计付款
  transferToNextMonth: number;     // 转后续月份
}

export interface BrandProjectRow {
  brandName: string;               // 品牌
  category: string;                // 品类
  orderDate: string;               // 下单时间
  executiveProject: string;        // 执行项目
  projectName: string;             // 项目名称
  contractSigningStatus: string;   // 合同签署情况
  planningAmount: number;          // 规划金额
  executionStatus: string;         // 执行情况
  expectedPaymentDate: string;     // 预计回款日期
  paymentStatus: string;           // 回款情况
  receivedAmount: number;          // 已回款额
  unreceivedAmount: number;        // 未回款额
  paymentTerms: string;            // 账期
  grossProfitAmount: number;       // 毛利额
  grossProfitRate: number;         // 毛利率
  paidProjectAmount: number;       // 已支付项目金额
  unpaidProjectAmount: number;     // 未支付项目金额
}

export interface BrandSummaryRow {
  brandName: string;               // 品牌
  category: string;                // 品类
  brandOrderAmount: number;        // 品牌下单金额
  executedAmount: number;          // 已执行完金额
  executingAmount: number;         // 执行中项目金额
  estimatedProfit: number;         // 预估毛利
  estimatedProfitRate: number;     // 预估毛利率
  receivedAmount: number;          // 已回款
  unreceivedAmount: number;        // 未回款
  totalPayableAmount: number;      // 合计需支付项目金额（含达人返点在应支付里回收）
  paidProjectAmount: number;       // 已支付项目金额
  unpaidProjectAmount: number;     // 未支付项目金额
  remarks: string;                 // 备注1
}

export class FinancialExportService {
  private databaseService: DatabaseService;
  private projectService: ProjectService;

  constructor() {
    this.databaseService = new DatabaseService();
    this.projectService = new ProjectService();
  }

  /**
   * 导出财务报表Excel
   */
  async exportFinancialReport(params: FinancialExportParams): Promise<Buffer> {
    console.log('🚀 开始生成财务报表...', params);

    // 获取基础数据
    const { projects, brands, revenues, weeklyBudgets, suppliers } = await this.getBaseData(params);

    // 创建Excel工作簿
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'CanTV财务系统';
    workbook.created = new Date();
    workbook.company = 'CanTV';
    workbook.title = `财务报表_${params.year}年`;
    workbook.subject = '项目财务数据汇总报表';
    workbook.description = `包含${params.year}年度所有项目的财务数据，包括项目汇总、品牌详情和品牌汇总等信息。`;

    // 1. 创建项目汇总表
    await this.createProjectSummarySheet(workbook, projects, brands, revenues, weeklyBudgets, suppliers, params.year);

    // 2. 创建各品牌详情表
    for (const brand of brands) {
      const brandProjects = projects.filter(p => p.brandId === brand.id);
      if (brandProjects.length > 0) {
        await this.createBrandDetailSheet(workbook, brand, brandProjects, revenues, weeklyBudgets, params.year);
      }
    }

    // 3. 创建品牌汇总表
    await this.createBrandSummarySheet(workbook, brands, projects, revenues, weeklyBudgets, params.year);

    // 生成Buffer
    const buffer = await workbook.xlsx.writeBuffer();
    console.log('✅ 财务报表生成完成');

    return buffer as Buffer;
  }

  /**
   * 获取基础数据
   */
  private async getBaseData(params: FinancialExportParams) {
    const startDate = new Date(params.year, 0, 1); // 年初
    const endDate = new Date(params.year, 11, 31); // 年末

    console.log(`📊 获取${params.year}年数据...`);

    // 获取项目数据
    const projectsResult = await this.projectService.getProjects({
      startDate,
      endDate,
      page: 1,
      pageSize: 10000 // 获取所有数据
    });

    let projects = projectsResult.projects;

    // 应用过滤条件
    if (params.brandIds && params.brandIds.length > 0) {
      projects = projects.filter(p => params.brandIds!.includes(p.brandId));
    }

    if (!params.includeCompleted) {
      projects = projects.filter(p => p.status !== 'completed');
    }

    if (!params.includeCancelled) {
      projects = projects.filter(p => p.status !== 'cancelled');
    }

    // 获取品牌数据
    const brandsResult = await this.projectService.getBrands({ pageSize: 1000 });
    const brands = brandsResult.brands;

    // 获取项目收入数据
    const revenues: ProjectRevenue[] = [];
    for (const project of projects) {
      try {
        const projectRevenues = await this.projectService.getProjectRevenues({ projectId: project.id });
        revenues.push(...projectRevenues.revenues);
      } catch (error) {
        console.warn(`获取项目 ${project.id} 收入数据失败:`, error);
      }
    }

    // 获取周预算数据
    const weeklyBudgets: WeeklyBudget[] = [];
    for (const project of projects) {
      try {
        const projectBudgets = await this.projectService.getWeeklyBudgets({ projectId: project.id });
        weeklyBudgets.push(...projectBudgets.weeklyBudgets);
      } catch (error) {
        console.warn(`获取项目 ${project.id} 周预算数据失败:`, error);
      }
    }

    // 获取供应商数据
    const suppliersResult = await this.projectService.getSuppliers({ pageSize: 1000 });
    const suppliers = suppliersResult.suppliers;

    console.log(`📈 数据统计: 项目${projects.length}个, 品牌${brands.length}个, 收入${revenues.length}条, 周预算${weeklyBudgets.length}条, 供应商${suppliers.length}个`);

    return { projects, brands, revenues, weeklyBudgets, suppliers };
  }

  /**
   * 创建项目汇总表
   */
  private async createProjectSummarySheet(
    workbook: ExcelJS.Workbook,
    projects: Project[],
    brands: Brand[],
    revenues: ProjectRevenue[],
    weeklyBudgets: WeeklyBudget[],
    suppliers: Supplier[],
    year: number
  ) {
    const worksheet = workbook.addWorksheet('项目汇总表');

    // 添加标题行
    worksheet.mergeCells('A1:AF1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = `CanTV财务系统 - ${year}年度项目汇总报表`;
    titleCell.font = {
      name: '微软雅黑',
      size: 16,
      bold: true,
      color: { argb: 'FF2E4BC6' }
    };
    titleCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFF8F9FA' }
    };
    titleCell.border = {
      top: { style: 'thin', color: { argb: 'FFD1D5DB' } },
      left: { style: 'thin', color: { argb: 'FFD1D5DB' } },
      bottom: { style: 'thin', color: { argb: 'FFD1D5DB' } },
      right: { style: 'thin', color: { argb: 'FFD1D5DB' } }
    };
    worksheet.getRow(1).height = 35;

    // 添加生成时间信息
    worksheet.mergeCells('A2:AF2');
    const infoCell = worksheet.getCell('A2');
    infoCell.value = `生成时间: ${new Date().toLocaleString('zh-CN')} | 数据范围: ${year}年1月1日 - ${year}年12月31日`;
    infoCell.font = {
      name: '微软雅黑',
      size: 10,
      italic: true,
      color: { argb: 'FF6B7280' }
    };
    infoCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    worksheet.getRow(2).height = 20;

    // 空行
    worksheet.addRow([]);

    // 设置列标题
    const headers = [
      '下单时间', '品牌', '品类', '执行项目', '项目名称', '合同签署情况', '合同类型',
      '项目规划金额', '不含税金额', '项目进度', '预计回款时间', '回款周期', '执行项目周期',
      '已回款额', '未回款额', '回款状态', '采购成本（去税）', '返点', '居间', '毛利', '毛利率',
      '备注', '供应商公司名称', '服务内容', '供应商采购成本', '专票税率', '不含税金额',
      '已付金额', '未付款金额', '6月应付', '剩余成本', '7月预计付款', '转后续月份'
    ];

    const headerRowIndex = 4;
    worksheet.addRow(headers);

    // 设置表头样式
    const headerRow = worksheet.getRow(headerRowIndex);
    headerRow.height = 40; // 增加表头高度
    headerRow.font = {
      name: '微软雅黑',
      size: 12, // 增加表头字体大小
      bold: true,
      color: { argb: 'FFFFFFFF' }
    };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF4F46E5' }
    };
    headerRow.alignment = {
      horizontal: 'center',
      vertical: 'middle',
      wrapText: true
    };

    // 为表头添加边框
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.border = {
        top: { style: 'medium', color: { argb: 'FF4F46E5' } },
        left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
        bottom: { style: 'medium', color: { argb: 'FF4F46E5' } },
        right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
      };
    });

    // 添加数据行
    let currentRowIndex = headerRowIndex + 1;
    for (const project of projects) {
      const brand = brands.find(b => b.id === project.brandId);
      const projectRevenues = revenues.filter(r => r.projectId === project.id);
      const projectBudgets = weeklyBudgets.filter(b => b.projectId === project.id);

      // 计算汇总数据
      const totalReceivedAmount = projectRevenues
        .filter(r => r.status === 'received')
        .reduce((sum, r) => sum + (r.actualAmount || 0), 0);

      const totalUnreceivedAmount = projectRevenues
        .filter(r => r.status !== 'received')
        .reduce((sum, r) => sum + r.plannedAmount, 0);

      const totalPaidAmount = projectBudgets.reduce((sum, b) => sum + b.paidAmount, 0);
      const totalUnpaidAmount = projectBudgets.reduce((sum, b) => sum + b.remainingAmount, 0);

      const row: ProjectSummaryRow = {
        orderDate: project.createdAt?.toISOString().split('T')[0] || '',
        brandName: brand?.name || '未知品牌',
        category: '数码', // 暂时固定，后续可从项目中获取
        executiveProject: project.executorPMInfo?.name || project.executorPM,
        projectName: project.projectName,
        contractSigningStatus: this.getContractSigningStatusText(project.contractSigningStatus),
        contractType: this.getContractTypeText(project.contractType),
        planningAmount: project.budget.planningBudget,
        taxExcludedAmount: project.budget.planningBudget * 0.94, // 假设6%税率
        projectProgress: this.getProjectProgressText(project.status as ProjectStatus),
        expectedPaymentTime: project.expectedPaymentMonth || '',
        paymentCycle: project.paymentTermDays ? `T+${project.paymentTermDays}` : '',
        executionCycle: `${project.period.startDate.toISOString().split('T')[0]} ~ ${project.period.endDate.toISOString().split('T')[0]}`,
        receivedAmount: totalReceivedAmount,
        unreceivedAmount: totalUnreceivedAmount,
        paymentStatus: this.getPaymentStatusText(projectRevenues),
        purchaseCost: project.cost.influencerCost + project.cost.adCost + project.cost.otherCost,
        rebate: project.cost.estimatedInfluencerRebate,
        intermediary: 0, // 暂时为0，后续可扩展
        grossProfit: project.profit.profit,
        grossProfitRate: project.profit.grossMargin,
        remarks: '',
        supplierName: this.getMainSupplierName(projectBudgets, suppliers),
        serviceContent: this.getServiceContent(projectBudgets),
        supplierCost: totalUnpaidAmount + totalPaidAmount,
        invoiceTaxRate: this.getMainTaxRate(projectBudgets),
        supplierTaxExcluded: (totalUnpaidAmount + totalPaidAmount) * 0.94,
        paidAmount: totalPaidAmount,
        unpaidAmount: totalUnpaidAmount,
        junePayable: this.getMonthlyPayable(projectBudgets, 6),
        remainingCost: totalUnpaidAmount,
        julyExpectedPayment: this.getMonthlyPayable(projectBudgets, 7),
        transferToNextMonth: 0 // 暂时为0
      };

      const dataRow = worksheet.addRow(Object.values(row));

      // 设置数据行样式
      this.styleDataRow(dataRow, currentRowIndex, headers.length);
      currentRowIndex++;
    }

    // 如果没有数据，添加提示行
    if (projects.length === 0) {
      const noDataRow = worksheet.addRow(['暂无数据', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']);
      noDataRow.getCell(1).value = `${year}年暂无项目数据`;
      noDataRow.font = {
        name: '微软雅黑',
        size: 12,
        italic: true,
        color: { argb: 'FF6B7280' }
      };
      noDataRow.alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.mergeCells(`A${currentRowIndex}:AF${currentRowIndex}`);
      noDataRow.height = 40;
    }

    // 设置列宽和样式
    this.setupProjectSummaryColumns(worksheet, headers);

    console.log('✅ 项目汇总表创建完成');
  }

  /**
   * 创建品牌详情表
   */
  private async createBrandDetailSheet(
    workbook: ExcelJS.Workbook,
    brand: Brand,
    projects: Project[],
    revenues: ProjectRevenue[],
    weeklyBudgets: WeeklyBudget[],
    year: number
  ) {
    const worksheet = workbook.addWorksheet(brand.name);

    // 添加品牌标题行
    worksheet.mergeCells('A1:Q1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = `${brand.name} - ${year}年度项目详情报表`;
    titleCell.font = {
      name: '微软雅黑',
      size: 14,
      bold: true,
      color: { argb: 'FF1E40AF' }
    };
    titleCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFEFF6FF' }
    };
    worksheet.getRow(1).height = 30;

    // 添加品牌信息行
    worksheet.mergeCells('A2:Q2');
    const infoCell = worksheet.getCell('A2');
    infoCell.value = `品牌描述: ${brand.description || '暂无描述'} | 生成时间: ${new Date().toLocaleString('zh-CN')}`;
    infoCell.font = {
      name: '微软雅黑',
      size: 10,
      italic: true,
      color: { argb: 'FF6B7280' }
    };
    infoCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    worksheet.getRow(2).height = 20;

    // 空行
    worksheet.addRow([]);

    // 设置列标题
    const headers = [
      '品牌', '品类', '下单时间', '执行项目', '项目名称', '合同签署情况', '规划金额',
      '执行情况', '预计回款日期', '回款情况', '已回款额', '未回款额', '账期',
      '毛利额', '毛利率', '已支付项目金额', '未支付项目金额'
    ];

    const headerRowIndex = 4;
    worksheet.addRow(headers);

    // 设置表头样式
    const headerRow = worksheet.getRow(headerRowIndex);
    headerRow.height = 35; // 增加表头高度
    headerRow.font = {
      name: '微软雅黑',
      size: 12, // 增加表头字体大小
      bold: true,
      color: { argb: 'FFFFFFFF' }
    };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF1E40AF' }
    };
    headerRow.alignment = {
      horizontal: 'center',
      vertical: 'middle',
      wrapText: true
    };

    // 为表头添加边框
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.border = {
        top: { style: 'medium', color: { argb: 'FF1E40AF' } },
        left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
        bottom: { style: 'medium', color: { argb: 'FF1E40AF' } },
        right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
      };
    });

    // 添加数据行
    let currentRowIndex = headerRowIndex + 1;
    for (const project of projects) {
      const projectRevenues = revenues.filter(r => r.projectId === project.id);
      const projectBudgets = weeklyBudgets.filter(b => b.projectId === project.id);

      const totalReceivedAmount = projectRevenues
        .filter(r => r.status === 'received')
        .reduce((sum, r) => sum + (r.actualAmount || 0), 0);

      const totalUnreceivedAmount = projectRevenues
        .filter(r => r.status !== 'received')
        .reduce((sum, r) => sum + r.plannedAmount, 0);

      const totalPaidAmount = projectBudgets.reduce((sum, b) => sum + b.paidAmount, 0);
      const totalUnpaidAmount = projectBudgets.reduce((sum, b) => sum + b.remainingAmount, 0);

      const row: BrandProjectRow = {
        brandName: brand.name,
        category: '数码',
        orderDate: project.createdAt?.toISOString().split('T')[0] || '',
        executiveProject: project.executorPMInfo?.name || project.executorPM,
        projectName: project.projectName,
        contractSigningStatus: this.getContractSigningStatusText(project.contractSigningStatus),
        planningAmount: project.budget.planningBudget,
        executionStatus: this.getProjectProgressText(project.status as ProjectStatus),
        expectedPaymentDate: project.expectedPaymentMonth || '',
        paymentStatus: this.getPaymentStatusText(projectRevenues),
        receivedAmount: totalReceivedAmount,
        unreceivedAmount: totalUnreceivedAmount,
        paymentTerms: project.paymentTermDays ? `T+${project.paymentTermDays}` : '',
        grossProfitAmount: project.profit.profit,
        grossProfitRate: project.profit.grossMargin,
        paidProjectAmount: totalPaidAmount,
        unpaidProjectAmount: totalUnpaidAmount
      };

      const dataRow = worksheet.addRow(Object.values(row));

      // 设置数据行样式
      this.styleDataRow(dataRow, currentRowIndex, headers.length);
      currentRowIndex++;
    }

    // 如果没有数据，添加提示行
    if (projects.length === 0) {
      const noDataRow = worksheet.addRow(['暂无数据', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']);
      noDataRow.getCell(1).value = `${brand.name}在${year}年暂无项目数据`;
      noDataRow.font = {
        name: '微软雅黑',
        size: 12,
        italic: true,
        color: { argb: 'FF6B7280' }
      };
      noDataRow.alignment = { horizontal: 'center', vertical: 'middle' };
      worksheet.mergeCells(`A${currentRowIndex}:Q${currentRowIndex}`);
      noDataRow.height = 40;
    }

    // 设置列宽和样式
    this.setupBrandDetailColumns(worksheet, headers);

    console.log(`✅ 品牌详情表创建完成: ${brand.name}`);
  }

  /**
   * 创建品牌汇总表
   */
  private async createBrandSummarySheet(
    workbook: ExcelJS.Workbook,
    brands: Brand[],
    projects: Project[],
    revenues: ProjectRevenue[],
    weeklyBudgets: WeeklyBudget[],
    year: number
  ) {
    const worksheet = workbook.addWorksheet('品牌汇总表');

    // 添加标题行
    worksheet.mergeCells('A1:M1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = `CanTV财务系统 - ${year}年度品牌汇总报表`;
    titleCell.font = {
      name: '微软雅黑',
      size: 16,
      bold: true,
      color: { argb: 'FF7C3AED' }
    };
    titleCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFAF5FF' }
    };
    worksheet.getRow(1).height = 35;

    // 添加统计信息行
    const totalBrands = brands.filter(b => projects.some(p => p.brandId === b.id)).length;
    const totalProjects = projects.length;
    worksheet.mergeCells('A2:M2');
    const statsCell = worksheet.getCell('A2');
    statsCell.value = `统计范围: ${year}年度 | 品牌数量: ${totalBrands}个 | 项目总数: ${totalProjects}个 | 生成时间: ${new Date().toLocaleString('zh-CN')}`;
    statsCell.font = {
      name: '微软雅黑',
      size: 10,
      italic: true,
      color: { argb: 'FF6B7280' }
    };
    statsCell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    };
    worksheet.getRow(2).height = 20;

    // 空行
    worksheet.addRow([]);

    // 设置列标题
    const headers = [
      '品牌', '品类', '品牌下单金额', '已执行完金额', '执行中项目金额', '预估毛利',
      '预估毛利率', '已回款', '未回款', '合计需支付项目金额（含达人返点在应支付里回收）',
      '已支付项目金额', '未支付项目金额', '备注1'
    ];

    const headerRowIndex = 4;
    worksheet.addRow(headers);

    // 设置表头样式
    const headerRow = worksheet.getRow(headerRowIndex);
    headerRow.height = 40; // 增加表头高度
    headerRow.font = {
      name: '微软雅黑',
      size: 12, // 增加表头字体大小
      bold: true,
      color: { argb: 'FFFFFFFF' }
    };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF7C3AED' }
    };
    headerRow.alignment = {
      horizontal: 'center',
      vertical: 'middle',
      wrapText: true
    };

    // 为表头添加边框
    headers.forEach((_, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.border = {
        top: { style: 'medium', color: { argb: 'FF7C3AED' } },
        left: { style: 'thin', color: { argb: 'FFFFFFFF' } },
        bottom: { style: 'medium', color: { argb: 'FF7C3AED' } },
        right: { style: 'thin', color: { argb: 'FFFFFFFF' } }
      };
    });

    // 添加数据行
    let currentRowIndex = headerRowIndex + 1;
    let totalOrderAmount = 0;
    let totalCompletedAmount = 0;
    let totalExecutingAmount = 0;
    let totalProfit = 0;
    let totalReceivedAmount = 0;
    let totalUnreceivedAmount = 0;
    let totalPaidAmount = 0;
    let totalUnpaidAmount = 0;

    for (const brand of brands) {
      const brandProjects = projects.filter(p => p.brandId === brand.id);

      if (brandProjects.length === 0) continue;

      const brandRevenues = revenues.filter(r =>
        brandProjects.some(p => p.id === r.projectId)
      );
      const brandBudgets = weeklyBudgets.filter(b =>
        brandProjects.some(p => p.id === b.projectId)
      );

      // 计算汇总数据
      const brandOrderAmount = brandProjects.reduce((sum, p) => sum + p.budget.planningBudget, 0);
      const completedAmount = brandProjects
        .filter(p => p.status === 'completed')
        .reduce((sum, p) => sum + p.budget.planningBudget, 0);
      const executingAmount = brandProjects
        .filter(p => p.status === 'active')
        .reduce((sum, p) => sum + p.budget.planningBudget, 0);
      const brandProfit = brandProjects.reduce((sum, p) => sum + p.profit.profit, 0);
      const avgProfitRate = brandProjects.length > 0
        ? brandProjects.reduce((sum, p) => sum + p.profit.grossMargin, 0) / brandProjects.length
        : 0;

      const brandReceivedAmount = brandRevenues
        .filter(r => r.status === 'received')
        .reduce((sum, r) => sum + (r.actualAmount || 0), 0);
      const brandUnreceivedAmount = brandRevenues
        .filter(r => r.status !== 'received')
        .reduce((sum, r) => sum + r.plannedAmount, 0);

      const brandPaidAmount = brandBudgets.reduce((sum, b) => sum + b.paidAmount, 0);
      const brandUnpaidAmount = brandBudgets.reduce((sum, b) => sum + b.remainingAmount, 0);
      const totalPayableAmount = brandPaidAmount + brandUnpaidAmount +
        brandProjects.reduce((sum, p) => sum + p.cost.estimatedInfluencerRebate, 0);

      // 累计总计
      totalOrderAmount += brandOrderAmount;
      totalCompletedAmount += completedAmount;
      totalExecutingAmount += executingAmount;
      totalProfit += brandProfit;
      totalReceivedAmount += brandReceivedAmount;
      totalUnreceivedAmount += brandUnreceivedAmount;
      totalPaidAmount += brandPaidAmount;
      totalUnpaidAmount += brandUnpaidAmount;

      const row: BrandSummaryRow = {
        brandName: brand.name,
        category: '数码',
        brandOrderAmount: brandOrderAmount,
        executedAmount: completedAmount,
        executingAmount: executingAmount,
        estimatedProfit: brandProfit,
        estimatedProfitRate: avgProfitRate,
        receivedAmount: brandReceivedAmount,
        unreceivedAmount: brandUnreceivedAmount,
        totalPayableAmount: totalPayableAmount,
        paidProjectAmount: brandPaidAmount,
        unpaidProjectAmount: brandUnpaidAmount,
        remarks: ''
      };

      const dataRow = worksheet.addRow(Object.values(row));

      // 设置数据行样式
      this.styleDataRow(dataRow, currentRowIndex, headers.length);
      currentRowIndex++;
    }

    // 添加合计行
    if (currentRowIndex > headerRowIndex + 1) {
      const totalRow = worksheet.addRow([
        '合计', '', totalOrderAmount, totalCompletedAmount, totalExecutingAmount,
        totalProfit, totalProfit / totalOrderAmount, totalReceivedAmount, totalUnreceivedAmount,
        totalPaidAmount + totalUnpaidAmount, totalPaidAmount, totalUnpaidAmount, ''
      ]);

      // 设置合计行样式
      totalRow.height = 35; // 增加合计行高度
      totalRow.font = {
        name: '微软雅黑',
        size: 12, // 增加合计行字体大小
        bold: true,
        color: { argb: 'FF1F2937' }
      };
      totalRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF3F4F6' }
      };

      headers.forEach((_, index) => {
        const cell = totalRow.getCell(index + 1);
        cell.border = {
          top: { style: 'medium', color: { argb: 'FF374151' } },
          left: { style: 'thin', color: { argb: 'FFD1D5DB' } },
          bottom: { style: 'medium', color: { argb: 'FF374151' } },
          right: { style: 'thin', color: { argb: 'FFD1D5DB' } }
        };

        if (index >= 2) { // 数字列
          cell.alignment = { horizontal: 'right', vertical: 'middle' };
          if (index >= 2 && index <= 11) {
            cell.numFmt = '#,##0.00';
          }
          if (index === 6) { // 毛利率
            cell.numFmt = '0.00%';
          }
        } else {
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        }
      });
    }

    // 设置列宽和样式
    this.setupBrandSummaryColumns(worksheet, headers);

    console.log('✅ 品牌汇总表创建完成');
  }

  /**
   * 获取合同签署状态文本
   */
  private getContractSigningStatusText(status: ContractSigningStatus): string {
    const statusMap: Record<string, string> = {
      'NO_CONTRACT': '无合同',
      'no_contract': '无合同',
      'SIGNED': '已签订',
      'signed': '已签订',
      'SIGNING': '签订中',
      'signing': '签订中',
      'PENDING': '待定',
      'pending': '待定'
    };
    return statusMap[status] || status;
  }

  /**
   * 获取合同类型文本
   */
  private getContractTypeText(type: ContractType): string {
    const typeMap: Record<string, string> = {
      'ANNUAL_FRAME': '年框',
      'annual_frame': '年框',
      'QUARTERLY_FRAME': '季框',
      'quarterly_frame': '季框',
      'SINGLE': '单次',
      'single': '单次',
      'PO_ORDER': 'PO单',
      'po_order': 'PO单',
      'JING_TASK': '京任务',
      'jing_task': '京任务'
    };
    return typeMap[type] || type;
  }

  /**
   * 获取项目进度文本
   */
  private getProjectProgressText(status: ProjectStatus): string {
    const statusMap: Record<string, string> = {
      'DRAFT': '已创建',
      'draft': '已创建',
      'ACTIVE': '执行中',
      'active': '执行中',
      'COMPLETED': '已完成',
      'completed': '已完成',
      'CANCELLED': '已取消',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  }

  /**
   * 获取回款状态文本
   */
  private getPaymentStatusText(revenues: ProjectRevenue[]): string {
    if (revenues.length === 0) return '无收入计划';

    const receivedCount = revenues.filter(r => r.status === 'received').length;
    const totalCount = revenues.length;

    if (receivedCount === 0) return '未回款';
    if (receivedCount === totalCount) return '已回款';
    return '部分回款';
  }

  /**
   * 获取主要供应商名称
   */
  private getMainSupplierName(budgets: WeeklyBudget[], suppliers: Supplier[]): string {
    if (budgets.length === 0) return '';

    // 按合同金额排序，取最大的供应商
    const supplierAmounts = new Map<string, number>();

    budgets.forEach(budget => {
      if (budget.supplierId) {
        const current = supplierAmounts.get(budget.supplierId) || 0;
        supplierAmounts.set(budget.supplierId, current + budget.contractAmount);
      }
    });

    if (supplierAmounts.size === 0) return '';

    const sortedSuppliers = Array.from(supplierAmounts.entries())
      .sort((a, b) => b[1] - a[1]);

    if (sortedSuppliers.length === 0) return '';

    const firstSupplier = sortedSuppliers[0];
    if (!firstSupplier) return '';

    const mainSupplierId = firstSupplier[0];
    const supplier = suppliers.find(s => s.id === mainSupplierId);
    return supplier?.name || '';
  }

  /**
   * 获取服务内容
   */
  private getServiceContent(budgets: WeeklyBudget[]): string {
    const serviceTypes = [...new Set(budgets.map(b => b.serviceType))];
    return serviceTypes.map(type => this.getServiceTypeText(type)).join(', ');
  }

  /**
   * 获取主要税率
   */
  private getMainTaxRate(budgets: WeeklyBudget[]): string {
    if (budgets.length === 0) return '专票6%';

    // 按合同金额排序，取最大的税率
    const taxRateAmounts = new Map<string, number>();

    budgets.forEach(budget => {
      const current = taxRateAmounts.get(budget.taxRate) || 0;
      taxRateAmounts.set(budget.taxRate, current + budget.contractAmount);
    });

    if (taxRateAmounts.size === 0) return '专票6%';

    const sortedTaxRates = Array.from(taxRateAmounts.entries())
      .sort((a, b) => b[1] - a[1]);

    if (sortedTaxRates.length === 0) return '专票6%';

    const firstTaxRate = sortedTaxRates[0];
    if (!firstTaxRate) return '专票6%';

    const mainTaxRate = firstTaxRate[0];
    return this.getTaxRateText(mainTaxRate);
  }

  /**
   * 获取指定月份应付金额
   */
  private getMonthlyPayable(budgets: WeeklyBudget[], month: number): number {
    // 这里需要根据实际业务逻辑计算
    // 暂时返回剩余金额的平均分配
    const totalRemaining = budgets.reduce((sum, b) => sum + b.remainingAmount, 0);
    return totalRemaining / 12; // 简单平均分配到12个月
  }

  /**
   * 获取收入状态文本
   */
  private getRevenueStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'receiving': '收款中',
      'received': '已收款',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  }

  /**
   * 获取收入类型文本
   */
  private getRevenueTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      'influencer_income': '达人收入',
      'project_income': '项目收入',
      'other': '其他收入'
    };
    return typeMap[type] || type;
  }

  /**
   * 获取服务类型文本
   */
  private getServiceTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      'INFLUENCER': '达人服务',
      'influencer': '达人服务',
      'ADVERTISING': '投流服务',
      'advertising': '投流服务',
      'OTHER': '其他服务',
      'other': '其他服务'
    };
    return typeMap[type] || type;
  }

  /**
   * 获取供应商状态文本
   */
  private getSupplierStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'active': '活跃',
      'inactive': '停用',
      'pending': '待审核',
      'blacklisted': '黑名单'
    };
    return statusMap[status] || status;
  }

  /**
   * 获取税率文本
   */
  private getTaxRateText(taxRate: string): string {
    const taxRateMap: Record<string, string> = {
      'SPECIAL_1': '专票1%',
      'special_1': '专票1%',
      'SPECIAL_3': '专票3%',
      'special_3': '专票3%',
      'SPECIAL_6': '专票6%',
      'special_6': '专票6%',
      'GENERAL': '普票',
      'general': '普票'
    };
    return taxRateMap[taxRate] || taxRate;
  }

  /**
   * 获取周预算状态文本
   */
  private getWeeklyBudgetStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'draft': '草稿',
      'approved': '已批准',
      'executing': '执行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || status;
  }

  /**
   * 获取单据类型文本
   */
  private getDocumentTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      'project_initiation': '项目立项表',
      'project_proposal': '项目提案',
      'project_plan': '项目计划',
      'project_execution': '项目执行',
      'project_summary': '项目总结'
    };
    return typeMap[type] || type;
  }

  /**
   * 设置数据行样式
   */
  private styleDataRow(row: ExcelJS.Row, rowIndex: number, columnCount: number): void {
    row.height = 30; // 增加行高提高可读性
    row.font = {
      name: '微软雅黑',
      size: 11 // 增加字体大小提高可读性
    };

    // 交替行颜色
    const isEvenRow = rowIndex % 2 === 0;
    const backgroundColor = isEvenRow ? 'FFFFFFFF' : 'FFF8FAFC';

    for (let i = 1; i <= columnCount; i++) {
      const cell = row.getCell(i);

      // 设置背景色
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: backgroundColor }
      };

      // 设置边框
      cell.border = {
        top: { style: 'thin', color: { argb: 'FFE5E7EB' } },
        left: { style: 'thin', color: { argb: 'FFE5E7EB' } },
        bottom: { style: 'thin', color: { argb: 'FFE5E7EB' } },
        right: { style: 'thin', color: { argb: 'FFE5E7EB' } }
      };

      // 设置基础对齐方式
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: true,
        indent: 1 // 增加缩进提高可读性
      };

      // 根据列类型设置特殊样式
      const cellValue = cell.value;
      if (typeof cellValue === 'number') {
        // 数字列 - 右对齐，增加内边距
        cell.alignment = {
          horizontal: 'right',
          vertical: 'middle',
          indent: 1
        };

        // 金额列添加千分位分隔符和颜色
        if (i >= 8 && i <= 21) { // 金额相关列
          cell.numFmt = '#,##0.00';
          // 增强金额颜色对比度
          if (cellValue < 0) {
            cell.font = { ...cell.font, color: { argb: 'FFDC2626' }, bold: true };
          } else if (cellValue > 0) {
            cell.font = { ...cell.font, color: { argb: 'FF059669' }, bold: false };
          }
        }

        // 百分比列 - 特殊格式
        if (i === 21) { // 毛利率列
          cell.numFmt = '0.00%';
          cell.font = { ...cell.font, bold: true };
        }
      } else if (typeof cellValue === 'string') {
        // 文本列 - 根据内容调整对齐
        if (cellValue.length > 10) {
          // 长文本左对齐
          cell.alignment = {
            horizontal: 'left',
            vertical: 'middle',
            wrapText: true,
            indent: 1
          };
        }

        // 状态颜色 - 增强对比度和可读性
        if (cellValue.includes('已完成') || cellValue === '已创建') {
          cell.font = { ...cell.font, color: { argb: 'FF059669' }, bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF0FDF4' } // 浅绿色背景
          };
        } else if (cellValue.includes('执行中')) {
          cell.font = { ...cell.font, color: { argb: 'FF2563EB' }, bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFEFF6FF' } // 浅蓝色背景
          };
        } else if (cellValue.includes('已取消')) {
          cell.font = { ...cell.font, color: { argb: 'FFDC2626' }, bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFEF2F2' } // 浅红色背景
          };
        }

        // 合同状态颜色 - 增强视觉效果
        if (cellValue === '已签订') {
          cell.font = { ...cell.font, color: { argb: 'FF059669' }, bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF0FDF4' }
          };
        } else if (cellValue === '签订中') {
          cell.font = { ...cell.font, color: { argb: 'FFD97706' }, bold: true };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFFBEB' } // 浅橙色背景
          };
        } else if (cellValue === '无合同') {
          cell.font = { ...cell.font, color: { argb: 'FF6B7280' } };
        }
      }
    }
  }

  /**
   * 设置项目汇总表列样式
   */
  private setupProjectSummaryColumns(worksheet: ExcelJS.Worksheet, headers: string[]): void {
    // 优化后的列宽配置 - 根据内容长度和重要性调整
    const columnWidths = [
      14, // 下单时间 - 增加宽度适应日期格式
      18, // 品牌 - 增加宽度适应品牌名称
      12, // 品类
      16, // 执行项目 - 增加宽度适应人员姓名
      25, // 项目名称 - 显著增加宽度，这是重要信息
      16, // 合同签署情况 - 增加宽度适应中文状态
      14, // 合同类型 - 增加宽度适应中文类型
      18, // 项目规划金额 - 增加宽度适应大金额显示
      18, // 不含税金额 - 增加宽度适应大金额显示
      14, // 项目进度 - 增加宽度适应中文状态
      18, // 预计回款时间 - 增加宽度适应月份显示
      14, // 回款周期 - 增加宽度适应T+天数格式
      25, // 执行项目周期 - 显著增加宽度适应日期范围
      18, // 已回款额 - 增加宽度适应大金额显示
      18, // 未回款额 - 增加宽度适应大金额显示
      14, // 回款状态 - 增加宽度适应中文状态
      18, // 采购成本（去税）- 增加宽度适应大金额显示
      14, // 返点 - 增加宽度适应金额显示
      14, // 居间 - 增加宽度适应金额显示
      16, // 毛利 - 增加宽度适应金额显示
      14, // 毛利率 - 增加宽度适应百分比显示
      20, // 备注 - 增加宽度适应备注内容
      22, // 供应商公司名称 - 显著增加宽度适应公司名称
      18, // 服务内容 - 增加宽度适应中文服务类型
      18, // 供应商采购成本 - 增加宽度适应大金额显示
      14, // 专票税率 - 增加宽度适应中文税率
      18, // 不含税金额 - 增加宽度适应大金额显示
      18, // 已付金额 - 增加宽度适应大金额显示
      18, // 未付款金额 - 增加宽度适应大金额显示
      14, // 6月应付 - 增加宽度适应金额显示
      16, // 剩余成本 - 增加宽度适应金额显示
      18, // 7月预计付款 - 增加宽度适应金额显示
      18  // 转后续月份 - 增加宽度适应金额显示
    ];

    // 设置列宽
    headers.forEach((_, index) => {
      const column = worksheet.getColumn(index + 1);
      column.width = columnWidths[index] || 12;
    });

    // 冻结窗格（冻结前6列和标题行）- 优化浏览体验
    worksheet.views = [
      {
        state: 'frozen',
        xSplit: 6, // 增加冻结列数，包含更多关键信息
        ySplit: 4,
        topLeftCell: 'G5',
        activeCell: 'A1',
        showGridLines: true,
        showRowColHeaders: true
      }
    ];

    // 设置打印选项
    worksheet.pageSetup = {
      paperSize: 9, // A4
      orientation: 'landscape',
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
      }
    };
  }

  /**
   * 设置品牌详情表列样式
   */
  private setupBrandDetailColumns(worksheet: ExcelJS.Worksheet, headers: string[]): void {
    // 优化后的列宽配置 - 提高可读性
    const columnWidths = [
      18, // 品牌 - 增加宽度适应品牌名称
      12, // 品类
      14, // 下单时间 - 增加宽度适应日期格式
      16, // 执行项目 - 增加宽度适应人员姓名
      25, // 项目名称 - 显著增加宽度，重要信息
      16, // 合同签署情况 - 增加宽度适应中文状态
      18, // 规划金额 - 增加宽度适应大金额显示
      14, // 执行情况 - 增加宽度适应中文状态
      18, // 预计回款日期 - 增加宽度适应日期显示
      14, // 回款情况 - 增加宽度适应中文状态
      18, // 已回款额 - 增加宽度适应大金额显示
      18, // 未回款额 - 增加宽度适应大金额显示
      14, // 账期 - 增加宽度适应T+天数格式
      18, // 毛利额 - 增加宽度适应金额显示
      14, // 毛利率 - 增加宽度适应百分比显示
      20, // 已支付项目金额 - 增加宽度适应大金额显示
      20  // 未支付项目金额 - 增加宽度适应大金额显示
    ];

    // 设置列宽
    headers.forEach((_, index) => {
      const column = worksheet.getColumn(index + 1);
      column.width = columnWidths[index] || 12;
    });

    // 冻结窗格 - 优化品牌详情表浏览
    worksheet.views = [
      {
        state: 'frozen',
        xSplit: 4, // 增加冻结列数
        ySplit: 4,
        topLeftCell: 'E5',
        activeCell: 'A1',
        showGridLines: true,
        showRowColHeaders: true
      }
    ];

    // 设置打印选项
    worksheet.pageSetup = {
      paperSize: 9, // A4
      orientation: 'landscape',
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
      }
    };
  }

  /**
   * 设置品牌汇总表列样式
   */
  private setupBrandSummaryColumns(worksheet: ExcelJS.Worksheet, headers: string[]): void {
    // 优化后的列宽配置 - 提高汇总表可读性
    const columnWidths = [
      20, // 品牌 - 增加宽度适应品牌名称
      12, // 品类
      20, // 品牌下单金额 - 增加宽度适应大金额显示
      20, // 已执行完金额 - 增加宽度适应大金额显示
      20, // 执行中项目金额 - 增加宽度适应大金额显示
      18, // 预估毛利 - 增加宽度适应金额显示
      14, // 预估毛利率 - 增加宽度适应百分比显示
      18, // 已回款 - 增加宽度适应金额显示
      18, // 未回款 - 增加宽度适应金额显示
      30, // 合计需支付项目金额（含达人返点在应支付里回收）- 显著增加宽度适应长标题
      22, // 已支付项目金额 - 增加宽度适应大金额显示
      22, // 未支付项目金额 - 增加宽度适应大金额显示
      25  // 备注1 - 增加宽度适应备注内容
    ];

    // 设置列宽
    headers.forEach((_, index) => {
      const column = worksheet.getColumn(index + 1);
      column.width = columnWidths[index] || 15;
    });

    // 冻结窗格 - 优化品牌汇总表浏览
    worksheet.views = [
      {
        state: 'frozen',
        xSplit: 3, // 增加冻结列数，包含品牌、品类、下单金额
        ySplit: 4,
        topLeftCell: 'D5',
        activeCell: 'A1',
        showGridLines: true,
        showRowColHeaders: true
      }
    ];

    // 设置打印选项
    worksheet.pageSetup = {
      paperSize: 9, // A4
      orientation: 'landscape',
      fitToPage: true,
      fitToWidth: 1,
      fitToHeight: 0,
      margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
      }
    };
  }
}
