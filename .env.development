# 服务器配置
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# 钉钉应用配置
DINGTALK_APP_KEY=ding8foizj7e8k5msbj9
DINGTALK_APP_SECRET=SXtLYIyPkmTbPm0aGuRxrme6TU7qTTq-iwBtVmVcAh30G2kXXRxhHIvy5G-P-Js3
DINGTALK_CORP_ID=dinge21dd1a7d6663db3a39a90f97fcb1e09
DINGTALK_AGENT_ID=3909887093

# 钉钉API配置
DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
DINGTALK_NEW_API_BASE_URL=https://api.dingtalk.com

# 钉钉回调加密配置
DINGTALK_CALLBACK_TOKEN=LdhbojLTXoGMpw7V0VgZjs9Daj
DINGTALK_AES_KEY=siDcS7UGEBaD4njAuW9fQKsEUAG16HhykVFc3C1wUnf
DINGTALK_SUITE_KEY=ding8foizj7e8k5msbj9

# 数据库配置 (可选)
# DATABASE_URL=your_database_url_here
DATABASE_URL="************************************************************/can_tv_project_management?schema=public"

# 日志配置
LOG_LEVEL=info

# 时区配置
TIMEZONE=Asia/Shanghai

# JWT配置 (可选)
# JWT_SECRET=your_jwt_secret_here

# Redis 配置
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=7
REDIS_TTL_ACCESS_TOKEN=3600
REDIS_TTL_REFRESH_TOKEN=604800
REDIS_TTL_USER_SESSION=86400

# 审批配置
# 对公付款审批流程代码（从钉钉后台获取）
APPROVAL_PROCESS_CODE_PAYMENT=PROC-AEBA1016-A1A7-4FD1-A9A6-127922B0D695
# 费用报销审批流程代码
APPROVAL_PROCESS_CODE_EXPENSE=PROC-EXPENSE-001
# 合同审批流程代码
APPROVAL_PROCESS_CODE_CONTRACT=PROC-CONTRACT-001
