# 用户角色权限管理系统

## 概述

本系统实现了基于钉钉用户的完整角色权限管理功能，支持：
- 基于钉钉用户的身份认证
- 灵活的角色定义和管理
- 细粒度的权限控制
- 用户级别和部门级别的角色分配
- API接口的权限验证中间件

## 系统架构

### 数据库模型

#### 1. Role（角色表）
- `id`: 角色唯一标识
- `name`: 角色名称（英文，用于系统内部）
- `displayName`: 角色显示名称（中文）
- `description`: 角色描述
- `isSystem`: 是否为系统内置角色
- `isActive`: 是否激活

#### 2. Permission（权限表）
- `id`: 权限唯一标识
- `name`: 权限名称（格式：module.action）
- `displayName`: 权限显示名称
- `description`: 权限描述
- `module`: 所属模块
- `action`: 操作类型
- `resource`: 资源类型（可选）
- `isSystem`: 是否为系统内置权限

#### 3. RolePermission（角色权限关联表）
- 角色与权限的多对多关联
- 记录角色拥有的权限

#### 4. UserRole（用户角色关联表）
- 用户直接分配的角色
- 支持角色过期时间

#### 5. DepartmentRole（部门角色关联表）
- 部门分配的角色
- 部门成员自动继承部门角色

## 核心功能

### 1. 角色管理

#### 创建角色
```typescript
const roleData = {
  name: 'content_manager',
  displayName: '内容经理',
  description: '负责内容管理的角色',
  createdBy: 'admin_user_id'
};
const role = await roleService.createRole(roleData);
```

#### 更新角色
```typescript
const updateData = {
  displayName: '高级内容经理',
  description: '负责高级内容管理',
  isActive: true
};
await roleService.updateRole(roleId, updateData);
```

#### 为角色分配权限
```typescript
const assignData = {
  roleId: 'role_id',
  permissionIds: ['permission1', 'permission2'],
  assignedBy: 'admin_user_id'
};
await roleService.assignPermissions(assignData);
```

### 2. 权限管理

#### 创建权限
```typescript
const permissionData = {
  name: 'content.create',
  displayName: '创建内容',
  description: '创建新内容的权限',
  module: 'content',
  action: 'create'
};
const permission = await permissionService.createPermission(permissionData);
```

#### 按模块获取权限
```typescript
const modules = await permissionService.getPermissionsByModule();
```

### 3. 用户权限管理

#### 为用户分配角色
```typescript
const assignData = {
  userid: 'dingtalk_user_id',
  roleIds: ['role1', 'role2'],
  assignedBy: 'admin_user_id',
  expiresAt: new Date('2024-12-31') // 可选
};
await userPermissionService.assignUserRoles(assignData);
```

#### 为部门分配角色
```typescript
const assignData = {
  deptId: 123,
  roleIds: ['role1'],
  assignedBy: 'admin_user_id'
};
await userPermissionService.assignDepartmentRoles(assignData);
```

#### 获取用户权限
```typescript
const userPermissions = await userPermissionService.getUserPermissions(userid);
const hasPermission = await userPermissionService.hasPermission(userid, 'project.create');
```

## API接口

### 角色管理API

- `POST /api/roles` - 创建角色
- `GET /api/roles` - 获取角色列表
- `GET /api/roles/:roleId` - 获取角色详情
- `PUT /api/roles/:roleId` - 更新角色
- `DELETE /api/roles/:roleId` - 删除角色
- `POST /api/roles/:roleId/permissions` - 为角色分配权限
- `GET /api/roles/:roleId/permissions` - 获取角色权限
- `GET /api/roles/:roleId/departments` - 获取角色关联的部门列表
- `GET /api/roles/:roleId/users` - 获取角色关联的用户列表（包括直接分配和部门继承）

### 权限管理API

- `POST /api/permissions` - 创建权限
- `GET /api/permissions` - 获取权限列表
- `GET /api/permissions/:permissionId` - 获取权限详情
- `PUT /api/permissions/:permissionId` - 更新权限
- `DELETE /api/permissions/:permissionId` - 删除权限
- `GET /api/permissions/modules` - 按模块分组获取权限

### 用户权限管理API

- `GET /api/users/:userid/permissions` - 获取用户权限信息
- `POST /api/users/:userid/permissions/check` - 检查用户权限
- `GET /api/users/:userid/roles` - 获取用户角色
- `POST /api/users/:userid/roles` - 为用户分配角色
- `GET /api/departments/:deptId/roles` - 获取部门角色
- `POST /api/departments/:deptId/roles` - 为部门分配角色
- `DELETE /api/departments/:deptId/roles/:roleId` - 移除部门特定角色
- `DELETE /api/departments/:deptId/roles` - 清空部门所有角色
- `GET /api/me/permissions` - 获取当前用户权限
- `POST /api/me/permissions/check` - 检查当前用户权限

## 权限中间件

### 使用方法

```typescript
import { requirePermission, requireAllPermissions, PERMISSIONS } from '../middleware/permission.js';

// 单个权限验证
fastify.post('/api/projects', {
  preHandler: [jwtAuthMiddleware, requirePermission(PERMISSIONS.PROJECT_CREATE)],
}, projectController.createProject);

// 多个权限验证（需要全部）
fastify.delete('/api/projects/:id', {
  preHandler: [jwtAuthMiddleware, requireAllPermissions([
    PERMISSIONS.PROJECT_DELETE,
    PERMISSIONS.PROJECT_UPDATE
  ])],
}, projectController.deleteProject);

// 多个权限验证（需要其中一个）
fastify.get('/api/projects', {
  preHandler: [jwtAuthMiddleware, requireAnyPermission([
    PERMISSIONS.PROJECT_READ,
    PERMISSIONS.PROJECT_UPDATE
  ])],
}, projectController.getProjects);
```

### 权限常量

系统预定义了常用权限常量：

```typescript
export const PERMISSIONS = {
  // 用户管理
  USER_READ: 'user.read',
  USER_CREATE: 'user.create',
  USER_UPDATE: 'user.update',
  USER_DELETE: 'user.delete',
  
  // 角色管理
  ROLE_READ: 'role.read',
  ROLE_CREATE: 'role.create',
  ROLE_UPDATE: 'role.update',
  ROLE_DELETE: 'role.delete',
  ROLE_ASSIGN: 'role.assign',
  
  // 项目管理
  PROJECT_READ: 'project.read',
  PROJECT_CREATE: 'project.create',
  PROJECT_UPDATE: 'project.update',
  PROJECT_DELETE: 'project.delete',
  PROJECT_APPROVE: 'project.approve',
  
  // 更多权限...
};
```

## 系统默认角色

### 1. 超级管理员 (super_admin)
- 拥有系统所有权限
- 不能被删除或禁用

### 2. 管理员 (admin)
- 拥有大部分管理权限
- 可以管理用户、角色、项目、品牌等

### 3. 项目经理 (project_manager)
- 项目管理相关权限
- 可以创建和管理项目

### 4. 普通用户 (user)
- 基本的查看权限
- 可以查看项目、品牌等信息

## 初始化和测试

### 初始化系统权限和角色
```bash
node src/scripts/simpleInitPermissions.cjs
```

### 测试权限系统
```bash
node src/scripts/testPermissionsSimple.cjs
```

## 权限继承规则

1. **用户权限** = 直接分配的角色权限 + 部门角色权限
2. **部门权限继承**：用户自动继承所属部门的角色权限
3. **权限优先级**：用户直接分配的权限优先级高于部门继承的权限
4. **角色过期**：支持设置角色过期时间，过期后自动失效

## 安全考虑

1. **权限验证**：所有API接口都需要通过权限中间件验证
2. **系统角色保护**：系统内置角色和权限不能被删除
3. **管理员绕过**：管理员和老板可以绕过权限检查（可配置）
4. **审计日志**：记录所有权限分配和修改操作的审计信息

## 扩展指南

### 添加新权限
1. 在权限常量中定义新权限
2. 通过API或脚本创建权限记录
3. 将权限分配给相应角色

### 添加新角色
1. 通过API创建新角色
2. 为角色分配相应权限
3. 将角色分配给用户或部门

### 自定义权限验证
```typescript
// 自定义权限验证逻辑
export function requireCustomPermission(customLogic: (user: AuthenticatedUser) => boolean) {
  return async function(request: FastifyRequest, reply: FastifyReply) {
    if (!request.user || !customLogic(request.user)) {
      return reply.status(403).send({
        success: false,
        message: '权限不足',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }
  };
}
```
