#!/usr/bin/env node

/**
 * 钉钉消息通知功能演示脚本
 * 演示项目创建和周预算超额通知功能
 */

import { DingTalkNotificationService } from '../src/services/dingtalkNotification.js';

async function demoProjectCreatedNotification() {
  console.log('📋 演示项目创建通知...');
  
  const notificationService = new DingTalkNotificationService();
  
  const notificationData = {
    projectName: '春季营销推广项目',
    brandName: '某知名品牌',
    executorPMName: '张三',
    budget: 500000,
    startDate: '2024-03-01',
    endDate: '2024-05-31',
    creatorName: '李四'
  };
  
  try {
    const success = await notificationService.sendProjectCreatedNotification(
      'demo-pm-001',
      notificationData
    );
    
    if (success) {
      console.log('✅ 项目创建通知发送成功');
    } else {
      console.log('❌ 项目创建通知发送失败');
    }
  } catch (error) {
    console.error('❌ 项目创建通知发送出错:', error);
  }
}

async function demoWeeklyBudgetExceededNotification() {
  console.log('\n⚠️ 演示周预算超额通知...');
  
  const notificationService = new DingTalkNotificationService();
  
  const notificationData = {
    projectName: '春季营销推广项目',
    brandName: '某知名品牌',
    weeklyBudgetTitle: '第一周达人合作预算',
    contractAmount: 80000,
    projectCost: 350000,
    exceedPercentage: 22.9,
    creatorName: '李四'
  };
  
  try {
    const success = await notificationService.sendWeeklyBudgetExceededNotification(
      'demo-creator-001',
      notificationData
    );
    
    if (success) {
      console.log('✅ 周预算超额通知发送成功');
    } else {
      console.log('❌ 周预算超额通知发送失败');
    }
  } catch (error) {
    console.error('❌ 周预算超额通知发送出错:', error);
  }
}

async function demoCustomNotification() {
  console.log('\n📢 演示自定义通知...');
  
  const notificationService = new DingTalkNotificationService();
  
  try {
    const success = await notificationService.sendCustomNotification(
      ['demo-user-001', 'demo-user-002'],
      '系统维护通知',
      '系统将于今晚22:00-24:00进行维护升级，期间可能影响正常使用，请提前做好相关准备。如有紧急情况，请联系技术支持。',
      'text'
    );
    
    if (success) {
      console.log('✅ 自定义通知发送成功');
    } else {
      console.log('❌ 自定义通知发送失败');
    }
  } catch (error) {
    console.error('❌ 自定义通知发送出错:', error);
  }
}

async function runDemo() {
  console.log('🚀 钉钉消息通知功能演示');
  console.log('========================\n');
  
  await demoProjectCreatedNotification();
  await demoWeeklyBudgetExceededNotification();
  await demoCustomNotification();
  
  console.log('\n✨ 演示完成');
  console.log('\n💡 提示:');
  console.log('- 项目创建时会自动通知执行PM');
  console.log('- 周预算超过项目成本10%时会自动通知项目创建人');
  console.log('- 支持发送自定义通知给指定用户');
  console.log('- 所有通知都会记录发送状态和错误信息');
}

// 运行演示
runDemo().catch(console.error);
