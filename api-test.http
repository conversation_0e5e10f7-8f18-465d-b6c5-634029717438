### 钉钉对公付款审批功能 API 测试

### 1. 发起对公付款审批（新版本完整字段）
POST http://localhost:3000/api/weekly-budgets/approval
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "weeklyBudgetId": "budget-001",
  "totalAmount": 10000,
  "paymentReason": "项目执行付款",
  "contractEntity": "company_a",
  "expectedPaymentDate": "2024-01-15",
  "paymentMethod": "bank_transfer",
  "receivingAccount": {
    "accountName": "供应商公司名称",
    "accountNumber": "6228480402564890018",
    "bankName": "中国农业银行",
    "bankCode": "103"
  },
  "relatedApprovalId": "REL-2024-001",
  "invoiceFiles": [
    "https://example.com/invoice1.pdf",
    "https://example.com/invoice2.pdf"
  ],
  "attachments": [
    "https://example.com/contract.pdf"
  ],
  "remark": "第一期付款，请尽快处理"
}

### 1.1. 发起对公付款审批（兼容旧版本字段）
POST http://localhost:3000/api/weekly-budgets/approval
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "weeklyBudgetId": "budget-001",
  "totalAmount": 5000,
  "paymentReason": "紧急付款",
  "expectedPaymentDate": "2024-01-10",
  "receivingAccount": {
    "accountName": "供应商名称",
    "accountNumber": "**********",
    "bankName": "中国银行"
  },
  "approvalAmount": 5000,
  "reason": "紧急付款",
  "remark": "供应商催款"
}

### 2. 直接发起审批（审批管理接口）
POST http://localhost:3000/api/approvals/payment
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "weeklyBudgetId": "budget-001",
  "totalAmount": 5000,
  "paymentReason": "紧急付款",
  "contractEntity": "company_a",
  "expectedPaymentDate": "2024-01-10",
  "paymentMethod": "bank_transfer",
  "receivingAccount": {
    "accountName": "供应商名称",
    "accountNumber": "**********",
    "bankName": "中国银行"
  },
  "remark": "供应商催款"
}

### 3. 获取审批实例列表
GET http://localhost:3000/api/approvals?page=1&pageSize=20&status=PENDING
Authorization: Bearer your-jwt-token

### 4. 获取单个审批实例
GET http://localhost:3000/api/approvals/approval-instance-001
Authorization: Bearer your-jwt-token

### 5. 同步审批状态
POST http://localhost:3000/api/approvals/sync/process-instance-001
Authorization: Bearer your-jwt-token

### 6. 获取审批统计信息
GET http://localhost:3000/api/approvals/stats?startDate=2024-01-01&endDate=2024-12-31
Authorization: Bearer your-jwt-token

### 7. 批量同步审批状态
POST http://localhost:3000/api/approvals/batch-sync
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "processInstanceIds": [
    "process-instance-001",
    "process-instance-002",
    "process-instance-003"
  ]
}

### ========== 用户管理 API ==========

### 1. 同步指定用户信息
POST http://localhost:3000/api/users/sync
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "userIds": ["user001", "user002", "user003"],
  "force": false
}

### 2. 同步所有用户信息
POST http://localhost:3000/api/users/sync-all
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "force": false
}

### 3. 批量获取用户信息
POST http://localhost:3000/api/users/batch
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "userIds": ["user001", "user002", "user003"]
}

### 4. 获取单个用户信息
GET http://localhost:3000/api/users/user001
Authorization: Bearer your-jwt-token

### 5. 获取本地用户列表（分页）
GET http://localhost:3000/api/users?page=1&pageSize=20&keyword=张三&isActive=true
Authorization: Bearer your-jwt-token

### 5.1 获取本地用户列表（查看完整字段）
GET http://localhost:3000/api/users?page=1&pageSize=5
Authorization: Bearer your-jwt-token

### 6. 获取用户同步状态统计
GET http://localhost:3000/api/users/sync/stats
Authorization: Bearer your-jwt-token

### 8. 审批回调接口（钉钉回调）
POST http://localhost:3000/api/approvals/callback
Content-Type: application/json

{
  "EventType": "bpms_instance_change",
  "processInstanceId": "process-instance-001",
  "processCode": "PROC-PAYMENT-001",
  "corpId": "your-corp-id",
  "createTime": 1640995200000,
  "title": "对公付款申请",
  "type": "finish",
  "result": "agree",
  "staffId": "user-001"
}

### 9. 获取周预算列表（查看审批状态）
GET http://localhost:3000/api/weekly-budgets?page=1&pageSize=20&approvalStatus=PENDING
Authorization: Bearer your-jwt-token

### 10. 获取单个周预算（查看审批信息）
GET http://localhost:3000/api/weekly-budgets/budget-001
Authorization: Bearer your-jwt-token

### 11. 上传发票文件
POST http://localhost:3000/api/approvals/upload/invoice
Content-Type: multipart/form-data
Authorization: Bearer your-jwt-token

# 在实际使用中，这里需要上传文件
# file=@/path/to/invoice.pdf

### 12. 上传附件文件
POST http://localhost:3000/api/approvals/upload/attachment
Content-Type: multipart/form-data
Authorization: Bearer your-jwt-token

# 在实际使用中，这里需要上传文件
# file=@/path/to/contract.pdf

### 13. 完整的审批流程示例（包含文件上传后的URL）
POST http://localhost:3000/api/weekly-budgets/approval
Content-Type: application/json
Authorization: Bearer your-jwt-token

{
  "weeklyBudgetId": "budget-001",
  "totalAmount": 15000,
  "paymentReason": "项目第二期付款",
  "contractEntity": "company_a",
  "expectedPaymentDate": "2024-01-20",
  "paymentMethod": "bank_transfer",
  "receivingAccount": {
    "accountName": "北京某某科技有限公司",
    "accountNumber": "6228480402564890018",
    "bankName": "中国农业银行北京分行",
    "bankCode": "************"
  },
  "relatedApprovalId": "REL-2024-002",
  "invoiceFiles": [
    "/uploads/invoice/1705123456789_invoice_001.pdf",
    "/uploads/invoice/1705123456790_invoice_002.pdf"
  ],
  "attachments": [
    "/uploads/attachment/1705123456791_contract.pdf",
    "/uploads/attachment/1705123456792_service_agreement.pdf"
  ],
  "remark": "第二期付款，包含服务费和税费，请财务部门审核后尽快处理"
}
