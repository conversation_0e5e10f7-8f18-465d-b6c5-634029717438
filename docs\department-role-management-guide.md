# 部门角色管理功能指南

## 概述

部门角色管理功能允许您将角色直接分配给部门，部门下的所有用户将自动继承这些角色权限。这大大简化了权限管理，避免了逐个用户分配角色的繁琐操作。

## 核心功能

### 1. 部门角色分配
- 为部门批量分配角色
- 部门成员自动继承角色权限
- 支持角色过期时间设置

### 2. 权限继承机制
- 用户权限 = 直接分配的角色权限 + 部门继承的角色权限
- 部门角色变更实时生效
- 支持多部门用户的权限合并

### 3. 角色管理增强
- 查看角色关联的部门列表
- 查看角色关联的用户列表（直接+继承）
- 角色使用情况统计

## API接口

### 部门角色管理

#### 1. 获取部门角色
```http
GET /api/departments/:deptId/roles
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "deptId": 123,
    "roles": [
      {
        "id": "role_001",
        "name": "project_manager",
        "displayName": "项目经理"
      }
    ],
    "total": 1
  },
  "message": "获取部门角色成功"
}
```

#### 2. 为部门分配角色
```http
POST /api/departments/:deptId/roles
Content-Type: application/json

{
  "roleIds": ["role_001", "role_002"],
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

#### 3. 移除部门特定角色
```http
DELETE /api/departments/:deptId/roles/:roleId
```

#### 4. 清空部门所有角色
```http
DELETE /api/departments/:deptId/roles
```

### 角色管理增强

#### 1. 获取角色关联的部门列表
```http
GET /api/roles/:roleId/departments
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "roleId": "role_001",
    "departments": [
      {
        "deptId": 123,
        "name": "技术部",
        "parentId": 1,
        "assignedAt": "2024-01-01T00:00:00Z",
        "assignedBy": "admin",
        "expiresAt": null
      }
    ],
    "total": 1
  }
}
```

#### 2. 获取角色关联的用户列表
```http
GET /api/roles/:roleId/users?includeInherited=true&page=1&pageSize=50
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "roleId": "role_001",
    "directUsers": [
      {
        "userid": "user001",
        "name": "张三",
        "avatar": "...",
        "assignedAt": "2024-01-01T00:00:00Z",
        "assignedBy": "admin"
      }
    ],
    "inheritedUsers": [
      {
        "userid": "user002",
        "name": "李四",
        "avatar": "...",
        "deptId": 123,
        "deptName": "技术部"
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 50
  }
}
```

## 使用场景

### 场景1：新部门权限配置
```javascript
// 1. 为技术部分配项目管理相关角色
await fetch('/api/departments/123/roles', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-jwt-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    roleIds: ['project_manager', 'developer', 'code_reviewer']
  })
});

// 2. 技术部所有成员自动获得这些角色权限
```

### 场景2：权限调整
```javascript
// 移除技术部的某个角色
await fetch('/api/departments/123/roles/code_reviewer', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});
```

### 场景3：角色使用情况分析
```javascript
// 查看项目经理角色被哪些部门使用
const response = await fetch('/api/roles/project_manager/departments', {
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});

// 查看项目经理角色有哪些用户
const usersResponse = await fetch('/api/roles/project_manager/users?includeInherited=true', {
  headers: {
    'Authorization': 'Bearer your-jwt-token'
  }
});
```

## 权限验证

所有部门角色管理操作都需要相应的权限：

- `ROLE_READ` - 查看角色和部门角色信息
- `ROLE_ASSIGN` - 分配、移除部门角色

## 最佳实践

### 1. 角色设计原则
- 按职能设计角色（如：项目经理、开发者、测试员）
- 避免过于细粒度的角色划分
- 考虑角色的可复用性

### 2. 部门角色分配策略
- 优先使用部门角色分配，减少个人角色分配
- 定期审查部门角色配置
- 为临时权限设置过期时间

### 3. 权限管理流程
```
1. 创建角色并分配权限
2. 将角色分配给相关部门
3. 部门成员自动继承权限
4. 定期审查和调整
```

## 注意事项

1. **权限继承优先级**：用户最终权限 = 直接角色权限 ∪ 部门继承权限
2. **角色删除限制**：正在被部门使用的角色无法删除
3. **实时生效**：部门角色变更对部门成员立即生效
4. **多部门用户**：用户属于多个部门时，会继承所有部门的角色权限

## 故障排除

### 常见问题

1. **用户权限未生效**
   - 检查用户是否属于相关部门
   - 确认角色是否处于激活状态
   - 验证角色权限配置

2. **无法删除角色**
   - 检查角色是否被部门使用
   - 先移除部门角色关联再删除角色

3. **权限过多或过少**
   - 检查用户的部门归属
   - 审查部门角色配置
   - 确认直接分配的角色

通过部门角色管理功能，您可以更高效地管理企业权限体系，确保权限分配的准确性和一致性。
