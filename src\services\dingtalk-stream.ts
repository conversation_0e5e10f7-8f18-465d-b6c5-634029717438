/**
 * 钉钉Stream推送服务
 * 实现钉钉Stream模式的实时消息推送和回调处理
 * 
 * 参考文档: https://open.dingtalk.com/document/orgapp/configure-stream-push
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import { ApprovalService } from './approval.js';
import { DingTalkService } from './dingtalk.js';

export interface StreamMessage {
  specVersion: string;
  type: string;
  source: string;
  id: string;
  time: string;
  subject: string;
  data: any;
}

export interface StreamConnectionInfo {
  endpoint: string;
  ticket: string;
}

export class DingTalkStreamService extends EventEmitter {
  private dingTalkService: DingTalkService;
  private approvalService: ApprovalService;
  private ws: WebSocket | null = null;
  private connectionInfo: StreamConnectionInfo | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000; // 5秒
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isConnected = false;

  constructor() {
    super();
    this.dingTalkService = new DingTalkService();
    this.approvalService = new ApprovalService();
  }

  /**
   * 启动Stream连接
   */
  async start(): Promise<void> {
    try {
      console.log('🚀 启动钉钉Stream推送服务...');
      
      // 获取Stream连接信息
      this.connectionInfo = await this.getStreamConnectionInfo();
      
      if (!this.connectionInfo) {
        throw new Error('无法获取Stream连接信息');
      }

      // 建立WebSocket连接
      await this.connect();
      
      console.log('✅ 钉钉Stream推送服务启动成功');
    } catch (error) {
      console.error('❌ 启动钉钉Stream推送服务失败:', error);
      throw error;
    }
  }

  /**
   * 停止Stream连接
   */
  async stop(): Promise<void> {
    console.log('🛑 停止钉钉Stream推送服务...');
    
    this.isConnected = false;
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    console.log('✅ 钉钉Stream推送服务已停止');
  }

  /**
   * 获取Stream连接信息
   */
  private async getStreamConnectionInfo(): Promise<StreamConnectionInfo> {
    try {
      const accessToken = await this.dingTalkService.getAccessToken();
      
      const response = await fetch('https://api.dingtalk.com/v1.0/gateway/connections/open', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-acs-dingtalk-access-token': accessToken
        },
        body: JSON.stringify({
          clientId: process.env.DINGTALK_APP_KEY,
          clientSecret: process.env.DINGTALK_APP_SECRET,
          ua: 'DingTalkStream/1.0.0',
          subscriptions: [
            {
              type: 'CALLBACK',
              topic: '*' // 订阅所有回调事件
            }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`获取Stream连接信息失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.endpoint || !data.ticket) {
        throw new Error('Stream连接信息格式错误');
      }

      console.log('📡 获取Stream连接信息成功:', {
        endpoint: data.endpoint,
        ticket: data.ticket.substring(0, 20) + '...'
      });

      return {
        endpoint: data.endpoint,
        ticket: data.ticket
      };
    } catch (error) {
      console.error('❌ 获取Stream连接信息失败:', error);
      throw error;
    }
  }

  /**
   * 建立WebSocket连接
   */
  private async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.connectionInfo) {
        reject(new Error('缺少连接信息'));
        return;
      }

      const wsUrl = `${this.connectionInfo.endpoint}?ticket=${this.connectionInfo.ticket}`;
      console.log('🔗 建立WebSocket连接:', wsUrl.substring(0, 100) + '...');

      this.ws = new WebSocket(wsUrl);

      this.ws.on('open', () => {
        console.log('✅ WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.startHeartbeat();
        resolve();
      });

      this.ws.on('message', (data: WebSocket.Data) => {
        try {
          const rawMessage = data.toString();
          const parsedMessage = JSON.parse(rawMessage);

          // 检查消息格式并补充缺失字段
          const message: StreamMessage = {
            specVersion: parsedMessage.specVersion || '1.0',
            type: parsedMessage.type || 'SYSTEM',
            source: parsedMessage.source || 'dingtalk',
            id: parsedMessage.id || Date.now().toString(),
            time: parsedMessage.time || new Date().toISOString(),
            subject: parsedMessage.subject || 'connection',
            data: parsedMessage.data || parsedMessage
          };

          this.handleStreamMessage(message);
        } catch (error) {
          console.error('❌ 解析Stream消息失败:', error);
          console.error('原始消息内容:', data.toString().substring(0, 200));
        }
      });

      this.ws.on('close', (code: number, reason: Buffer) => {
        console.log(`🔌 WebSocket连接已关闭: ${code} ${reason.toString()}`);
        this.isConnected = false;
        
        if (this.heartbeatInterval) {
          clearInterval(this.heartbeatInterval);
          this.heartbeatInterval = null;
        }

        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        } else {
          console.error('❌ 达到最大重连次数，停止重连');
          this.emit('error', new Error('WebSocket连接失败，达到最大重连次数'));
        }
      });

      this.ws.on('error', (error: Error) => {
        console.error('❌ WebSocket连接错误:', error);
        reject(error);
      });

      // 连接超时处理
      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error('WebSocket连接超时'));
        }
      }, 10000); // 10秒超时
    });
  }

  /**
   * 处理Stream消息
   */
  private async handleStreamMessage(message: StreamMessage): Promise<void> {
    try {
      // 安全地获取消息字段，处理可能的undefined值
      const messageInfo = {
        type: message.type || 'UNKNOWN',
        source: message.source || 'unknown',
        subject: message.subject || 'unknown',
        id: message.id || 'unknown'
      };

      console.log('📨 收到Stream消息:', messageInfo);

      // 只有当消息ID存在时才发送ACK确认
      if (message.id) {
        this.sendAck(message.id);
      } else {
        console.warn('⚠️ 消息缺少ID，跳过ACK确认');
      }

      // 根据消息类型处理
      switch (message.type) {
        case 'SYSTEM':
          await this.handleSystemMessage(message);
          break;
        case 'CALLBACK':
          await this.handleCallbackMessage(message);
          break;
        default:
          console.log(`❓ 未处理的消息类型: ${message.type || 'undefined'}`);
      }
    } catch (error) {
      console.error('❌ 处理Stream消息失败:', error);
    }
  }

  /**
   * 处理系统消息
   */
  private async handleSystemMessage(message: StreamMessage): Promise<void> {
    const subject = message.subject || 'unknown';
    console.log('🔧 处理系统消息:', subject);

    switch (subject) {
      case 'ping':
        // 心跳消息，发送pong响应
        console.log('💓 收到ping消息，发送pong响应');
        this.sendPong();
        break;
      case 'unknown':
        console.log('⚠️ 收到未知系统消息，可能是连接建立消息');
        // 这可能是连接建立时的初始消息，不需要特殊处理
        break;
      default:
        console.log(`❓ 未处理的系统消息: ${subject}`);
    }
  }

  /**
   * 处理回调消息
   */
  private async handleCallbackMessage(message: StreamMessage): Promise<void> {
    try {
      const eventType = message.data?.EventType;
      console.log('📋 处理回调消息:', eventType);

      switch (eventType) {
        case 'bpms_instance_change':
          // 审批实例状态变更
          await this.handleApprovalChange(message.data);
          break;
        case 'user_add_org':
        case 'user_modify_org':
        case 'user_leave_org':
          // 用户相关事件
          await this.handleUserEvent(message.data);
          break;
        case 'org_dept_create':
        case 'org_dept_modify':
        case 'org_dept_remove':
          // 部门相关事件
          await this.handleDepartmentEvent(message.data);
          break;
        default:
          console.log(`❓ 未处理的回调事件: ${eventType}`);
      }

      // 触发事件，供外部监听
      this.emit('callback', {
        type: eventType,
        data: message.data,
        messageId: message.id
      });
    } catch (error) {
      console.error('❌ 处理回调消息失败:', error);
    }
  }

  /**
   * 处理审批状态变更
   */
  private async handleApprovalChange(data: any): Promise<void> {
    try {
      console.log('📋 处理审批状态变更:', {
        processInstanceId: data.processInstanceId,
        result: data.result,
        type: data.type
      });

      // 调用审批服务处理
      const result = await this.approvalService.handleApprovalStatusChange({
        processInstanceId: data.processInstanceId,
        result: data.result,
        type: data.type,
        staffId: data.staffId,
        createTime: data.createTime,
        finishTime: data.finishTime,
        corpId: data.corpId
      });

      console.log('✅ 审批状态变更处理完成:', result);
    } catch (error) {
      console.error('❌ 处理审批状态变更失败:', error);
    }
  }

  /**
   * 处理用户事件
   */
  private async handleUserEvent(data: any): Promise<void> {
    console.log('👤 处理用户事件:', data);
    // TODO: 实现用户事件处理逻辑
  }

  /**
   * 处理部门事件
   */
  private async handleDepartmentEvent(data: any): Promise<void> {
    console.log('🏢 处理部门事件:', data);
    // TODO: 实现部门事件处理逻辑
  }

  /**
   * 发送ACK确认
   */
  private sendAck(messageId: string): void {
    if (!messageId || messageId === 'unknown') {
      console.log('⚠️ 跳过ACK确认，消息ID无效:', messageId);
      return;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const ackMessage = {
        code: 200,
        headers: {
          contentType: 'application/json'
        },
        message: 'OK',
        data: JSON.stringify({ success: true })
      };

      try {
        this.ws.send(JSON.stringify(ackMessage));
        console.log('📤 发送ACK确认:', messageId);
      } catch (error) {
        console.error('❌ 发送ACK确认失败:', error);
      }
    } else {
      console.warn('⚠️ WebSocket未连接，无法发送ACK确认');
    }
  }

  /**
   * 发送Pong响应
   */
  private sendPong(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const pongMessage = {
        type: 'SYSTEM',
        subject: 'pong'
      };

      this.ws.send(JSON.stringify(pongMessage));
      console.log('🏓 发送Pong响应');
    }
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        const pingMessage = {
          type: 'SYSTEM',
          subject: 'ping'
        };
        this.ws.send(JSON.stringify(pingMessage));
        console.log('💓 发送心跳');
      }
    }, 30000); // 30秒心跳间隔
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;
    
    console.log(`🔄 ${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`);
    
    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('❌ 重连失败:', error);
      }
    }, delay);
  }

  /**
   * 获取连接状态
   */
  public getConnectionStatus(): {
    isConnected: boolean;
    reconnectAttempts: number;
    endpoint?: string;
  } {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      endpoint: this.connectionInfo?.endpoint
    };
  }
}
