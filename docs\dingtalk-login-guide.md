# 钉钉免登录完整实现指南

## 概述

本项目实现了完整的钉钉H5微应用免登录功能，包括前端JSAPI集成和后端API支持。

## 功能特性

- ✅ 完整的钉钉免登录流程
- ✅ JSAPI签名生成和验证
- ✅ 用户信息获取
- ✅ 部门和用户管理
- ✅ 工作通知发送
- ✅ 详细的调试信息
- ✅ 错误处理和降级方案

## 文件结构

```
public/
├── dingtalk-login-demo.html     # 钉钉免登录演示页面
├── dingtalk-design-demo.html    # 完整功能演示页面
├── test-api.html                # API测试页面
└── index.html                   # 基础演示页面

src/
├── controllers/
│   ├── auth.ts                  # 认证控制器
│   └── app.ts                   # 应用控制器
├── services/
│   └── dingtalk.ts              # 钉钉服务类
├── routes/
│   ├── auth.ts                  # 认证路由
│   └── app.ts                   # 应用路由
└── types/
    └── dingtalk.ts              # 类型定义
```

## 快速开始

### 1. 环境配置

在 `.env` 文件中配置钉钉应用信息：

```env
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_CORP_ID=your_corp_id
DINGTALK_AGENT_ID=your_agent_id
```

### 2. 启动服务

```bash
npm install
npm run dev
```

### 3. 访问演示页面

- 免登录演示：`http://localhost:3000/dingtalk-login-demo.html`
- 完整功能演示：`http://localhost:3000/dingtalk-design-demo.html`
- API测试：`http://localhost:3000/test-api.html`

## 免登录流程

### 前端流程

1. **环境检测**：检查是否在钉钉客户端中
2. **获取配置**：从服务端获取应用配置和JSAPI签名
3. **初始化JSAPI**：使用配置信息初始化钉钉JSAPI
4. **免登录认证**：获取免登授权码并验证用户身份

### 后端流程

1. **获取访问令牌**：使用AppKey和AppSecret获取access_token
2. **生成JSAPI签名**：使用jsapi_ticket生成前端所需的签名
3. **验证免登码**：通过免登码获取用户ID
4. **获取用户信息**：使用用户ID获取详细用户信息

## API接口

### 认证相关

- `GET /api/health` - 健康检查
- `POST /api/auth/user-info` - 通过免登码获取用户信息
- `GET /api/auth/jsapi-signature` - 获取JSAPI签名
- `GET /api/auth/departments` - 获取部门列表

### 应用相关

- `GET /api/app/config` - 获取应用配置
- `GET /api/app/department/users` - 获取部门用户
- `POST /api/app/notification/send` - 发送工作通知
- `GET /api/app/jsapi-signature/enhanced` - 获取增强版JSAPI签名
- `GET /api/app/user/permissions` - 获取用户权限
- `GET /api/app/stats` - 获取应用统计

## 前端集成示例

### 基础免登录

```javascript
// 1. 检查钉钉环境
if (typeof dd !== 'undefined') {
    // 2. 获取应用配置
    const config = await fetch('/api/app/config').then(r => r.json());
    
    // 3. 获取JSAPI签名
    const signature = await fetch(`/api/auth/jsapi-signature?url=${encodeURIComponent(location.href)}`)
        .then(r => r.json());
    
    // 4. 初始化JSAPI
    dd.config({
        agentId: config.data.agentId,
        corpId: config.data.corpId,
        timeStamp: signature.data.timeStamp,
        nonceStr: signature.data.nonceStr,
        signature: signature.data.signature,
        jsApiList: config.data.jsApiList
    });
    
    // 5. 免登录认证
    dd.ready(() => {
        dd.runtime.permission.requestAuthCode({
            redirection: "none",
            onSuccess: async (result) => {
                const userInfo = await fetch('/api/auth/user-info', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ authCode: result.code })
                }).then(r => r.json());
                
                console.log('用户信息:', userInfo.data);
            },
            onFail: (err) => {
                console.error('免登录失败:', err);
            }
        });
    });
}
```

## 调试和测试

### 1. 使用调试工具

项目集成了钉钉官方的H5调试工具，可以在PC端模拟钉钉环境。

### 2. 查看调试信息

在 `dingtalk-login-demo.html` 中点击"显示调试信息"按钮可以查看详细的执行日志。

### 3. API测试

访问 `test-api.html` 可以单独测试各个API接口的功能。

## 常见问题

### 1. JSAPI签名错误

- 确保URL编码正确
- 检查jsapi_ticket是否有效
- 验证签名算法参数顺序

### 2. 免登码获取失败

- 确认应用权限配置
- 检查JSAPI初始化是否成功
- 验证应用是否在钉钉中正确配置

### 3. 用户信息获取失败

- 检查access_token是否有效
- 确认用户在企业中的状态
- 验证API权限配置

## 部署注意事项

1. **HTTPS要求**：钉钉要求所有H5微应用必须使用HTTPS
2. **域名白名单**：在钉钉开放平台配置可信域名
3. **IP白名单**：配置服务器出口IP白名单
4. **环境变量**：确保生产环境正确配置钉钉应用信息

## 技术栈

- **前端**：原生JavaScript + 钉钉JSAPI
- **后端**：Node.js + Fastify + TypeScript
- **HTTP客户端**：Axios
- **加密**：Node.js crypto模块
- **验证**：Zod

## 参考文档

- [钉钉开放平台](https://open.dingtalk.com/)
- [H5微应用开发指南](https://open.dingtalk.com/document/orgapp/h5-micro-applications)
- [免登录流程](https://open.dingtalk.com/document/orgapp/logon-free-process)
- [JSAPI文档](https://open.dingtalk.com/document/orgapp/jsapi-overview)
