# API使用示例 - 钉钉对公付款审批完整流程

## 🎯 完整的审批流程示例

以下是一个完整的钉钉对公付款审批流程，从用户登录到审批完成的全过程。

### 步骤1: 用户登录认证

```bash
# 1. 钉钉免登登录
curl -X POST http://localhost:3000/api/auth/dingtalk \
  -H "Content-Type: application/json" \
  -d '{
    "authCode": "your_dingtalk_auth_code",
    "corpId": "your_corp_id"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "userid": "user123",
      "name": "张三",
      "avatar": "https://example.com/avatar.jpg",
      "mobile": "13800138000",
      "deptIds": [1, 2],
      "isAdmin": false
    }
  },
  "message": "登录成功"
}
```

### 步骤2: 查看周预算列表

```bash
# 2. 获取当前用户的周预算列表
curl -X GET "http://localhost:3000/api/weekly-budgets?page=1&pageSize=10&approvalStatus=none" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "weeklyBudgets": [
      {
        "id": "budget-001",
        "title": "第1周达人投放预算",
        "weekStartDate": "2024-01-01",
        "weekEndDate": "2024-01-07",
        "contractAmount": 50000,
        "paidAmount": 0,
        "remainingAmount": 50000,
        "status": "approved",
        "approvalStatus": "none",
        "projectId": "project-001",
        "supplierId": "supplier-001"
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10
  }
}
```

### 步骤3: 上传发票文件

```bash
# 3. 上传发票文件
curl -X POST http://localhost:3000/api/approvals/upload/invoice \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@invoice_001.pdf"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "filename": "invoice_001.pdf",
        "url": "/uploads/invoice/1705123456789_invoice_001.pdf",
        "size": 1024000
      }
    ]
  },
  "message": "发票文件上传成功"
}
```

### 步骤4: 上传附件文件

```bash
# 4. 上传合同附件
curl -X POST http://localhost:3000/api/approvals/upload/attachment \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -F "file=@contract.pdf"
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "filename": "contract.pdf",
        "url": "/uploads/attachment/1705123456791_contract.pdf",
        "size": 2048000
      }
    ]
  },
  "message": "附件文件上传成功"
}
```

### 步骤5: 发起对公付款审批

```bash
# 5. 发起审批申请
curl -X POST http://localhost:3000/api/weekly-budgets/approval \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "weeklyBudgetId": "budget-001",
    "totalAmount": 25000,
    "paymentReason": "项目第一期执行付款",
    "contractEntity": "company_a",
    "expectedPaymentDate": "2024-01-15",
    "paymentMethod": "bank_transfer",
    "receivingAccount": {
      "accountName": "北京某某科技有限公司",
      "accountNumber": "6228480402564890018",
      "bankName": "中国农业银行北京分行",
      "bankCode": "************"
    },
    "relatedApprovalId": "REL-2024-001",
    "invoiceFiles": [
      "/uploads/invoice/1705123456789_invoice_001.pdf"
    ],
    "attachments": [
      "/uploads/attachment/1705123456791_contract.pdf"
    ],
    "remark": "第一期付款，请财务部门审核后尽快处理"
  }'
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "approval-instance-001",
    "processInstanceId": "dingtalk-process-123456",
    "title": "项目 - 第1周达人投放预算 - 对公付款申请",
    "status": "PENDING",
    "approvalAmount": 25000,
    "weeklyBudgetId": "budget-001",
    "createdAt": "2024-01-01T10:00:00.000Z"
  },
  "message": "发起审批成功"
}
```

### 步骤6: 查询审批状态

```bash
# 6. 查询审批实例状态
curl -X GET http://localhost:3000/api/approvals/approval-instance-001 \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "approval-instance-001",
    "processInstanceId": "dingtalk-process-123456",
    "processCode": "PROC-PAYMENT-001",
    "title": "项目 - 第1周达人投放预算 - 对公付款申请",
    "originatorUserId": "user123",
    "status": "PENDING",
    "result": null,
    "createTime": "2024-01-01T10:00:00.000Z",
    "finishTime": null,
    "approvalAmount": 25000,
    "reason": "项目第一期执行付款",
    "remark": "第一期付款，请财务部门审核后尽快处理",
    "weeklyBudget": {
      "id": "budget-001",
      "title": "第1周达人投放预算",
      "project": {
        "id": "project-001",
        "name": "品牌推广项目"
      }
    }
  },
  "message": "获取审批详情成功"
}
```

### 步骤7: 同步审批状态（可选）

```bash
# 7. 主动同步钉钉审批状态
curl -X POST http://localhost:3000/api/approvals/sync/dingtalk-process-123456 \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "approval-instance-001",
    "status": "APPROVED",
    "result": "agree",
    "finishTime": "2024-01-01T14:30:00.000Z"
  },
  "message": "同步审批状态成功"
}
```

### 步骤8: 查看更新后的周预算

```bash
# 8. 查看审批通过后的周预算状态
curl -X GET http://localhost:3000/api/weekly-budgets/budget-001 \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "budget-001",
    "title": "第1周达人投放预算",
    "contractAmount": 50000,
    "paidAmount": 25000,        // 审批通过后自动更新
    "remainingAmount": 25000,   // 自动计算剩余金额
    "status": "executing",
    "approvalStatus": "approved", // 审批状态已更新
    "approvalAmount": 25000,
    "approvalReason": "项目第一期执行付款"
  },
  "message": "获取周预算成功"
}
```

## 📊 审批统计查询

```bash
# 获取审批统计信息
curl -X GET "http://localhost:3000/api/approvals/stats?startDate=2024-01-01&endDate=2024-01-31" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total": 15,
    "pending": 3,
    "approved": 10,
    "rejected": 1,
    "cancelled": 1,
    "totalAmount": 500000,
    "approvedAmount": 350000,
    "pendingAmount": 75000
  },
  "message": "获取审批统计成功"
}
```

## 🔄 钉钉回调处理

当钉钉审批状态发生变化时，系统会自动接收回调：

```json
POST /api/approvals/callback
{
  "EventType": "bpms_instance_change",
  "processInstanceId": "dingtalk-process-123456",
  "processCode": "PROC-PAYMENT-001",
  "corpId": "your_corp_id",
  "createTime": 1640995200000,
  "title": "对公付款申请",
  "type": "finish",
  "result": "agree",
  "staffId": "user123"
}
```

系统会自动：
1. 更新审批实例状态
2. 如果审批通过，更新周预算的已付金额
3. 更新周预算的审批状态

## 💡 最佳实践

1. **文件上传顺序**: 先上传文件，再发起审批
2. **错误处理**: 检查每个步骤的响应状态
3. **状态同步**: 定期同步审批状态或依赖钉钉回调
4. **权限验证**: 确保用户有相应的操作权限
5. **数据验证**: 发起审批前验证必填字段

这个完整的流程展示了从用户登录到审批完成的全过程，包括文件上传、审批发起、状态跟踪等所有环节。
