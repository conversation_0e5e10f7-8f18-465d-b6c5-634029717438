# Docker 构建问题解决指南

## 🚨 当前问题分析

### 问题1: `pnpm install --frozen-lockfile` 失败
**原因**: `package.json` 和 `pnpm-lock.yaml` 不同步

### 问题2: `tsc: not found` 
**原因**: TypeScript 编译器不在 PATH 中，通常因为只安装了生产依赖

## 🔧 解决方案

### 方案1: 使用修复脚本（推荐）

```powershell
# 基本修复
.\fix-docker-build.ps1

# 使用简化版 Dockerfile
.\fix-docker-build.ps1 -UseSimple

# 清理所有缓存重新构建
.\fix-docker-build.ps1 -CleanAll

# 构建特定标签
.\fix-docker-build.ps1 -Tag v1.0.0
```

### 方案2: 手动修复步骤

#### 步骤1: 检查 Docker 环境
```powershell
# 检查 Docker 是否运行
docker version

# 检查可用空间
docker system df
```

#### 步骤2: 清理环境（如果需要）
```powershell
# 清理构建缓存
docker builder prune -f

# 清理未使用的镜像
docker image prune -f

# 完全清理（谨慎使用）
docker system prune -a -f
```

#### 步骤3: 使用简化版 Dockerfile
```powershell
# 使用简化版构建
docker build -f Dockerfile.simple -t cantv-ding-backend:latest .
```

### 方案3: 修复依赖问题

#### 本地修复 pnpm 依赖
```powershell
# 删除 node_modules（如果存在权限问题）
Remove-Item -Recurse -Force node_modules -ErrorAction SilentlyContinue

# 删除 lock 文件
Remove-Item pnpm-lock.yaml -ErrorAction SilentlyContinue

# 重新安装
pnpm install

# 验证构建脚本
pnpm run build
```

## 📋 Dockerfile 对比

### 原始版本问题
- 使用 `--frozen-lockfile` 过于严格
- 构建阶段可能缺少 devDependencies
- 复杂的错误处理可能导致问题

### 简化版本优势
- 移除 `--frozen-lockfile` 限制
- 明确安装所有依赖
- 简化的构建流程
- 更好的错误处理

## 🔍 问题诊断

### 检查1: 文件完整性
```powershell
# 检查必要文件
@("package.json", "tsconfig.json", "start.sh", ".env.prod") | ForEach-Object {
    if (Test-Path $_) { 
        Write-Host "✅ $_" -ForegroundColor Green 
    } else { 
        Write-Host "❌ $_" -ForegroundColor Red 
    }
}
```

### 检查2: 依赖完整性
```powershell
# 检查 TypeScript 是否在依赖中
Get-Content package.json | ConvertFrom-Json | Select-Object -ExpandProperty devDependencies | Select-Object typescript

# 检查构建脚本
Get-Content package.json | ConvertFrom-Json | Select-Object -ExpandProperty scripts | Select-Object build
```

### 检查3: Docker 资源
```powershell
# 检查 Docker 内存使用
docker system df

# 检查运行中的容器
docker ps -a
```

## 🚀 快速测试

### 测试1: 本地构建
```powershell
# 测试 TypeScript 编译
pnpm run build

# 测试 Prisma 生成
pnpm db:generate
```

### 测试2: Docker 构建
```powershell
# 测试简化版构建
docker build -f Dockerfile.simple -t test-build .

# 测试运行
docker run --rm -p 3000:3000 test-build
```

## 💡 最佳实践

### 1. 依赖管理
- 确保 `typescript` 在 `devDependencies` 中
- 定期更新 `pnpm-lock.yaml`
- 使用 `pnpm install` 而不是 `--frozen-lockfile`

### 2. Docker 优化
- 使用多阶段构建分离构建和运行环境
- 构建阶段安装所有依赖
- 运行阶段只安装生产依赖

### 3. 错误处理
- 添加适当的错误信息
- 提供回退机制
- 使用健康检查

## 🔧 常用命令

### 构建相关
```powershell
# 标准构建
docker build -t cantv-ding-backend:latest .

# 使用简化版
docker build -f Dockerfile.simple -t cantv-ding-backend:latest .

# 不使用缓存
docker build --no-cache -t cantv-ding-backend:latest .

# 查看构建过程
docker build --progress=plain -t cantv-ding-backend:latest .
```

### 调试相关
```powershell
# 进入构建阶段调试
docker build --target builder -t debug-builder .
docker run --rm -it debug-builder sh

# 查看镜像层
docker history cantv-ding-backend:latest

# 检查镜像内容
docker run --rm -it cantv-ding-backend:latest sh
```

### 清理相关
```powershell
# 清理构建缓存
docker builder prune -f

# 清理未使用镜像
docker image prune -f

# 清理所有
docker system prune -a -f
```

## 📞 获取帮助

如果问题仍然存在：

1. **使用修复脚本**: `.\fix-docker-build.ps1 -UseSimple`
2. **检查错误日志**: 完整的构建输出
3. **验证环境**: Docker 版本、可用资源
4. **简化测试**: 使用最小化的 Dockerfile

记住：大多数构建问题都与依赖管理相关，使用简化版 Dockerfile 通常能解决问题！🎯
