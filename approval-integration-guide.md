# 钉钉对公付款审批完整集成指南

## 🎯 功能概述

现在钉钉对公付款审批功能已经完全集成了用户认证和文件上传功能，提供了完整的端到端审批流程。

## ✅ 已集成的功能

### 1. 用户认证集成
- ✅ JWT认证中间件
- ✅ 自动获取当前用户信息
- ✅ 用户权限验证
- ✅ 部门信息获取

### 2. 文件上传集成
- ✅ 发票文件上传
- ✅ 附件文件上传
- ✅ 文件类型验证
- ✅ 文件大小限制

### 3. 钉钉API集成
- ✅ 钉钉审批实例创建
- ✅ 审批状态同步
- ✅ 审批回调处理

## 🔄 完整的审批流程

### 步骤1: 用户登录认证
```javascript
// 1. 钉钉免登登录
POST /api/auth/login
{
  "authCode": "dingtalk_auth_code"
}

// 响应
{
  "success": true,
  "data": {
    "accessToken": "jwt_token",
    "user": {
      "userid": "user123",
      "name": "张三",
      "mobile": "13800138000",
      "deptIds": [1, 2],
      "isAdmin": false
    }
  }
}
```

### 步骤2: 上传发票和附件文件
```javascript
// 2.1 上传发票文件
POST /api/approvals/upload/invoice
Content-Type: multipart/form-data
Authorization: Bearer jwt_token

// 2.2 上传附件文件
POST /api/approvals/upload/attachment
Content-Type: multipart/form-data
Authorization: Bearer jwt_token

// 响应
{
  "success": true,
  "data": {
    "files": [
      {
        "filename": "invoice_001.pdf",
        "url": "/uploads/invoice/1705123456789_invoice_001.pdf",
        "size": 1024000
      }
    ]
  }
}
```

### 步骤3: 发起对公付款审批
```javascript
POST /api/weekly-budgets/approval
Authorization: Bearer jwt_token
{
  "weeklyBudgetId": "budget-001",
  "totalAmount": 10000,
  "paymentReason": "项目执行付款",
  "contractEntity": "company_a",
  "expectedPaymentDate": "2024-01-15",
  "paymentMethod": "bank_transfer",
  "receivingAccount": {
    "accountName": "供应商公司名称",
    "accountNumber": "6228480402564890018",
    "bankName": "中国农业银行",
    "bankCode": "103"
  },
  "invoiceFiles": [
    "/uploads/invoice/1705123456789_invoice_001.pdf"
  ],
  "attachments": [
    "/uploads/attachment/1705123456791_contract.pdf"
  ],
  "remark": "第一期付款，请尽快处理"
}
```

### 步骤4: 系统自动处理
1. **验证用户权限** - 确保用户已认证
2. **获取用户信息** - 从钉钉API获取申请人详细信息
3. **构建审批表单** - 自动填充申请人、部门等信息
4. **发起钉钉审批** - 调用钉钉API创建审批实例
5. **记录审批状态** - 在数据库中创建审批记录
6. **更新周预算** - 更新周预算的审批状态

### 步骤5: 审批状态跟踪
```javascript
// 5.1 查询审批状态
GET /api/approvals?weeklyBudgetId=budget-001
Authorization: Bearer jwt_token

// 5.2 同步钉钉审批状态
POST /api/approvals/sync/process-instance-001
Authorization: Bearer jwt_token

// 5.3 获取审批统计
GET /api/approvals/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer jwt_token
```

## 📋 实际使用示例

### 前端集成示例
```javascript
class ApprovalManager {
  constructor(apiClient) {
    this.api = apiClient;
  }

  // 完整的审批流程
  async submitPaymentApproval(approvalData, invoiceFiles, attachmentFiles) {
    try {
      // 1. 上传发票文件
      const invoiceUrls = [];
      for (const file of invoiceFiles) {
        const result = await this.uploadFile(file, 'invoice');
        invoiceUrls.push(...result.files.map(f => f.url));
      }

      // 2. 上传附件文件
      const attachmentUrls = [];
      for (const file of attachmentFiles) {
        const result = await this.uploadFile(file, 'attachment');
        attachmentUrls.push(...result.files.map(f => f.url));
      }

      // 3. 发起审批
      const approval = await this.api.post('/weekly-budgets/approval', {
        ...approvalData,
        invoiceFiles: invoiceUrls,
        attachments: attachmentUrls
      });

      return approval;
    } catch (error) {
      console.error('审批提交失败:', error);
      throw error;
    }
  }

  // 上传文件
  async uploadFile(file, type) {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`/api/approvals/upload/${type}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.api.token}`
      },
      body: formData
    });

    return await response.json();
  }
}
```

## 🔧 配置要求

### 环境变量
```env
# 钉钉配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_CORP_ID=your_corp_id
DINGTALK_AGENT_ID=your_agent_id

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_TYPES=pdf,jpg,jpeg,png,doc,docx,xls,xlsx
```

### 钉钉后台配置
1. **审批模板配置**
   - 模板代码: `PROC-PAYMENT-001`
   - 表单字段: 申请人、申请部门、所属项目、付款事由等12个字段
   - 审批流程: 根据企业需求配置审批人

2. **回调地址配置**
   - 回调URL: `https://your-domain.com/api/approvals/callback`
   - 事件类型: 审批实例状态变更

3. **权限配置**
   - 审批管理权限
   - 用户信息读取权限
   - 部门信息读取权限

## 🚀 部署和测试

### 1. 启动服务
```bash
npm run dev
```

### 2. 测试认证
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"authCode": "your_dingtalk_auth_code"}'
```

### 3. 测试文件上传
```bash
curl -X POST http://localhost:3000/api/approvals/upload/invoice \
  -H "Authorization: Bearer your_jwt_token" \
  -F "file=@invoice.pdf"
```

### 4. 测试审批发起
```bash
curl -X POST http://localhost:3000/api/weekly-budgets/approval \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d @approval_request.json
```

## 📝 注意事项

1. **文件安全**: 上传的文件需要进行病毒扫描和内容验证
2. **存储管理**: 建议使用云存储服务（如阿里云OSS）存储文件
3. **权限控制**: 确保只有授权用户才能访问审批相关功能
4. **日志记录**: 记录所有审批操作的详细日志
5. **错误处理**: 提供友好的错误提示和重试机制

现在整个钉钉对公付款审批功能已经完全集成了用户认证和文件上传功能，可以投入实际使用了！
