# 钉钉集成指南

## 📋 前置条件

### 1. 钉钉开放平台账号
- 访问 [钉钉开放平台](https://open.dingtalk.com/)
- 使用企业管理员账号登录

### 2. 创建企业内部应用
1. 进入开发者后台
2. 选择"应用开发" > "企业内部开发"
3. 创建H5微应用
4. 记录以下信息：
   - App<PERSON>ey (应用标识)
   - AppSecret (应用密钥)
   - AgentId (应用ID)
   - CorpId (企业ID)

## 🔧 配置步骤

### 1. 应用基本信息
```
应用名称: CanTV钉钉微应用
应用类型: H5微应用
应用图标: 上传应用图标
应用描述: 企业内部H5微应用
```

### 2. 开发配置
```
服务器出口IP: 配置服务器公网IP
应用首页地址: https://your-domain.com/index.html
PC端首页地址: https://your-domain.com/index.html
```

### 3. 权限配置
需要申请以下权限：
- 身份验证
- 通讯录只读权限
- 手机号码信息
- 邮箱等个人信息

### 4. 环境变量配置
在 `.env` 文件中配置：
```env
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_CORP_ID=your_corp_id
```

## 🚀 部署配置

### 1. HTTPS要求
钉钉要求所有H5微应用必须使用HTTPS协议：
- 申请SSL证书
- 配置Nginx/Apache反向代理
- 确保所有API接口都通过HTTPS访问

### 2. 域名白名单
在钉钉开放平台配置可信域名：
- 添加你的域名到可信域名列表
- 确保域名已备案（中国大陆）

### 3. IP白名单
配置服务器出口IP白名单：
- 获取服务器公网IP
- 在开放平台添加到IP白名单

## 📱 前端集成

### 1. 引入钉钉JSAPI
```html
<script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script>
```

### 2. 免登授权
```javascript
dd.ready(() => {
    dd.runtime.permission.requestAuthCode({
        redirection: "none",
        onSuccess: (result) => {
            // 获取到免登码，发送到后端
            const authCode = result.code;
            // 调用后端API获取用户信息
        },
        onFail: (err) => {
            console.error('获取免登码失败:', err);
        }
    });
});
```

### 3. 错误处理
```javascript
dd.error((err) => {
    console.error('钉钉JSAPI错误:', err);
});
```

## 🔐 后端API集成

### 1. 获取访问令牌
```javascript
const response = await axios.get('https://oapi.dingtalk.com/gettoken', {
    params: {
        appkey: DINGTALK_APP_KEY,
        appsecret: DINGTALK_APP_SECRET
    }
});
```

### 2. 通过免登码获取用户信息
```javascript
const response = await axios.post(
    'https://oapi.dingtalk.com/topapi/v2/user/getuserinfo',
    { code: authCode },
    { params: { access_token: accessToken } }
);
```

### 3. 获取用户详细信息
```javascript
const response = await axios.post(
    'https://oapi.dingtalk.com/topapi/v2/user/get',
    { userid: userid },
    { params: { access_token: accessToken } }
);
```

## 🧪 测试指南

### 1. 本地开发测试
- 使用ngrok等工具将本地服务暴露到公网
- 配置HTTPS域名
- 在钉钉客户端中访问测试

### 2. 模拟环境测试
- 项目已支持非钉钉环境的模拟测试
- 在浏览器中直接访问可查看模拟数据
- 用于开发阶段的功能验证

### 3. 生产环境测试
- 部署到正式服务器
- 配置正式域名和SSL证书
- 在钉钉企业内部进行完整测试

## 🔍 常见问题

### 1. 免登码获取失败
- 检查应用权限配置
- 确认域名在白名单中
- 验证HTTPS配置

### 2. 用户信息获取失败
- 检查访问令牌是否有效
- 确认用户在企业通讯录中
- 验证API调用参数

### 3. JSAPI加载失败
- 检查网络连接
- 确认在钉钉客户端中访问
- 验证JSAPI版本

### 4. 跨域问题
- 配置CORS允许钉钉域名
- 检查预检请求处理
- 确认API响应头设置

## 📚 参考资料

- [钉钉开放平台文档](https://open.dingtalk.com/document/)
- [H5微应用开发指南](https://open.dingtalk.com/document/orgapp-client/h5-development-guide)
- [服务端API参考](https://open.dingtalk.com/document/orgapp-server/api-overview)
- [JSAPI参考](https://open.dingtalk.com/document/orgapp-client/jsapi-overview)

## 🆘 技术支持

如遇到问题，可以：
1. 查看钉钉开放平台文档
2. 在开发者社区提问
3. 联系钉钉技术支持
4. 查看项目README和代码注释
