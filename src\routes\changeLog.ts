import { FastifyInstance } from 'fastify';
import { ChangeLogController } from '../controllers/changeLog.js';
import { jwtAuthMiddleware } from '../middleware/auth.js';

export async function changeLogRoutes(fastify: FastifyInstance) {
  const changeLogController = new ChangeLogController();

  // 应用JWT认证中间件到所有路由
  fastify.addHook('preHandler', jwtAuthMiddleware);

  // 注册路由前的日志
  fastify.log.info('📝 注册项目变更记录路由...');

  // 获取变更记录列表
  fastify.get('/change-logs', {
    schema: {
      description: '获取项目变更记录列表',
      tags: ['ChangeLog'],
      querystring: {
        type: 'object',
        properties: {
          projectId: { type: 'string', description: '项目ID' },
          operatorId: { type: 'string', description: '操作人员ID' },
          changeType: { 
            type: 'string', 
            enum: ['CREATE', 'UPDATE', 'DELETE', 'STATUS_CHANGE', 'APPROVAL', 'ATTACHMENT'],
            description: '变更类型' 
          },
          startDate: { type: 'string', format: 'date', description: '开始日期' },
          endDate: { type: 'string', format: 'date', description: '结束日期' },
          page: { type: 'integer', minimum: 1, default: 1, description: '页码' },
          pageSize: { type: 'integer', minimum: 1, maximum: 100, default: 20, description: '每页数量' },
          sortBy: { 
            type: 'string', 
            enum: ['createdAt', 'changeType'], 
            default: 'createdAt',
            description: '排序字段' 
          },
          sortOrder: { 
            type: 'string', 
            enum: ['asc', 'desc'], 
            default: 'desc',
            description: '排序方向' 
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                changeLogs: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      changeType: { type: 'string' },
                      changeTitle: { type: 'string' },
                      changeDetails: { type: 'object' },
                      beforeData: { type: 'object' },
                      afterData: { type: 'object' },
                      changedFields: { type: 'array', items: { type: 'string' } },
                      operatorId: { type: 'string' },
                      operatorName: { type: 'string' },
                      operatorIP: { type: 'string' },
                      userAgent: { type: 'string' },
                      reason: { type: 'string' },
                      description: { type: 'string' },
                      projectId: { type: 'string' },
                      project: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          projectName: { type: 'string' },
                          status: { type: 'string' },
                          brand: {
                            type: 'object',
                            properties: {
                              id: { type: 'string' },
                              name: { type: 'string' }
                            }
                          }
                        }
                      },
                      createdAt: { type: 'string', format: 'date-time' }
                    }
                  }
                },
                total: { type: 'integer' },
                page: { type: 'integer' },
                pageSize: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, changeLogController.getChangeLogs.bind(changeLogController));

  // 获取单个项目的变更记录
  fastify.get('/projects/:projectId/change-logs', {
    schema: {
      description: '获取单个项目的变更记录',
      tags: ['ChangeLog'],
      params: {
        type: 'object',
        properties: {
          projectId: { type: 'string', description: '项目ID' }
        },
        required: ['projectId']
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1, description: '页码' },
          pageSize: { type: 'integer', minimum: 1, maximum: 100, default: 50, description: '每页数量' },
          sortOrder: { 
            type: 'string', 
            enum: ['asc', 'desc'], 
            default: 'desc',
            description: '排序方向' 
          }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                changeLogs: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      changeType: { type: 'string' },
                      changeTitle: { type: 'string' },
                      changeDetails: { type: 'object' },
                      // beforeData和afterData的结构不确定多少字段
                      // 因此使用any类型
                      beforeData: { type: 'object', additionalProperties: true },
                      afterData: { type: 'object', additionalProperties: true },
                      changedFields: { type: 'array', items: { type: 'string' } },
                      operatorId: { type: 'string' },
                      operatorName: { type: 'string' },
                      operatorIP: { type: 'string' },
                      userAgent: { type: 'string' },
                      reason: { type: 'string' },
                      description: { type: 'string' },
                      projectId: { type: 'string' },
                      project: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          projectName: { type: 'string' },
                          status: { type: 'string' },
                          brand: {
                            type: 'object',
                            properties: {
                              id: { type: 'string' },
                              name: { type: 'string' }
                            }
                          }
                        }
                      },
                      createdAt: { type: 'string', format: 'date-time' }
                    }
                  }
                },
                total: { type: 'integer' },
                page: { type: 'integer' },
                pageSize: { type: 'integer' },
                totalPages: { type: 'integer' }
              }
            },
            message: { type: 'string' }
          }
        }
      }
    }
  }, changeLogController.getProjectChangeLogs.bind(changeLogController));
}
