// 测试项目API的脚本
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

// 模拟获取JWT token的函数
async function getTestToken() {
  try {
    // 尝试使用模拟的钉钉免登码获取token
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        authCode: 'test_auth_code_for_development'
      })
    });

    if (loginResponse.ok) {
      const loginResult = await loginResponse.json();
      if (loginResult.success && loginResult.data.accessToken) {
        return loginResult.data.accessToken;
      }
    }

    // 如果登录失败，返回null，我们将测试无认证的情况
    console.log('⚠️ 无法获取有效的JWT token，将测试认证失败的情况');
    return null;
  } catch (error) {
    console.log('⚠️ 获取JWT token时发生错误:', error.message);
    return null;
  }
}

async function testProjectsAPI() {
  console.log('🧪 开始测试项目API...\n');

  // 获取测试token
  const token = await getTestToken();
  const headers = {
    'Content-Type': 'application/json'
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
    console.log('✅ 获取到JWT token，将进行认证测试');
  } else {
    console.log('⚠️ 未获取到JWT token，将测试认证失败情况');
  }

  try {
    // 1. 测试获取项目列表
    console.log('\n1. 测试获取项目列表...');
    const response = await fetch(`${BASE_URL}/projects`, { headers });
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 获取项目列表成功');
      console.log(`📊 总项目数: ${result.data.total}`);
      console.log(`📄 当前页项目数: ${result.data.projects.length}`);
      
      if (result.data.projects.length > 0) {
        const firstProject = result.data.projects[0];
        console.log('\n📋 第一个项目的数据结构:');
        console.log('基本信息:', {
          id: firstProject.id,
          projectName: firstProject.projectName,
          documentType: firstProject.documentType,
          status: firstProject.status
        });
        
        console.log('品牌信息:', firstProject.brand ? {
          id: firstProject.brand.id,
          name: firstProject.brand.name
        } : '❌ 品牌信息缺失');
        
        console.log('执行PM信息:', firstProject.executorPMInfo ? {
          userid: firstProject.executorPMInfo.userid,
          name: firstProject.executorPMInfo.name,
          department: firstProject.executorPMInfo.department
        } : '❌ 执行PM信息缺失');
        
        console.log('内容媒介信息:', firstProject.contentMediaInfo ? 
          `✅ ${firstProject.contentMediaInfo.length} 个内容媒介` : 
          '❌ 内容媒介信息缺失');
        
        console.log('预算信息:', firstProject.budget ? {
          planningBudget: firstProject.budget.planningBudget,
          influencerBudget: firstProject.budget.influencerBudget,
          adBudget: firstProject.budget.adBudget,
          otherBudget: firstProject.budget.otherBudget
        } : '❌ 预算信息缺失');
        
        console.log('成本信息:', firstProject.cost ? {
          influencerCost: firstProject.cost.influencerCost,
          adCost: firstProject.cost.adCost,
          otherCost: firstProject.cost.otherCost,
          estimatedInfluencerRebate: firstProject.cost.estimatedInfluencerRebate
        } : '❌ 成本信息缺失');
        
        console.log('利润信息:', firstProject.profit ? {
          profit: firstProject.profit.profit,
          grossMargin: firstProject.profit.grossMargin
        } : '❌ 利润信息缺失');
        
        console.log('附件信息:', firstProject.attachments ? 
          `✅ ${firstProject.attachments.length} 个附件` : 
          '❌ 附件信息缺失');

        // 2. 测试获取单个项目
        console.log('\n2. 测试获取单个项目...');
        const projectResponse = await fetch(`${BASE_URL}/projects/${firstProject.id}`, { headers });
        const projectResult = await projectResponse.json();
        
        if (projectResult.success) {
          console.log('✅ 获取单个项目成功');
          const project = projectResult.data;
          console.log('单个项目的用户信息:', {
            executorPMInfo: project.executorPMInfo ? '✅ 存在' : '❌ 缺失',
            contentMediaInfo: project.contentMediaInfo ? `✅ ${project.contentMediaInfo.length} 个` : '❌ 缺失'
          });
        } else {
          console.log('❌ 获取单个项目失败:', projectResult.message);
        }
      } else {
        console.log('⚠️ 没有项目数据，无法测试详细信息');
      }
    } else {
      console.log('❌ 获取项目列表失败:', result.message);
    }

    // 3. 测试分页
    console.log('\n3. 测试分页功能...');
    const pageResponse = await fetch(`${BASE_URL}/projects?page=1&pageSize=5`, { headers });
    const pageResult = await pageResponse.json();

    if (pageResult.success) {
      console.log('✅ 分页功能正常');
      console.log(`📄 请求页面: ${pageResult.data.page}`);
      console.log(`📊 页面大小: ${pageResult.data.pageSize}`);
      console.log(`📈 总页数: ${pageResult.data.totalPages}`);
    } else {
      console.log('❌ 分页功能失败:', pageResult.message);
    }

    // 4. 测试过滤功能
    console.log('\n4. 测试过滤功能...');
    const filterResponse = await fetch(`${BASE_URL}/projects?documentType=project_initiation`, { headers });
    const filterResult = await filterResponse.json();

    if (filterResult.success) {
      console.log('✅ 过滤功能正常');
      console.log(`📋 过滤后项目数: ${filterResult.data.projects.length}`);
    } else {
      console.log('❌ 过滤功能失败:', filterResult.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🏁 测试完成');
}

// 运行测试
testProjectsAPI();
