// 测试供应商和周预算管理功能
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

async function testSupplierAndWeeklyBudget() {
  console.log('🧪 测试供应商和周预算管理功能...\n');

  try {
    // 1. 创建供应商
    console.log('1. 创建供应商...');
    const supplierData = {
      name: '优质达人供应商',
      shortName: '优质达人',
      code: 'SUP001',
      contactPerson: '张经理',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      address: '北京市朝阳区xxx路xxx号',
      taxNumber: '91110000123456789X',
      bankAccount: '1234567890123456789',
      bankName: '中国银行北京分行',
      legalPerson: '张三',
      serviceTypes: ['influencer', 'advertising'],
      preferredTaxRate: 'special_6',
      creditLimit: 1000000,
      paymentTerms: '月结30天',
      rating: 5,
      notes: '优质供应商，合作愉快'
    };

    const createSupplierResponse = await fetch(`${BASE_URL}/test/suppliers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(supplierData)
    });

    const createSupplierResult = await createSupplierResponse.json();
    let supplierId = null;

    if (createSupplierResult.success) {
      supplierId = createSupplierResult.data.id;
      console.log('✅ 创建供应商成功, ID:', supplierId);
      console.log('供应商信息:', {
        name: createSupplierResult.data.name,
        serviceTypes: createSupplierResult.data.serviceTypes,
        status: createSupplierResult.data.status
      });
    } else {
      console.log('❌ 创建供应商失败:', createSupplierResult.message);
      return;
    }

    // 2. 获取供应商列表
    console.log('\n2. 获取供应商列表...');
    const suppliersResponse = await fetch(`${BASE_URL}/test/suppliers`);
    const suppliersResult = await suppliersResponse.json();

    if (suppliersResult.success) {
      console.log('✅ 获取供应商列表成功');
      console.log(`📊 总供应商数: ${suppliersResult.data.total}`);
    } else {
      console.log('❌ 获取供应商列表失败:', suppliersResult.message);
    }

    // 3. 获取现有项目ID
    console.log('\n3. 获取现有项目...');
    const projectsResponse = await fetch(`${BASE_URL}/test/projects`);
    const projectsResult = await projectsResponse.json();

    let projectId = null;
    if (projectsResult.success && projectsResult.data.projects && projectsResult.data.projects.length > 0) {
      projectId = projectsResult.data.projects[0].id;
      console.log('✅ 找到项目:', projectsResult.data.projects[0].projectName, 'ID:', projectId);
    } else {
      console.log('❌ 没有找到现有项目，无法测试周预算功能');
      return;
    }

    // 4. 创建周预算
    console.log('\n4. 创建周预算...');
    const weeklyBudgetData = {
      title: '第1周达人投放预算',
      weekStartDate: '2024-01-01',
      weekEndDate: '2024-01-07',
      serviceType: 'influencer',
      serviceContent: '小红书达人投放，包含图文和视频内容',
      remarks: '重点关注美妆类达人',
      contractAmount: 50000,
      taxRate: 'special_6',
      supplierId: supplierId
    };

    const createBudgetResponse = await fetch(`${BASE_URL}/test/projects/${projectId}/weekly-budgets`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(weeklyBudgetData)
    });

    const createBudgetResult = await createBudgetResponse.json();
    let budgetId = null;

    if (createBudgetResult.success) {
      budgetId = createBudgetResult.data.id;
      console.log('✅ 创建周预算成功, ID:', budgetId);
      console.log('预算信息:', {
        title: createBudgetResult.data.title,
        serviceType: createBudgetResult.data.serviceType,
        contractAmount: createBudgetResult.data.contractAmount,
        status: createBudgetResult.data.status
      });
    } else {
      console.log('❌ 创建周预算失败:', createBudgetResult.message);
      return;
    }

    // 5. 获取周预算列表
    console.log('\n5. 获取周预算列表...');
    const budgetsResponse = await fetch(`${BASE_URL}/test/weekly-budgets?projectId=${projectId}`);
    const budgetsResult = await budgetsResponse.json();

    if (budgetsResult.success) {
      console.log('✅ 获取周预算列表成功');
      console.log(`📊 总预算数: ${budgetsResult.data.total}`);
    } else {
      console.log('❌ 获取周预算列表失败:', budgetsResult.message);
    }

    // 6. 更新周预算状态
    console.log('\n6. 更新周预算状态...');
    const updateBudgetData = {
      id: budgetId,
      status: 'approved',
      paidAmount: 25000,
      remarks: '已支付50%预付款'
    };

    const updateBudgetResponse = await fetch(`${BASE_URL}/test/weekly-budgets/${budgetId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateBudgetData)
    });

    const updateBudgetResult = await updateBudgetResponse.json();

    if (updateBudgetResult.success) {
      console.log('✅ 更新周预算成功');
      console.log('更新后状态:', updateBudgetResult.data.status);
      console.log('已付金额:', updateBudgetResult.data.paidAmount);
      console.log('剩余金额:', updateBudgetResult.data.remainingAmount);
    } else {
      console.log('❌ 更新周预算失败:', updateBudgetResult.message);
    }

    // 7. 批量创建周预算
    console.log('\n7. 批量创建周预算...');
    const batchBudgetData = {
      startDate: '2024-01-08',
      endDate: '2024-01-28',
      serviceType: 'advertising',
      defaultContractAmount: 30000,
      defaultTaxRate: 'special_3'
    };

    const batchBudgetResponse = await fetch(`${BASE_URL}/test/projects/${projectId}/weekly-budgets/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(batchBudgetData)
    });

    const batchBudgetResult = await batchBudgetResponse.json();

    if (batchBudgetResult.success) {
      console.log('✅ 批量创建周预算成功');
      console.log(`📊 创建预算数: ${batchBudgetResult.data.length}`);
    } else {
      console.log('❌ 批量创建周预算失败:', batchBudgetResult.message);
    }

    // 8. 更新供应商信息
    console.log('\n8. 更新供应商信息...');
    const updateSupplierData = {
      id: supplierId,
      rating: 4,
      notes: '合作良好，但需要提升响应速度',
      status: 'active'
    };

    const updateSupplierResponse = await fetch(`${BASE_URL}/test/suppliers/${supplierId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateSupplierData)
    });

    const updateSupplierResult = await updateSupplierResponse.json();

    if (updateSupplierResult.success) {
      console.log('✅ 更新供应商成功');
      console.log('更新后评级:', updateSupplierResult.data.rating);
      console.log('更新后状态:', updateSupplierResult.data.status);
    } else {
      console.log('❌ 更新供应商失败:', updateSupplierResult.message);
    }

    // 9. 获取统计信息
    console.log('\n9. 获取统计信息...');
    
    // 获取周预算统计
    const budgetStatsResponse = await fetch(`${BASE_URL}/test/weekly-budgets/stats`);
    const budgetStatsResult = await budgetStatsResponse.json();

    if (budgetStatsResult.success) {
      console.log('✅ 获取周预算统计成功');
      console.log('预算统计:', {
        totalBudgets: budgetStatsResult.data.totalBudgets,
        totalContractAmount: budgetStatsResult.data.totalContractAmount,
        totalPaidAmount: budgetStatsResult.data.totalPaidAmount
      });
    } else {
      console.log('❌ 获取周预算统计失败:', budgetStatsResult.message);
    }

    console.log('\n✅ 供应商和周预算管理功能测试完成！');
    console.log('🎉 所有核心功能都正常工作');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testSupplierAndWeeklyBudget();
