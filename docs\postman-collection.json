{"info": {"name": "项目管理系统 API", "description": "项目管理系统的完整API接口集合，包括品牌管理、项目管理、统计分析和文件上传功能。", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api", "type": "string"}], "item": [{"name": "品牌管理", "item": [{"name": "获取品牌列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/brands?page=1&pageSize=10&status=active", "host": ["{{baseUrl}}"], "path": ["brands"], "query": [{"key": "page", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "10", "description": "每页数量"}, {"key": "status", "value": "active", "description": "品牌状态", "disabled": true}, {"key": "keyword", "value": "", "description": "搜索关键字", "disabled": true}]}}, "response": []}, {"name": "获取单个品牌", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/brands/brand-001", "host": ["{{baseUrl}}"], "path": ["brands", "brand-001"]}}, "response": []}, {"name": "创建品牌", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"测试品牌\",\n  \"description\": \"这是一个测试品牌\",\n  \"logo\": \"https://example.com/logo.png\"\n}"}, "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}, "response": []}, {"name": "更新品牌", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"brand-001\",\n  \"name\": \"更新后的品牌名称\",\n  \"description\": \"更新后的品牌描述\"\n}"}, "url": {"raw": "{{baseUrl}}/brands", "host": ["{{baseUrl}}"], "path": ["brands"]}}, "response": []}, {"name": "删除品牌", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/brands/brand-001", "host": ["{{baseUrl}}"], "path": ["brands", "brand-001"]}}, "response": []}]}, {"name": "项目管理", "item": [{"name": "获取项目列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/projects?page=1&pageSize=20&status=active", "host": ["{{baseUrl}}"], "path": ["projects"], "query": [{"key": "page", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "20", "description": "每页数量"}, {"key": "status", "value": "active", "description": "项目状态", "disabled": true}, {"key": "brandId", "value": "", "description": "品牌ID", "disabled": true}, {"key": "contractType", "value": "", "description": "合同类型", "disabled": true}, {"key": "keyword", "value": "", "description": "项目名称关键字", "disabled": true}]}}, "response": []}, {"name": "获取单个项目", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/projects/project-001", "host": ["{{baseUrl}}"], "path": ["projects", "project-001"]}}, "response": []}, {"name": "创建项目", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"documentType\": \"project_initiation\",\n  \"brandId\": \"brand-001\",\n  \"projectName\": \"春节营销活动\",\n  \"period\": {\n    \"startDate\": \"2024-02-01\",\n    \"endDate\": \"2024-02-29\"\n  },\n  \"budget\": {\n    \"planningBudget\": 1000000.00,\n    \"influencerBudget\": 400000.00,\n    \"adBudget\": 300000.00,\n    \"otherBudget\": 100000.00\n  },\n  \"cost\": {\n    \"influencerCost\": 350000.00,\n    \"adCost\": 280000.00,\n    \"otherCost\": 80000.00,\n    \"estimatedInfluencerRebate\": 20000.00\n  },\n  \"executorPM\": \"user-001\",\n  \"contentMediaIds\": [\"user-002\", \"user-003\"],\n  \"contractType\": \"annual_frame\",\n  \"settlementRules\": \"<p>按月结算，每月25日前提交结算单</p>\",\n  \"kpi\": \"<p><strong>目标指标：</strong></p><ul><li>曝光量：1000万+</li><li>点击率：2%+</li><li>转化率：0.5%+</li></ul>\"\n}"}, "url": {"raw": "{{baseUrl}}/projects", "host": ["{{baseUrl}}"], "path": ["projects"]}}, "response": []}, {"name": "更新项目", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"project-001\",\n  \"projectName\": \"更新后的项目名称\",\n  \"status\": \"active\",\n  \"budget\": {\n    \"planningBudget\": 1200000.00\n  }\n}"}, "url": {"raw": "{{baseUrl}}/projects", "host": ["{{baseUrl}}"], "path": ["projects"]}}, "response": []}, {"name": "删除项目", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/projects/project-001", "host": ["{{baseUrl}}"], "path": ["projects", "project-001"]}}, "response": []}]}, {"name": "统计分析", "item": [{"name": "获取项目统计", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/projects/stats", "host": ["{{baseUrl}}"], "path": ["projects", "stats"]}}, "response": []}]}, {"name": "文件上传", "item": [{"name": "上传文件", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/upload", "host": ["{{baseUrl}}"], "path": ["upload"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 可以在这里添加预请求脚本", "// 例如设置认证令牌等"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 通用测试脚本", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "pm.test(\"Response is successful\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"]}}]}