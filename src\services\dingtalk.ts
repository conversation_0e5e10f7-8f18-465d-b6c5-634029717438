import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';
import { env } from '../config/env.js';
import type {
  ApprovalFormField,
  DingTalkApprovalDetail,
  DingTalkApprovalResponse,
  DingTalkCreateApprovalRequest,
  PaymentApprovalFormData
} from '../types/approval.js';
import type {
  AccessTokenResponse,
  AppConfig,
  DingTalkConfig,
  FileUploadResponse,
  GetDepartmentListResponse,
  GetUserInfoByCodeResponse,
  GetUserResponse,
  JSAPISignature,
  MessageRequest,
  UserListResponse
} from '../types/dingtalk.js';

export class DingTalkService {
  private apiClient: AxiosInstance;
  private newApiClient: AxiosInstance;
  private config: DingTalkConfig;
  private accessToken: string | null = null;
  private tokenExpireTime: number = 0;

  constructor() {
    this.config = {
      appKey: env.DINGTALK_APP_KEY || '',
      appSecret: env.DINGTALK_APP_SECRET || '',
      corpId: env.DINGTALK_CORP_ID || '',
      agentId: env.DINGTALK_AGENT_ID || '',
    };

    // 旧版API客户端
    this.apiClient = axios.create({
      baseURL: env.DINGTALK_API_BASE_URL,
      timeout: 10000,
    });

    // 新版API客户端
    this.newApiClient = axios.create({
      baseURL: env.DINGTALK_NEW_API_BASE_URL,
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  /**
   * 检查钉钉配置是否完整
   */
  private checkConfig(): void {
    if (!this.config.appKey || !this.config.appSecret || !this.config.corpId) {
      throw new Error('钉钉配置不完整，请检查环境变量 DINGTALK_APP_KEY, DINGTALK_APP_SECRET, DINGTALK_CORP_ID');
    }
  }

  /**
   * 检查配置是否可用
   */
  isConfigured(): boolean {
    return !!(this.config.appKey && this.config.appSecret && this.config.corpId);
  }

  private setupInterceptors() {
    // 请求拦截器
    this.apiClient.interceptors.request.use((config) => {
      console.log(`[DingTalk API] ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    });

    // 响应拦截器
    this.apiClient.interceptors.response.use(
      (response) => {
        console.log(`[DingTalk API] Response:`, response.data);
        return response;
      },
      (error) => {
        console.error(`[DingTalk API] Error:`, error.response?.data || error.message);
        throw error;
      }
    );
  }

  /**
   * 获取访问令牌
   */
  async getAccessToken(): Promise<string> {
    this.checkConfig();

    // 检查token是否过期
    if (this.accessToken && Date.now() < this.tokenExpireTime) {
      return this.accessToken;
    }

    try {
      const response = await this.apiClient.get<AccessTokenResponse>('/gettoken', {
        params: {
          appkey: this.config.appKey,
          appsecret: this.config.appSecret,
        },
      });

      if (response.data.errcode !== 0) {
        throw new Error(`获取访问令牌失败: ${response.data.errmsg}`);
      }

      this.accessToken = response.data.access_token!;
      // 提前5分钟过期
      this.tokenExpireTime = Date.now() + (response.data.expires_in! - 300) * 1000;

      return this.accessToken;
    } catch (error) {
      console.error('获取钉钉访问令牌失败:', error);
      throw error;
    }
  }

  /**
   * 通过免登码获取用户信息
   */
  async getUserInfoByCode(authCode: string): Promise<string> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<GetUserInfoByCodeResponse>(
        '/topapi/v2/user/getuserinfo',
        {
          code: authCode,
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取用户信息失败: ${response.data.errmsg}`);
      }

      return response.data.result!.userid;
    } catch (error) {
      console.error('通过免登码获取用户信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户详细信息
   */
  async getUserDetail(userid: string): Promise<GetUserResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<GetUserResponse>(
        '/topapi/v2/user/get',
        {
          userid,
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取用户详细信息失败: ${response.data.errmsg}`);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取用户详细信息失败:', error);
      throw error;
    }
  }

  /**
   * 通过免登码获取完整用户信息
   */
  async getUserInfoByAuthCode(authCode: string): Promise<GetUserResponse['result'] | null> {
    try {
      // 1. 通过免登码获取用户ID
      const userid = await this.getUserInfoByCode(authCode);

      if (!userid) {
        return null;
      }

      // 2. 通过用户ID获取详细信息
      const userDetail = await this.getUserDetail(userid);

      return userDetail;
    } catch (error) {
      console.error('通过免登码获取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 生成JSAPI签名
   */
  async generateJSAPISignature(url: string): Promise<JSAPISignature> {
    // 使用正确的JSAPI签名方法
    return await this.generateCorrectJSAPISignature(url);
  }

  /**
   * 获取部门列表（只获取直接子部门）
   */
  async getDepartmentList(): Promise<GetDepartmentListResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<GetDepartmentListResponse>(
        '/topapi/v2/department/listsub',
        {
          dept_id: 1, // 根部门ID
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取部门列表失败: ${response.data.errmsg}`);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取部门列表失败:', error);
      throw error;
    }
  }

  /**
   * 递归获取所有部门（包括嵌套的子部门）
   */
  async getAllDepartments(): Promise<GetDepartmentListResponse['result']> {
    const allDepartments: any[] = [];
    const processedDeptIds = new Set<number>();

    const getDepartmentRecursive = async (deptId: number): Promise<void> => {
      if (processedDeptIds.has(deptId)) {
        return; // 避免重复处理
      }
      processedDeptIds.add(deptId);

      try {
        const accessToken = await this.getAccessToken();
        const response = await this.apiClient.post<GetDepartmentListResponse>(
          '/topapi/v2/department/listsub',
          {
            dept_id: deptId,
          },
          {
            params: {
              access_token: accessToken,
            },
          }
        );

        if (response.data.errcode === 0 && response.data.result) {
          for (const dept of response.data.result) {
            allDepartments.push(dept);
            // 递归获取子部门
            await getDepartmentRecursive(dept.dept_id);
          }
        }
      } catch (error) {
        console.warn(`获取部门 ${deptId} 的子部门失败:`, error);
      }
    };

    // 从根部门开始递归获取
    await getDepartmentRecursive(1);

    console.log(`递归获取到 ${allDepartments.length} 个部门`);
    return allDepartments;
  }

  /**
   * 获取应用配置信息（用于前端初始化）
   */
  getAppConfig(): AppConfig {
    return {
      appKey: this.config.appKey || 'demo-app-key',
      corpId: this.config.corpId || 'demo-corp-id',
      agentId: this.config.agentId || 'demo-agent-id',
      jsApiList: [
        'runtime.permission.requestAuthCode',
        'biz.contact.choose',
        'device.notification.confirm',
        'device.notification.alert',
        'device.notification.prompt',
        'biz.ding.post',
        'biz.util.openLink'
      ],
      debug: process.env.NODE_ENV === 'development'
    };
  }

  /**
   * 获取部门用户列表
   */
  async getDepartmentUsers(deptId: number, cursor: number = 0, size: number = 100): Promise<UserListResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<UserListResponse>(
        '/topapi/user/listsimple',
        {
          dept_id: deptId,
          cursor,
          size
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取部门用户列表失败: ${response.data.errmsg}`);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取部门用户列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有用户列表（当没有设置部门时使用）
   */
  async getAllUsers(cursor: number = 0, size: number = 100): Promise<UserListResponse['result']> {
    const accessToken = await this.getAccessToken();

    try {
      // 首先尝试使用钉钉的用户列表接口，不指定部门ID来获取所有用户
      const response = await this.apiClient.post<UserListResponse>(
        '/topapi/user/listsimple',
        {
          // dept_id: 1, // 使用根部门ID，通常可以获取到所有用户
          cursor,
          size,
          contain_access_limit: false // 包含受限用户
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        // 如果根部门方式失败，尝试递归获取所有部门的用户
        console.warn('通过根部门获取用户失败，尝试递归遍历所有部门:', response.data.errmsg);
        return await this.getAllUsersByDepartments(cursor, size);
      }

      // 检查返回的用户数量，如果太少可能是因为部门嵌套问题
      const userCount = response.data.result?.list?.length || 0;
      console.log(`通过根部门获取到 ${userCount} 个用户`);

      // 如果用户数量很少（小于10），可能存在部门嵌套问题，使用递归方法
      if (userCount < 10 && cursor === 0) {
        console.log('用户数量较少，可能存在部门嵌套问题，尝试递归获取所有部门用户');
        return await this.getAllUsersByDepartments(cursor, size);
      }

      return response.data.result;
    } catch (error) {
      console.error('获取所有用户列表失败，尝试递归遍历部门方式:', error);
      // 降级方案：递归遍历所有部门获取用户
      return await this.getAllUsersByDepartments(cursor, size);
    }
  }

  /**
   * 通过遍历所有部门获取用户列表（降级方案）
   */
  private async getAllUsersByDepartments(cursor: number = 0, size: number = 100): Promise<UserListResponse['result']> {
    try {
      // 递归获取所有部门（包括嵌套的子部门）
      console.log('开始递归获取所有部门...');
      const departments = await this.getAllDepartments();
      if (!departments || departments.length === 0) {
        console.warn('未获取到任何部门');
        return {
          list: [],
          has_more: false,
          next_cursor: 0
        };
      }

      console.log(`获取到 ${departments.length} 个部门，开始获取用户...`);
      const allUsers: any[] = [];
      const userIds = new Set<string>(); // 用于去重

      // 遍历所有部门获取用户
      for (const dept of departments) {
        try {
          let deptCursor = 0;
          let hasMore = true;

          while (hasMore && allUsers.length < size) {
            const deptUsers = await this.getDepartmentUsers(dept.dept_id, deptCursor, Math.min(100, size - allUsers.length));

            if (deptUsers?.list) {
              // 去重添加用户
              for (const user of deptUsers.list) {
                if (!userIds.has(user.userid)) {
                  userIds.add(user.userid);
                  allUsers.push(user);
                }
              }
            }

            hasMore = deptUsers?.has_more || false;
            deptCursor = deptUsers?.next_cursor || 0;

            // 如果已经获取足够的用户，跳出循环
            if (allUsers.length >= size) {
              break;
            }
          }
        } catch (deptError) {
          console.warn(`获取部门 ${dept.dept_id}(${dept.name}) 用户失败:`, deptError);
          // 继续处理其他部门
        }
      }

      console.log(`从 ${departments.length} 个部门中获取到 ${allUsers.length} 个唯一用户`);

      // 应用分页逻辑
      const startIndex = cursor;
      const endIndex = Math.min(startIndex + size, allUsers.length);
      const paginatedUsers = allUsers.slice(startIndex, endIndex);

      return {
        list: paginatedUsers,
        has_more: endIndex < allUsers.length,
        next_cursor: endIndex < allUsers.length ? endIndex : 0
      };
    } catch (error) {
      console.error('通过部门遍历获取用户失败:', error);
      return {
        list: [],
        has_more: false,
        next_cursor: 0
      };
    }
  }

  /**
   * 发送工作通知
   */
  async sendWorkNotification(message: MessageRequest): Promise<boolean> {
    const accessToken = await this.getAccessToken();
    console.log('[ this.config.agentId ] >', this.config.agentId)
    try {
      const response = await this.apiClient.post(
        '/topapi/message/corpconversation/asyncsend_v2',
        {
          agent_id: this.config.agentId,
          userid_list: message.userIds.join(','),
          msg: {
            msgtype: message.messageType || 'text',
            text: {
              content: message.content
            }
          }
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`发送工作通知失败: ${response.data.errmsg}`);
      }

      return true;
    } catch (error) {
      console.error('发送工作通知失败:', error);
      throw error;
    }
  }

  /**
   * 上传媒体文件
   */
  async uploadMedia(file: Buffer, type: 'image' | 'voice' | 'video' | 'file'): Promise<string> {
    const accessToken = await this.getAccessToken();

    try {
      const formData = new FormData();
      formData.append('media', new Blob([file]));
      formData.append('type', type);

      const response = await this.apiClient.post<FileUploadResponse>(
        '/media/upload',
        formData,
        {
          params: {
            access_token: accessToken,
            type
          },
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`上传媒体文件失败: ${response.data.errmsg}`);
      }

      return response.data!.media_id;
    } catch (error) {
      console.error('上传媒体文件失败:', error);
      throw error;
    }
  }

  /**
   * 批量上传附件文件到钉钉媒体库
   */
  async uploadAttachmentFiles(files: any[]): Promise<string[]> {
    const mediaIds: string[] = [];

    for (const file of files) {
      try {
        // 读取文件内容
        let fileBuffer: Buffer;
        if (file.file) {
          // Fastify multipart file
          const chunks: Buffer[] = [];
          for await (const chunk of file.file) {
            chunks.push(chunk);
          }
          fileBuffer = Buffer.concat(chunks);
        } else if (file.buffer) {
          // 已经是Buffer
          fileBuffer = file.buffer;
        } else {
          throw new Error('无法读取文件内容');
        }

        // 根据文件类型确定媒体类型
        const mimeType = file.mimetype || file.mimeType || '';
        let mediaType: 'image' | 'voice' | 'video' | 'file' = 'file';

        if (mimeType.startsWith('image/')) {
          mediaType = 'image';
        } else if (mimeType.startsWith('audio/')) {
          mediaType = 'voice';
        } else if (mimeType.startsWith('video/')) {
          mediaType = 'video';
        }

        // 上传到钉钉媒体库
        const mediaId = await this.uploadMedia(fileBuffer, mediaType);
        mediaIds.push(mediaId);

        console.log(`文件上传到钉钉成功: ${file.filename || file.originalname} -> ${mediaId}`);
      } catch (error) {
        console.error(`文件上传到钉钉失败: ${file.filename || file.originalname}`, error);
        throw new Error(`文件上传失败: ${file.filename || file.originalname}`);
      }
    }

    return mediaIds;
  }

  /**
   * 获取jsapi_ticket（用于正确的JSAPI签名）
   */
  async getJSAPITicket(): Promise<string> {
    try {
      const accessToken = await this.getAccessToken();

      console.log('正在获取JSAPI票据...');

      const response = await this.apiClient.get('/get_jsapi_ticket', {
        params: {
          access_token: accessToken,
        },
      });

      console.log('JSAPI票据响应:', {
        errcode: response.data.errcode,
        errmsg: response.data.errmsg,
        hasTicket: !!response.data.ticket
      });

      if (response.data.errcode !== 0) {
        throw new Error(`获取JSAPI票据失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
      }

      if (!response.data.ticket) {
        throw new Error('JSAPI票据为空');
      }

      console.log('JSAPI票据获取成功');
      return response.data.ticket;
    } catch (error) {
      console.error('获取JSAPI票据失败:', error);

      // 检查是否是网络错误
      if (error && typeof error === 'object' && 'code' in error) {
        const errorCode = (error as any).code;
        if (errorCode === 'ECONNREFUSED' || errorCode === 'ENOTFOUND') {
          throw new Error('无法连接到钉钉服务器，请检查网络连接');
        }
      }

      // 检查是否是认证错误
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('40001') || errorMessage.includes('access_token')) {
        throw new Error('访问令牌无效，请检查应用配置');
      }

      // 重新抛出错误，不要返回模拟票据
      throw new Error(`获取JSAPI票据失败: ${errorMessage}`);
    }
  }

  /**
   * 清理URL，移除钉钉调试参数
   */
  private cleanUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      // 移除钉钉调试相关的参数
      const debugParams = [
        'dd_debug_h5',
        'dd_debug_v1',
        'dd_debug_os',
        'dd_debug_v2',
        'dd_debug_unifiedAppId',
        'dd_debug_token',
        'dd_debug_uuid',
        'dd_debug_pid'
      ];

      debugParams.forEach(param => {
        urlObj.searchParams.delete(param);
      });

      return urlObj.toString();
    } catch (error) {
      console.warn('URL清理失败，使用原始URL:', error);
      return url;
    }
  }

  /**
   * 生成正确的JSAPI签名
   */
  async generateCorrectJSAPISignature(url: string): Promise<JSAPISignature> {
    const timestamp = Date.now();
    const nonceStr = crypto.randomBytes(16).toString('hex');

    try {
      const ticket = await this.getJSAPITicket();

      // 1. URL解码
      let decodedUrl = decodeURIComponent(url);

      // 2. 清理调试参数
      decodedUrl = this.cleanUrl(decodedUrl);

      // 3. 按照钉钉官方文档的签名算法
      // 重要：参数必须严格按照字母顺序排列
      const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${decodedUrl}`;
      const signature = crypto.createHash('sha1').update(string1, 'utf8').digest('hex');

      console.log('JSAPI签名生成详情:', {
        originalUrl: url,
        cleanedUrl: decodedUrl,
        ticket: ticket.substring(0, 10) + '...' + ticket.substring(ticket.length - 10), // 部分隐藏ticket
        nonceStr,
        timestamp,
        string1: string1.substring(0, 100) + '...',
        signature
      });

      return {
        agentId: this.config.agentId || '',
        corpId: this.config.corpId,
        timeStamp: timestamp,
        nonceStr,
        signature,
      };
    } catch (error) {
      console.error('生成JSAPI签名失败:', error);
      // 降级到简单签名
      return this.generateJSAPISignature(url);
    }
  }

  /**
   * 发起审批实例
   */
  async createApprovalInstance(request: DingTalkCreateApprovalRequest): Promise<DingTalkApprovalResponse> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<DingTalkApprovalResponse>(
        '/topapi/processinstance/create',
        request,
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`发起审批实例失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
      }

      return response.data;
    } catch (error) {
      console.error('发起审批实例失败:', error);
      throw error;
    }
  }

  /**
   * 获取审批实例详情
   */
  async getApprovalInstanceDetail(processInstanceId: string): Promise<DingTalkApprovalDetail> {
    const accessToken = await this.getAccessToken();

    try {
      const response = await this.apiClient.post<DingTalkApprovalDetail>(
        '/topapi/processinstance/get',
        {
          process_instance_id: processInstanceId,
        },
        {
          params: {
            access_token: accessToken,
          },
        }
      );

      if (response.data.errcode !== 0) {
        throw new Error(`获取审批实例详情失败: ${response.data.errmsg} (错误码: ${response.data.errcode})`);
      }

      return response.data;
    } catch (error) {
      console.error('获取审批实例详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建对公付款审批表单数据
   */
  createPaymentApprovalFormData(data: PaymentApprovalFormData): ApprovalFormField[] {
    const formFields: ApprovalFormField[] = [
      {
        name: '申请人',
        value: data.applicantName,
        componentType: 'TextField'
      },
      {
        name: '申请部门',
        value: data.department.toString(),
        componentType: 'TextField'
      },
      {
        name: '所属项目',
        value: data.projectName,
        componentType: 'TextField'
      },
      {
        name: '付款事由',
        value: data.paymentReason,
        componentType: 'TextareaField'
      },
      {
        name: '合同签署主体',
        value: data.contractEntity,
        componentType: 'TextField'
      },
      {
        name: '付款总额',
        value: data.totalAmount.toString(),
        componentType: 'TextField'
      },
      {
        name: '期望付款日期',
        value: data.expectedPaymentDate,
        componentType: 'DateField'
      },
      {
        name: '付款方式',
        value: data.paymentMethod,
        componentType: 'TextField'
      },
      // {
      //   name: '收款账户名称',
      //   value: data.receivingAccount.accountName,
      //   componentType: 'TextField'
      // },
      {
        name: '收款账号',
        value: data.receivingAccount.accountNumber,
        componentType: 'TextField'
      },
      // {
      //   name: '开户银行',
      //   value: data.receivingAccount.bankName,
      //   componentType: 'TextField'
      // }
    ];

    // 可选字段
    // if (data.relatedApprovalId) {
    //   formFields.push({
    //     name: '关联审批单',
    //     value: data.relatedApprovalId,
    //     componentType: 'TextField'
    //   });
    // }

    if (data.receivingAccount.bankCode) {
      formFields.push({
        name: '银行代码',
        value: data.receivingAccount.bankCode,
        componentType: 'TextField'
      });
    }

    if (data.invoiceFiles && data.invoiceFiles.length > 0) {
      formFields.push({
        name: '附件明细',
        value: data.invoiceFiles.concat(data.attachments ? data.attachments : []),
        componentType: 'DDAttachment'
      });
    }

    // 处理钉钉媒体附件
    // if (data.attachmentMediaIds && data.attachmentMediaIds.length > 0) {
    //   formFields.push({
    //     name: '附件',
    //     value: data.attachmentMediaIds,
    //     componentType: 'DDAttachment'
    //   });
    // } else if (data.attachments && data.attachments.length > 0) {
    //   // 兼容旧的附件URL格式
    //   formFields.push({
    //     name: '附件',
    //     value: data.attachments.join(','),
    //     componentType: 'TextField'
    //   });
    // }

    if (data.remark) {
      formFields.push({
        name: '备注',
        value: data.remark,
        componentType: 'TextareaField'
      });
    }

    return formFields;
  }

  /**
   * 发起对公付款审批
   */
  async createPaymentApproval(
    formData: PaymentApprovalFormData,
    originatorUserId: string,
    departmentId: number,
    processCode: string = 'PROC-PAYMENT-001',
  ): Promise<string> {
    const formFields = this.createPaymentApprovalFormData(formData);

    const request: DingTalkCreateApprovalRequest = {
      process_code: processCode,
      originator_user_id: originatorUserId,
      dept_id: departmentId,
      form_component_values: formFields
    };

    const response = await this.createApprovalInstance(request);

    if (!response.process_instance_id) {
      throw new Error('发起审批失败：未返回审批实例ID');
    }

    return response.process_instance_id;
  }
}
