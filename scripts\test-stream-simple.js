#!/usr/bin/env node

/**
 * 测试简化版钉钉Stream服务
 */

import { DingTalkStreamSimple } from '../dist/services/dingtalk-stream-simple.js';

async function testStreamService() {
  console.log('🧪 开始测试简化版钉钉Stream服务...');
  
  const streamService = new DingTalkStreamSimple();
  
  // 设置事件监听器
  streamService.on('message', (data) => {
    console.log('📨 收到消息事件:', JSON.stringify(data, null, 2));
  });
  
  streamService.on('approval_change', (data) => {
    console.log('📋 收到审批变更事件:', JSON.stringify(data, null, 2));
  });
  
  streamService.on('error', (error) => {
    console.error('❌ Stream服务错误:', error);
  });
  
  try {
    // 启动服务
    console.log('🚀 启动Stream服务...');
    await streamService.start();
    console.log('✅ Stream服务启动成功');
    
    // 显示连接状态
    const status = streamService.getConnectionStatus();
    console.log('📊 连接状态:', status);
    
    // 保持连接一段时间用于测试
    console.log('⏰ 保持连接30秒用于测试...');
    console.log('💡 您可以在钉钉中触发审批事件来测试消息接收');
    
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // 停止服务
    console.log('🛑 停止Stream服务...');
    await streamService.stop();
    console.log('✅ Stream服务已停止');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error.message.includes('400')) {
      console.log('\n🔍 400错误可能的原因:');
      console.log('1. 钉钉应用配置错误 (DINGTALK_APP_KEY, DINGTALK_APP_SECRET)');
      console.log('2. 应用没有Stream推送权限');
      console.log('3. 请求参数格式不正确');
      console.log('4. 访问令牌无效');
    }
    
    process.exit(1);
  }
}

// 处理退出信号
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，正在停止测试...');
  process.exit(0);
});

// 运行测试
testStreamService()
  .then(() => {
    console.log('✅ 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
