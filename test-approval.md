# 钉钉对公付款审批功能实现总结

## 🎯 实现目标

在周预算上发起钉钉对公付款审批，当审批通过后，将流程中对应金额写回数据库。

## ✅ 已完成的功能

### 1. 数据库模型扩展

#### 新增审批状态枚举
```sql
enum ApprovalStatus {
  NONE        // 无审批
  PENDING     // 审批中
  APPROVED    // 已通过
  REJECTED    // 已拒绝
  CANCELLED   // 已撤销
}
```

#### 新增审批实例表
```sql
model ApprovalInstance {
  id                    String          @id @default(cuid())
  processInstanceId     String          @unique // 钉钉审批实例ID
  processCode           String          // 审批模板代码
  businessId            String?         // 业务ID
  title                 String          // 审批标题
  originatorUserId      String          // 发起人钉钉用户ID
  status                ApprovalStatus  // 审批状态
  result                String?         // 审批结果
  createTime            DateTime        // 创建时间
  finishTime            DateTime?       // 完成时间
  approvalAmount        Decimal         // 审批金额
  actualAmount          Decimal?        // 实际金额
  reason                String?         // 审批原因
  remark                String?         // 备注
  weeklyBudgetId        String          // 关联周预算
  weeklyBudget          WeeklyBudget    @relation
  // ... 其他字段
}
```

#### 扩展周预算表
```sql
model WeeklyBudget {
  // ... 原有字段
  approvalStatus        ApprovalStatus  @default(NONE)  // 审批状态
  approvalAmount        Decimal?        // 审批金额
  approvalReason        String?         // 审批原因/说明
  approvalInstances     ApprovalInstance[] // 审批实例关联
}
```

### 2. 钉钉服务扩展

#### 新增审批相关方法
- `createApprovalInstance()` - 发起审批实例
- `getApprovalInstanceDetail()` - 获取审批实例详情
- `createPaymentApprovalFormData()` - 创建对公付款审批表单数据
- `createPaymentApproval()` - 发起对公付款审批

#### 审批表单字段
- 审批标题
- 项目名称
- 供应商名称
- 服务类型
- 服务内容
- 合同金额
- 申请金额
- 付款原因
- 周开始/结束日期
- 税率
- 银行账号信息
- 备注

### 3. 审批业务服务

#### ApprovalService 类
- `createPaymentApproval()` - 发起对公付款审批
- `getApprovalInstances()` - 获取审批实例列表
- `getApprovalInstance()` - 获取单个审批实例
- `syncApprovalStatus()` - 同步审批状态
- `updateWeeklyBudgetAfterApproval()` - 审批通过后更新周预算
- `getApprovalStats()` - 获取审批统计信息
- `handleApprovalCallback()` - 处理审批回调事件

### 4. 数据库服务扩展

#### DatabaseService 新增方法
- `createApprovalInstance()` - 创建审批实例
- `getApprovalInstances()` - 获取审批实例列表
- `getApprovalInstance()` - 获取单个审批实例
- `updateApprovalInstanceStatus()` - 更新审批实例状态
- `getApprovalStats()` - 获取审批统计信息
- `transformApprovalInstance()` - 转换审批实例数据

### 5. API 接口

#### 审批管理接口
- `POST /api/approvals/payment` - 发起对公付款审批
- `GET /api/approvals` - 获取审批实例列表
- `GET /api/approvals/:id` - 获取单个审批实例
- `POST /api/approvals/sync/:processInstanceId` - 同步审批状态
- `GET /api/approvals/stats` - 获取审批统计信息
- `POST /api/approvals/batch-sync` - 批量同步审批状态
- `POST /api/approvals/callback` - 审批回调接口（钉钉回调）

#### 周预算扩展接口
- `POST /api/weekly-budgets/approval` - 发起周预算对公付款审批

### 6. 类型定义

#### 审批相关类型
- `ApprovalInstance` - 审批实例接口
- `ApprovalStatus` - 审批状态枚举
- `CreateApprovalRequest` - 发起审批请求
- `DingTalkCreateApprovalRequest` - 钉钉发起审批请求
- `DingTalkApprovalResponse` - 钉钉审批响应
- `DingTalkApprovalDetail` - 钉钉审批详情
- `PaymentApprovalFormData` - 对公付款审批表单数据
- `ApprovalStats` - 审批统计信息
- `ApprovalQueryParams` - 审批查询参数

## 🔄 业务流程

### 发起审批流程
1. 用户在周预算上发起对公付款审批
2. 系统验证审批金额和周预算信息
3. 构建钉钉审批表单数据
4. 调用钉钉API发起审批实例
5. 创建本地审批实例记录
6. 更新周预算的审批状态为"审批中"

### 审批回调处理
1. 钉钉审批状态变更时触发回调
2. 系统接收回调事件
3. 同步钉钉审批状态到本地
4. 如果审批通过，更新周预算的已付金额
5. 更新周预算的审批状态

### 状态同步机制
- 支持主动同步单个审批状态
- 支持批量同步多个审批状态
- 自动处理钉钉回调事件
- 状态映射：钉钉状态 → 本地状态

## 🚀 使用示例

### 发起审批
```javascript
POST /api/weekly-budgets/approval
{
  "weeklyBudgetId": "budget-001",
  "approvalAmount": 10000,
  "reason": "项目执行付款",
  "remark": "第一期付款"
}
```

### 查询审批列表
```javascript
GET /api/approvals?status=PENDING&page=1&pageSize=20
```

### 同步审批状态
```javascript
POST /api/approvals/sync/process-instance-001
```

## 📋 待完成事项

1. **钉钉审批模板配置**
   - 需要在钉钉后台配置对公付款审批模板
   - 设置审批流程和审批人
   - 配置表单字段

2. **用户认证集成**
   - 从JWT或session中获取当前用户ID
   - 替换硬编码的用户ID

3. **错误处理优化**
   - 添加更详细的错误日志
   - 优化错误响应格式

4. **测试和验证**
   - 编写单元测试
   - 集成测试
   - 端到端测试

5. **监控和日志**
   - 添加审批流程监控
   - 记录关键操作日志

## 🔧 配置要求

### 环境变量
```env
# 钉钉配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret
DINGTALK_CORP_ID=your_corp_id
DINGTALK_AGENT_ID=your_agent_id

# 数据库配置
DATABASE_URL=postgresql://...
```

### 钉钉权限
- 审批管理权限
- 用户信息读取权限
- 部门信息读取权限

## 📝 注意事项

1. **审批模板代码**：需要在钉钉后台创建对公付款审批模板，并获取模板代码
2. **回调地址**：需要配置钉钉回调地址指向 `/api/approvals/callback`
3. **数据一致性**：确保钉钉审批状态与本地状态保持同步
4. **金额精度**：使用Decimal类型处理金额，避免浮点数精度问题
5. **并发处理**：考虑多个审批同时进行的情况

这个实现提供了完整的钉钉对公付款审批功能，包括发起审批、状态同步、回调处理等核心功能。
