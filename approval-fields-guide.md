# 钉钉对公付款审批字段说明

## 📋 审批流程字段

根据您提供的审批流程要求，我们已经实现了包含以下12个字段的完整审批表单：

### 1. 申请人 (applicant / applicantName)
- **字段名**: `applicant` (用户ID), `applicantName` (用户姓名)
- **类型**: 字符串
- **说明**: 发起审批的用户，自动从当前登录用户获取
- **示例**: `"user123"`, `"张三"`

### 2. 申请部门 (department)
- **字段名**: `department`
- **类型**: 字符串
- **说明**: 申请人所属部门，从用户信息中获取
- **示例**: `"财务部"`, `"市场部"`

### 3. 关联审批单 (relatedApprovalId)
- **字段名**: `relatedApprovalId`
- **类型**: 字符串（可选）
- **说明**: 关联的其他审批单号
- **示例**: `"REL-2024-001"`

### 4. 所属项目 (projectId / projectName)
- **字段名**: `projectId`, `projectName`
- **类型**: 字符串
- **说明**: 付款相关的项目信息
- **示例**: `"proj-001"`, `"品牌推广项目"`

### 5. 付款事由 (paymentReason)
- **字段名**: `paymentReason`
- **类型**: 字符串（必填）
- **说明**: 详细的付款原因说明
- **示例**: `"项目执行付款"`, `"供应商服务费"`

### 6. 合同签署主体 (contractEntity)
- **字段名**: `contractEntity`
- **类型**: 枚举
- **可选值**:
  - `company_a`: 公司A
  - `company_b`: 公司B
  - `subsidiary`: 子公司
  - `other`: 其他
- **示例**: `"company_a"`

### 7. 付款总额 (totalAmount)
- **字段名**: `totalAmount`
- **类型**: 数字（必填）
- **说明**: 本次申请的付款金额
- **示例**: `10000.00`

### 8. 期望付款时间 (expectedPaymentDate)
- **字段名**: `expectedPaymentDate`
- **类型**: 日期字符串（必填）
- **格式**: `YYYY-MM-DD`
- **示例**: `"2024-01-15"`

### 9. 付款方式 (paymentMethod)
- **字段名**: `paymentMethod`
- **类型**: 枚举
- **可选值**:
  - `bank_transfer`: 银行转账
  - `online_payment`: 网银支付
  - `check`: 支票
  - `cash`: 现金
  - `other`: 其他
- **示例**: `"bank_transfer"`

### 10. 收款账号 (receivingAccount)
- **字段名**: `receivingAccount`
- **类型**: 对象（必填）
- **包含字段**:
  - `accountName`: 账户名称（必填）
  - `accountNumber`: 账号（必填）
  - `bankName`: 开户银行（必填）
  - `bankCode`: 银行代码（可选）
- **示例**:
```json
{
  "accountName": "供应商公司名称",
  "accountNumber": "6228480402564890018",
  "bankName": "中国农业银行",
  "bankCode": "103"
}
```

### 11. 发票文件 (invoiceFiles)
- **字段名**: `invoiceFiles`
- **类型**: 字符串数组（可选）
- **说明**: 发票文件的URL列表
- **示例**: `["https://example.com/invoice1.pdf", "https://example.com/invoice2.pdf"]`

### 12. 附件 (attachments)
- **字段名**: `attachments`
- **类型**: 字符串数组（可选）
- **说明**: 其他相关附件的URL列表
- **示例**: `["https://example.com/contract.pdf", "https://example.com/agreement.pdf"]`

### 13. 备注 (remark)
- **字段名**: `remark`
- **类型**: 字符串（可选）
- **说明**: 额外的备注信息
- **示例**: `"第一期付款，请尽快处理"`

## 🔧 API 使用示例

### 完整请求示例
```json
POST /api/weekly-budgets/approval
{
  "weeklyBudgetId": "budget-001",
  "totalAmount": 10000,
  "paymentReason": "项目执行付款",
  "contractEntity": "company_a",
  "expectedPaymentDate": "2024-01-15",
  "paymentMethod": "bank_transfer",
  "receivingAccount": {
    "accountName": "供应商公司名称",
    "accountNumber": "6228480402564890018",
    "bankName": "中国农业银行",
    "bankCode": "103"
  },
  "relatedApprovalId": "REL-2024-001",
  "invoiceFiles": [
    "https://example.com/invoice1.pdf"
  ],
  "attachments": [
    "https://example.com/contract.pdf"
  ],
  "remark": "第一期付款，请尽快处理"
}
```

### 最简请求示例
```json
POST /api/weekly-budgets/approval
{
  "weeklyBudgetId": "budget-001",
  "totalAmount": 5000,
  "paymentReason": "紧急付款",
  "expectedPaymentDate": "2024-01-10",
  "receivingAccount": {
    "accountName": "供应商名称",
    "accountNumber": "**********",
    "bankName": "中国银行"
  }
}
```

## 📝 字段验证规则

### 必填字段
- `weeklyBudgetId`: 周预算ID
- `totalAmount`: 付款总额（> 0）
- `paymentReason`: 付款事由
- `expectedPaymentDate`: 期望付款时间
- `receivingAccount`: 收款账号信息
  - `accountName`: 账户名称
  - `accountNumber`: 账号
  - `bankName`: 开户银行

### 可选字段
- `contractEntity`: 合同签署主体（默认: company_a）
- `paymentMethod`: 付款方式（默认: bank_transfer）
- `relatedApprovalId`: 关联审批单
- `invoiceFiles`: 发票文件
- `attachments`: 附件
- `remark`: 备注

### 兼容字段
为了向后兼容，仍然支持旧版本的字段：
- `approvalAmount`: 等同于 `totalAmount`
- `reason`: 等同于 `paymentReason`

## 🔄 审批流程

1. **提交审批**: 用户填写完整的审批表单
2. **表单验证**: 系统验证必填字段和格式
3. **发起钉钉审批**: 调用钉钉API创建审批实例
4. **状态跟踪**: 系统记录审批状态变化
5. **自动回写**: 审批通过后自动更新周预算已付金额

## 🎯 注意事项

1. **文件上传**: 发票文件和附件需要先上传到文件服务器，然后传递URL
2. **用户信息**: 申请人和申请部门信息需要从用户认证信息中获取
3. **项目关联**: 系统会自动从周预算中获取项目信息
4. **金额验证**: 付款总额不能超过周预算的合同金额
5. **钉钉配置**: 需要在钉钉后台配置对应的审批模板

这个实现完全满足了您提出的12个审批字段要求，并提供了灵活的API接口和完整的类型安全保障。
