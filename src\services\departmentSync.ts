import { DatabaseService } from './database.js';
import { DingTalkService } from './dingtalk.js';

export interface DepartmentSyncOptions {
  syncIntervalHours?: number;  // 同步间隔（小时）
  batchSize?: number;          // 批量处理大小
  maxRetries?: number;         // 最大重试次数
}

export interface DepartmentInfo {
  deptId: number;
  name: string;
  parentId: number;
  createDeptGroup?: boolean;
  autoAddUser?: boolean;
  fromUnionOrg?: boolean;
  tags?: string;
  order?: number;
  deptManagerUseridList?: string[];
  outerDept?: boolean;
  outerPermitDepts?: number[];
  outerPermitUsers?: string[];
  orgDeptOwner?: string;
  deptPerimits?: number;
  userPerimits?: number;
  outerDeptOnlySelf?: boolean;
  sourceIdentifier?: string;
  ext?: string;
  hideSceneConfig?: any;
}

export class DepartmentSyncService {
  private databaseService: DatabaseService;
  private dingTalkService: DingTalkService;
  private options: Required<DepartmentSyncOptions>;

  constructor(
    databaseService: DatabaseService,
    dingTalkService: DingTalkService,
    options: DepartmentSyncOptions = {}
  ) {
    this.databaseService = databaseService;
    this.dingTalkService = dingTalkService;
    this.options = {
      syncIntervalHours: options.syncIntervalHours || 24,
      batchSize: options.batchSize || 50,
      maxRetries: options.maxRetries || 3,
    };
  }

  /**
   * 同步所有部门信息
   */
  async syncAllDepartments(): Promise<{
    success: number;
    failed: number;
    errors: Array<{ deptId: number; error: string }>;
  }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ deptId: number; error: string }>
    };

    try {
      console.log('开始同步所有部门信息...');

      // 递归获取钉钉所有层级的部门列表
      const departments = await this.dingTalkService.getAllDepartments();

      if (!departments || departments.length === 0) {
        console.log('未获取到部门信息');
        return results;
      }

      console.log(`递归获取到 ${departments.length} 个部门（包含所有层级），开始同步...`);

      // 分批处理部门同步
      for (let i = 0; i < departments.length; i += this.options.batchSize) {
        const batch = departments.slice(i, i + this.options.batchSize);
        
        const batchResults = await Promise.allSettled(
          batch.map(dept => this.syncSingleDepartment(dept))
        );

        batchResults.forEach((result, index) => {
          const dept = batch[index];
          if (!dept) {
            console.error(`批次中第 ${index} 个部门为空，跳过处理`);
            return;
          }

          if (result.status === 'fulfilled') {
            results.success++;
            console.log(`部门 ${dept.name}(${dept.dept_id}) 同步成功`);
          } else {
            results.failed++;
            results.errors.push({
              deptId: dept.dept_id,
              error: result.reason?.message || '未知错误'
            });
            console.error(`部门 ${dept.name}(${dept.dept_id}) 同步失败:`, result.reason);
          }
        });
      }

      console.log(`部门同步完成: 成功 ${results.success}, 失败 ${results.failed}`);
      return results;
      
    } catch (error) {
      console.error('同步所有部门失败:', error);
      throw error;
    }
  }

  /**
   * 同步单个部门信息
   */
  private async syncSingleDepartment(deptData: any): Promise<void> {
    let retries = 0;
    
    while (retries < this.options.maxRetries) {
      try {
        // 转换为数据库格式
        const departmentInfo: DepartmentInfo = {
          deptId: deptData.dept_id,
          name: deptData.name,
          parentId: deptData.parent_id || 0,
          createDeptGroup: deptData.create_dept_group || false,
          autoAddUser: deptData.auto_add_user || false,
          fromUnionOrg: deptData.from_union_org || false,
          tags: deptData.tags || undefined,
          order: deptData.order || 0,
          deptManagerUseridList: deptData.dept_manager_userid_list || [],
          outerDept: deptData.outer_dept || false,
          outerPermitDepts: deptData.outer_permit_depts || [],
          outerPermitUsers: deptData.outer_permit_users || [],
          orgDeptOwner: deptData.org_dept_owner || undefined,
          deptPerimits: deptData.dept_perimits || 0,
          userPerimits: deptData.user_perimits || 0,
          outerDeptOnlySelf: deptData.outer_dept_only_self || false,
          sourceIdentifier: deptData.source_identifier || undefined,
          ext: deptData.ext || undefined,
          hideSceneConfig: deptData.hide_scene_config || undefined,
        };

        // 保存到数据库
        await this.databaseService.upsertDepartment(departmentInfo);
        return; // 成功，退出重试循环
        
      } catch (error) {
        retries++;
        console.warn(`同步部门 ${deptData.name}(${deptData.dept_id}) 失败 (第${retries}次重试):`, error);
        
        if (retries >= this.options.maxRetries) {
          throw error; // 达到最大重试次数，抛出错误
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
      }
    }
  }

  /**
   * 获取部门信息（优先从本地数据库）
   */
  async getDepartmentInfo(deptId: number): Promise<{
    deptId: number;
    name: string;
    parentId: number;
    path?: string;
  } | null> {
    try {
      // 首先尝试从本地数据库获取
      const localDept = await this.databaseService.getDepartment(deptId);
      
      if (localDept) {
        // 检查是否需要同步
        const cutoffTime = new Date(Date.now() - this.options.syncIntervalHours * 60 * 60 * 1000);
        
        if (localDept.lastSyncAt && localDept.lastSyncAt > cutoffTime) {
          // 本地数据是最新的
          return {
            deptId: localDept.deptId,
            name: localDept.name,
            parentId: localDept.parentId,
            path: await this.getDepartmentPath(deptId)
          };
        }
      }

      // 本地没有数据或数据过期，从钉钉API同步
      await this.syncAllDepartments();
      
      // 重新从数据库获取
      const updatedDept = await this.databaseService.getDepartment(deptId);
      
      if (updatedDept) {
        return {
          deptId: updatedDept.deptId,
          name: updatedDept.name,
          parentId: updatedDept.parentId,
          path: await this.getDepartmentPath(deptId)
        };
      }

      return null;
      
    } catch (error) {
      console.error(`获取部门信息失败: ${deptId}`, error);
      return null;
    }
  }

  /**
   * 获取部门路径（从根部门到当前部门的完整路径）
   */
  async getDepartmentPath(deptId: number): Promise<string> {
    try {
      const path: string[] = [];
      let currentDeptId = deptId;
      
      while (currentDeptId && currentDeptId !== 1) {
        const dept = await this.databaseService.getDepartment(currentDeptId);
        if (!dept) break;
        
        path.unshift(dept.name);
        currentDeptId = dept.parentId;
      }
      
      return path.join(' / ');
    } catch (error) {
      console.error(`获取部门路径失败: ${deptId}`, error);
      return '';
    }
  }

  /**
   * 获取所有部门列表
   */
  async getAllDepartments(): Promise<Array<{
    deptId: number;
    name: string;
    parentId: number;
    path: string;
  }>> {
    try {
      const departments = await this.databaseService.getAllDepartments();
      
      const result = await Promise.all(
        departments.map(async (dept) => ({
          deptId: dept.deptId,
          name: dept.name,
          parentId: dept.parentId,
          path: await this.getDepartmentPath(dept.deptId)
        }))
      );
      
      return result.sort((a, b) => a.path.localeCompare(b.path));
    } catch (error) {
      console.error('获取所有部门列表失败:', error);
      return [];
    }
  }

  /**
   * 根据部门名称搜索部门
   */
  async searchDepartments(keyword: string): Promise<Array<{
    deptId: number;
    name: string;
    parentId: number;
    path: string;
  }>> {
    try {
      const departments = await this.databaseService.searchDepartments(keyword);
      
      const result = await Promise.all(
        departments.map(async (dept) => ({
          deptId: dept.deptId,
          name: dept.name,
          parentId: dept.parentId,
          path: await this.getDepartmentPath(dept.deptId)
        }))
      );
      
      return result.sort((a, b) => a.path.localeCompare(b.path));
    } catch (error) {
      console.error('搜索部门失败:', error);
      return [];
    }
  }

  /**
   * 获取子部门列表
   */
  async getSubDepartments(parentId: number): Promise<Array<{
    deptId: number;
    name: string;
    parentId: number;
  }>> {
    try {
      return await this.databaseService.getSubDepartments(parentId);
    } catch (error) {
      console.error(`获取子部门列表失败: ${parentId}`, error);
      return [];
    }
  }
}
