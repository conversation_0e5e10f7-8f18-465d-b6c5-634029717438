# 项目管理系统API使用指南

## 🚀 快速开始

### 1. 启动服务器
```bash
npm run dev
```
服务器将在 `http://localhost:3000` 启动

### 2. 访问API文档
- Swagger文档: `http://localhost:3000/docs`
- API文档: [API.md](./API.md)
- 快速参考: [API-Quick-Reference.md](./API-Quick-Reference.md)

### 3. 导入Postman集合
导入 `postman-collection.json` 到Postman中进行API测试

## 📋 功能概览

### 核心功能模块
- ✅ **认证管理** - 钉钉登录认证
- ✅ **项目管理** - 项目CRUD操作
- ✅ **品牌管理** - 品牌信息管理
- ✅ **收入管理** - 项目收入预测管理
- ✅ **供应商管理** - 供应商信息管理
- ✅ **周预算管理** - 项目周预算管理
- ✅ **文件管理** - 文件上传下载

### 新增功能（v1.2.0）
- 🏢 **供应商管理系统**
  - 供应商信息管理（联系方式、企业信息、财务信息）
  - 服务类型分类（达人/投流/其他）
  - 供应商评级和状态管理
  - 信用额度和付款条件管理

- 📊 **周预算管理系统**
  - 按周精细化预算管理
  - 服务内容和税率管理
  - 付款进度跟踪
  - 批量创建周期性预算
  - 预算状态流转管理

## 🧪 测试指南

### 使用测试接口（推荐）
测试接口无需认证，方便快速验证功能：

```bash
# 1. 创建供应商
curl -X POST http://localhost:3000/api/test/suppliers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试供应商",
    "serviceTypes": ["influencer"],
    "preferredTaxRate": "special_6",
    "rating": 5
  }'

# 2. 获取供应商列表
curl http://localhost:3000/api/test/suppliers

# 3. 创建周预算（需要先有项目）
curl -X POST http://localhost:3000/api/test/projects/PROJECT_ID/weekly-budgets \
  -H "Content-Type: application/json" \
  -d '{
    "title": "第1周预算",
    "weekStartDate": "2024-01-01",
    "weekEndDate": "2024-01-07",
    "serviceType": "influencer",
    "serviceContent": "达人投放服务",
    "contractAmount": 50000,
    "taxRate": "special_6"
  }'

# 4. 获取周预算列表
curl http://localhost:3000/api/test/weekly-budgets
```

### 运行自动化测试
```bash
# 运行供应商和周预算功能测试
node test-supplier-weekly-budget.js
```

## 📊 数据模型

### 供应商模型
```typescript
interface Supplier {
  id: string;
  name: string;                     // 供应商名称
  shortName?: string;               // 简称
  code?: string;                    // 供应商编码
  contactPerson?: string;           // 联系人
  contactPhone?: string;            // 联系电话
  contactEmail?: string;            // 联系邮箱
  serviceTypes: ServiceType[];      // 服务类型
  preferredTaxRate?: TaxRate;       // 首选税率
  status: SupplierStatus;           // 供应商状态
  rating?: number;                  // 评级 (1-5)
  // ... 更多字段
}
```

### 周预算模型
```typescript
interface WeeklyBudget {
  id: string;
  title: string;                    // 预算标题
  weekStartDate: Date;              // 周开始日期
  weekEndDate: Date;                // 周结束日期
  serviceType: ServiceType;         // 服务类型
  serviceContent: string;           // 服务内容描述
  contractAmount: number;           // 合同金额
  taxRate: TaxRate;                 // 税率
  paidAmount: number;               // 已付金额
  remainingAmount: number;          // 剩余金额
  status: WeeklyBudgetStatus;       // 预算状态
  projectId: string;                // 关联项目ID
  supplierId?: string;              // 关联供应商ID
  // ... 更多字段
}
```

## 🔧 配置说明

### 环境变量
```env
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/database"

# 钉钉配置
DINGTALK_APP_KEY="your_app_key"
DINGTALK_APP_SECRET="your_app_secret"

# JWT配置
JWT_SECRET="your_jwt_secret"

# 服务器配置
PORT=3000
NODE_ENV=development
```

### 数据库迁移
```bash
# 同步数据库架构
npx prisma db push

# 生成Prisma客户端
npx prisma generate

# 查看数据库
npx prisma studio
```

## 🎯 使用场景

### 1. 供应商管理场景
```javascript
// 创建供应商
const supplier = await fetch('/api/suppliers', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: '优质达人供应商',
    serviceTypes: ['influencer', 'advertising'],
    preferredTaxRate: 'special_6',
    rating: 5
  })
});

// 查询供应商
const suppliers = await fetch('/api/suppliers?serviceType=influencer&status=active');
```

### 2. 周预算管理场景
```javascript
// 创建单个周预算
const budget = await fetch(`/api/projects/${projectId}/weekly-budgets`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    title: '第1周达人投放预算',
    weekStartDate: '2024-01-01',
    weekEndDate: '2024-01-07',
    serviceType: 'influencer',
    contractAmount: 50000,
    taxRate: 'special_6'
  })
});

// 批量创建周预算
const batchBudgets = await fetch(`/api/projects/${projectId}/weekly-budgets/batch`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    serviceType: 'advertising',
    defaultContractAmount: 30000,
    defaultTaxRate: 'special_3'
  })
});

// 更新预算状态
const updatedBudget = await fetch(`/api/weekly-budgets/${budgetId}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    status: 'approved',
    paidAmount: 25000
  })
});
```

## 🔍 常见问题

### Q: 如何获取项目ID？
A: 使用 `GET /api/test/projects` 获取现有项目列表

### Q: 支持哪些服务类型？
A: `influencer`（达人服务）、`advertising`（投流服务）、`other`（其他服务）

### Q: 支持哪些税率类型？
A: `special_1`（专票1%）、`special_3`（专票3%）、`special_6`（专票6%）、`general`（普票）

### Q: 如何计算剩余金额？
A: 剩余金额 = 合同金额 - 已付金额，系统自动计算

### Q: 批量创建周预算的逻辑？
A: 根据开始和结束日期，按周自动创建预算记录，每周一个预算

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**版本**: v1.2.0  
**更新时间**: 2024-01-01  
**维护团队**: 项目管理系统开发组
