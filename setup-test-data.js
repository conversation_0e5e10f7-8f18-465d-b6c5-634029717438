// 设置测试数据
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

async function setupTestData() {
  console.log('🔧 开始设置测试数据...\n');

  try {
    // 1. 创建测试品牌
    console.log('1. 创建测试品牌...');
    const brandData = {
      name: '测试品牌',
      description: '这是一个测试品牌',
      logo: 'https://example.com/logo.png'
    };

    const brandResponse = await fetch(`${BASE_URL}/test/brands`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(brandData)
    });

    const brandResult = await brandResponse.json();
    let brandId = null;

    if (brandResult.success) {
      brandId = brandResult.data.id;
      console.log('✅ 创建品牌成功, ID:', brandId);
    } else {
      console.log('❌ 创建品牌失败:', brandResult.message);
      // 尝试获取现有品牌
      const brandsResponse = await fetch(`${BASE_URL}/test/brands`);
      const brandsResult = await brandsResponse.json();
      if (brandsResult.success && brandsResult.data.brands.length > 0) {
        brandId = brandsResult.data.brands[0].id;
        console.log('✅ 使用现有品牌, ID:', brandId);
      }
    }

    if (!brandId) {
      console.log('❌ 无法获取品牌ID，停止测试');
      return;
    }

    // 2. 创建测试项目
    console.log('\n2. 创建测试项目...');
    const projectData = {
      documentType: 'project_initiation',
      brandId: brandId,
      projectName: '测试项目 - 收入管理',
      period: {
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      },
      budget: {
        planningBudget: 1000000,
        influencerBudget: 400000,
        adBudget: 300000,
        otherBudget: 100000
      },
      cost: {
        influencerCost: 350000,
        adCost: 280000,
        otherCost: 80000,
        estimatedInfluencerRebate: 20000
      },
      executorPM: 'test-user-001',
      contentMediaIds: ['test-user-002', 'test-user-003'],
      contractType: 'annual_frame',
      settlementRules: '按月结算，每月25日前提交结算单',
      kpi: '目标曝光量：1000万，目标转化率：3%'
    };

    const projectResponse = await fetch(`${BASE_URL}/test/projects`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    });

    const projectResult = await projectResponse.json();
    let projectId = null;

    if (projectResult.success) {
      projectId = projectResult.data.id;
      console.log('✅ 创建项目成功, ID:', projectId);
    } else {
      console.log('❌ 创建项目失败:', projectResult.message);
      // 尝试获取现有项目
      const projectsResponse = await fetch(`${BASE_URL}/test/projects`);
      const projectsResult = await projectsResponse.json();
      if (projectsResult.success && projectsResult.data.projects.length > 0) {
        projectId = projectsResult.data.projects[0].id;
        console.log('✅ 使用现有项目, ID:', projectId);
      }
    }

    if (!projectId) {
      console.log('❌ 无法获取项目ID，停止测试');
      return;
    }

    // 3. 创建测试收入
    console.log('\n3. 创建测试收入...');
    const revenueData = {
      title: '第一阶段里程碑收入',
      revenueType: 'milestone',
      plannedAmount: 500000,
      plannedDate: '2024-06-30',
      milestone: '项目第一阶段完成',
      paymentTerms: '收到发票后30天内付款',
      notes: '项目启动阶段的里程碑收入'
    };

    const revenueResponse = await fetch(`${BASE_URL}/test/projects/${projectId}/revenues`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(revenueData)
    });

    const revenueResult = await revenueResponse.json();

    if (revenueResult.success) {
      console.log('✅ 创建收入成功, ID:', revenueResult.data.id);
      
      // 4. 验证数据
      console.log('\n4. 验证测试数据...');
      
      // 检查项目列表
      const checkProjectsResponse = await fetch(`${BASE_URL}/test/projects`);
      const checkProjectsResult = await checkProjectsResponse.json();
      console.log(`📊 项目总数: ${checkProjectsResult.data.total}`);
      
      // 检查收入列表
      const checkRevenuesResponse = await fetch(`${BASE_URL}/test/revenues`);
      const checkRevenuesResult = await checkRevenuesResponse.json();
      console.log(`💰 收入总数: ${checkRevenuesResult.data.total}`);
      
      console.log('\n✅ 测试数据设置完成！');
      console.log('现在可以运行 node test-revenue-api.js 进行完整测试');
      
    } else {
      console.log('❌ 创建收入失败:', revenueResult.message);
    }

  } catch (error) {
    console.error('❌ 设置测试数据时发生错误:', error.message);
  }
}

// 运行设置
setupTestData();
