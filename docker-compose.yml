version: '3.8'

services:
  cantv-ding-backend:
    image: registry.cn-hangzhou.aliyuncs.com/cantv-ding/cantv-ding-backend:${IMAGE_TAG:-latest}
    container_name: cantv-ding-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=3000
      - DATABASE_URL=${DATABASE_URL}
      - DINGTALK_APP_KEY=${DINGTALK_APP_KEY}
      - DINGTALK_APP_SECRET=${DINGTALK_APP_SECRET}
      - DINGTALK_CORP_ID=${DINGTALK_CORP_ID}
      - DINGTALK_AGENT_ID=${DINGTALK_AGENT_ID}
      - JWT_SECRET=${JWT_SECRET}
      - DINGTALK_API_BASE_URL=${DINGTALK_API_BASE_URL:-https://oapi.dingtalk.com}
      - DINGTALK_NEW_API_BASE_URL=${DINGTALK_NEW_API_BASE_URL:-https://api.dingtalk.com}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - cantv-ding-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: cantv-ding-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - cantv-ding-backend
    restart: unless-stopped
    networks:
      - cantv-ding-network

networks:
  cantv-ding-network:
    driver: bridge
