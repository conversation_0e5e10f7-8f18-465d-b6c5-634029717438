// 测试项目收入管理API
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000/api';

async function testRevenueAPI() {
  console.log('🧪 开始测试项目收入管理API...\n');

  try {
    // 1. 测试创建项目收入
    console.log('1. 测试创建项目收入...');
    const createRevenueData = {
      title: '测试里程碑收入',
      revenueType: 'milestone',
      plannedAmount: 500000,
      plannedDate: '2024-06-30',
      milestone: '项目第一阶段完成',
      paymentTerms: '收到发票后30天内付款',
      notes: '这是一个测试收入记录'
    };

    const createResponse = await fetch(`${BASE_URL}/test/projects/project-001/revenues`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(createRevenueData)
    });

    const createResult = await createResponse.json();
    console.log('创建收入响应状态:', createResponse.status);
    console.log('创建收入结果:', JSON.stringify(createResult, null, 2));

    let revenueId = null;
    if (createResult.success && createResult.data) {
      revenueId = createResult.data.id;
      console.log('✅ 创建项目收入成功, ID:', revenueId);
    } else {
      console.log('❌ 创建项目收入失败');
    }

    // 2. 测试获取收入列表
    console.log('\n2. 测试获取收入列表...');
    const listResponse = await fetch(`${BASE_URL}/test/revenues`);
    const listResult = await listResponse.json();
    
    console.log('收入列表响应状态:', listResponse.status);
    if (listResult.success) {
      console.log('✅ 获取收入列表成功');
      console.log(`📊 总收入数: ${listResult.data.total}`);
      console.log(`📄 当前页收入数: ${listResult.data.revenues.length}`);
      
      if (listResult.data.revenues.length > 0) {
        const firstRevenue = listResult.data.revenues[0];
        console.log('\n📋 第一个收入的数据结构:');
        console.log('基本信息:', {
          id: firstRevenue.id ? '✅' : '❌',
          title: firstRevenue.title ? '✅' : '❌',
          revenueType: firstRevenue.revenueType ? '✅' : '❌',
          status: firstRevenue.status ? '✅' : '❌'
        });
        
        console.log('金额信息:', {
          plannedAmount: typeof firstRevenue.plannedAmount === 'number' ? '✅' : '❌',
          actualAmount: firstRevenue.actualAmount !== undefined ? '✅' : '⚠️ 可选',
          invoiceAmount: firstRevenue.invoiceAmount !== undefined ? '✅' : '⚠️ 可选'
        });
        
        console.log('时间信息:', {
          plannedDate: firstRevenue.plannedDate ? '✅' : '❌',
          confirmedDate: firstRevenue.confirmedDate !== undefined ? '✅' : '⚠️ 可选',
          invoiceDate: firstRevenue.invoiceDate !== undefined ? '✅' : '⚠️ 可选',
          receivedDate: firstRevenue.receivedDate !== undefined ? '✅' : '⚠️ 可选'
        });
        
        console.log('业务信息:', {
          milestone: firstRevenue.milestone !== undefined ? '✅' : '⚠️ 可选',
          invoiceNumber: firstRevenue.invoiceNumber !== undefined ? '✅' : '⚠️ 可选',
          paymentTerms: firstRevenue.paymentTerms !== undefined ? '✅' : '⚠️ 可选',
          notes: firstRevenue.notes !== undefined ? '✅' : '⚠️ 可选'
        });
      }
    } else {
      console.log('❌ 获取收入列表失败:', listResult.message);
    }

    // 3. 测试获取单个收入（如果有收入ID）
    if (revenueId) {
      console.log('\n3. 测试获取单个收入...');
      const getResponse = await fetch(`${BASE_URL}/test/revenues/${revenueId}`);
      const getResult = await getResponse.json();
      
      if (getResult.success) {
        console.log('✅ 获取单个收入成功');
        console.log('收入详情:', {
          id: getResult.data.id,
          title: getResult.data.title,
          status: getResult.data.status,
          plannedAmount: getResult.data.plannedAmount
        });
      } else {
        console.log('❌ 获取单个收入失败:', getResult.message);
      }

      // 4. 测试更新收入
      console.log('\n4. 测试更新收入...');
      const updateData = {
        status: 'confirmed',
        actualAmount: 500000,
        confirmedDate: '2024-06-25',
        notes: '收入已确认，等待开票'
      };

      const updateResponse = await fetch(`${BASE_URL}/test/revenues/${revenueId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      const updateResult = await updateResponse.json();
      if (updateResult.success) {
        console.log('✅ 更新收入成功');
        console.log('更新后状态:', updateResult.data.status);
        console.log('实际金额:', updateResult.data.actualAmount);
      } else {
        console.log('❌ 更新收入失败:', updateResult.message);
      }
    }

    // 5. 测试过滤功能
    console.log('\n5. 测试过滤功能...');
    const filterResponse = await fetch(`${BASE_URL}/test/revenues?status=planned&revenueType=milestone`);
    const filterResult = await filterResponse.json();
    
    if (filterResult.success) {
      console.log('✅ 过滤功能正常');
      console.log(`📋 过滤后收入数: ${filterResult.data.revenues.length}`);
    } else {
      console.log('❌ 过滤功能失败:', filterResult.message);
    }

    // 6. 测试分页功能
    console.log('\n6. 测试分页功能...');
    const pageResponse = await fetch(`${BASE_URL}/test/revenues?page=1&pageSize=5`);
    const pageResult = await pageResponse.json();
    
    if (pageResult.success) {
      console.log('✅ 分页功能正常');
      console.log(`📄 请求页面: ${pageResult.data.page}`);
      console.log(`📊 页面大小: ${pageResult.data.pageSize}`);
      console.log(`📈 总页数: ${pageResult.data.totalPages}`);
    } else {
      console.log('❌ 分页功能失败:', pageResult.message);
    }

    // 7. 测试收入统计
    console.log('\n7. 测试收入统计...');
    const statsResponse = await fetch(`${BASE_URL}/test/revenues/stats`);
    const statsResult = await statsResponse.json();
    
    if (statsResult.success) {
      console.log('✅ 收入统计功能正常');
      console.log('统计数据:', {
        totalPlannedRevenue: statsResult.data.totalPlannedRevenue,
        totalActualRevenue: statsResult.data.totalActualRevenue,
        totalInvoicedRevenue: statsResult.data.totalInvoicedRevenue,
        totalReceivedRevenue: statsResult.data.totalReceivedRevenue
      });
    } else {
      console.log('❌ 收入统计功能失败:', statsResult.message);
    }

    // 8. 测试删除收入（如果有收入ID）
    if (revenueId) {
      console.log('\n8. 测试删除收入...');
      const deleteResponse = await fetch(`${BASE_URL}/test/revenues/${revenueId}`, {
        method: 'DELETE'
      });

      const deleteResult = await deleteResponse.json();
      if (deleteResult.success) {
        console.log('✅ 删除收入成功');
      } else {
        console.log('❌ 删除收入失败:', deleteResult.message);
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n🏁 收入管理API测试完成');
}

// 运行测试
testRevenueAPI();
