# API 快速参考手册

## 📋 基础信息

- **Base URL**: `http://localhost:3000/api`
- **Content-Type**: `application/json`
- **认证**: 暂无（后续可集成钉钉免登录）

## 🏷️ 品牌管理 API

### 获取品牌列表
```http
GET /brands?page=1&pageSize=10&status=active&keyword=搜索词
```

### 获取单个品牌
```http
GET /brands/{id}
```

### 创建品牌
```http
POST /brands
Content-Type: application/json

{
  "name": "品牌名称",
  "description": "品牌描述",
  "logo": "https://example.com/logo.png"
}
```

### 更新品牌
```http
PUT /brands
Content-Type: application/json

{
  "id": "brand-001",
  "name": "新品牌名称",
  "status": "active"
}
```

### 删除品牌
```http
DELETE /brands/{id}
```

## 📊 项目管理 API

### 获取项目列表
```http
GET /projects?page=1&pageSize=20&status=active&brandId=brand-001&keyword=项目名
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `status`: 项目状态 (`draft`, `active`, `completed`, `cancelled`)
- `brandId`: 品牌ID
- `contractType`: 合同类型 (`annual_frame`, `quarterly_frame`, `single`, `po_order`, `jing_task`)
- `executorPM`: 执行PM用户ID
- `keyword`: 项目名称关键字
- `startDate`: 开始日期 (YYYY-MM-DD)
- `endDate`: 结束日期 (YYYY-MM-DD)

### 获取单个项目
```http
GET /projects/{id}
```

### 创建项目
```http
POST /projects
Content-Type: application/json

{
  "documentType": "project_initiation",
  "brandId": "brand-001",
  "projectName": "春节营销活动",
  "period": {
    "startDate": "2024-02-01",
    "endDate": "2024-02-29"
  },
  "budget": {
    "planningBudget": 1000000.00,
    "influencerBudget": 400000.00,
    "adBudget": 300000.00,
    "otherBudget": 100000.00
  },
  "cost": {
    "influencerCost": 350000.00,
    "adCost": 280000.00,
    "otherCost": 80000.00,
    "estimatedInfluencerRebate": 20000.00
  },
  "executorPM": "user-001",
  "contentMediaIds": ["user-002", "user-003"],
  "contractType": "annual_frame",
  "settlementRules": "按月结算规则",
  "kpi": "目标KPI要求"
}
```

### 更新项目
```http
PUT /projects
Content-Type: application/json

{
  "id": "project-001",
  "projectName": "更新后的项目名称",
  "status": "active",
  "budget": {
    "planningBudget": 1200000.00
  }
}
```

### 删除项目
```http
DELETE /projects/{id}
```

## 📈 统计分析 API

### 获取项目统计
```http
GET /projects/stats
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalProjects": 10,
    "activeProjects": 5,
    "completedProjects": 3,
    "totalBudget": 5000000.00,
    "totalProfit": 1500000.00,
    "averageGrossMargin": 30.00,
    "projectsByBrand": [
      {
        "brandId": "brand-001",
        "brandName": "可口可乐",
        "count": 5,
        "totalBudget": 3000000.00
      }
    ],
    "projectsByContractType": [
      {
        "contractType": "annual_frame",
        "count": 3,
        "totalBudget": 2000000.00
      }
    ]
  }
}
```

## 📎 文件上传 API

### 上传文件
```http
POST /upload
Content-Type: multipart/form-data

file: [文件对象]
```

**支持的文件类型**:
- 文档: `.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`
- 图片: `.png`, `.jpg`, `.jpeg`, `.gif`
- 其他: `.txt`, `.zip`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "file-001",
    "filename": "contract_20240115.pdf",
    "originalName": "合同文件.pdf",
    "size": 1024000,
    "mimeType": "application/pdf",
    "url": "/uploads/contract_20240115.pdf"
  }
}
```

## 🔧 枚举值参考

### 单据类型 (DocumentType)
- `project_initiation`: 项目立项表

### 项目状态 (ProjectStatus)
- `draft`: 草稿
- `active`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

### 品牌状态 (BrandStatus)
- `active`: 启用
- `inactive`: 禁用

### 合同类型 (ContractType)
- `annual_frame`: 年框
- `quarterly_frame`: 季框
- `single`: 单次
- `po_order`: PO单
- `jing_task`: 京任务

## ❌ 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": "详细错误信息（可选）"
}
```

### 常见错误码
- `400 INVALID_REQUEST`: 请求参数无效
- `401 UNAUTHORIZED`: 未授权访问
- `403 FORBIDDEN`: 权限不足
- `404 NOT_FOUND`: 资源不存在
- `409 CONFLICT`: 资源冲突
- `422 VALIDATION_ERROR`: 数据验证失败
- `500 INTERNAL_ERROR`: 服务器内部错误

## 💻 快速测试

### 使用 cURL

```bash
# 获取品牌列表
curl -X GET "http://localhost:3000/api/brands"

# 创建品牌
curl -X POST "http://localhost:3000/api/brands" \
  -H "Content-Type: application/json" \
  -d '{"name": "测试品牌", "description": "测试描述"}'

# 获取项目统计
curl -X GET "http://localhost:3000/api/projects/stats"

# 上传文件
curl -X POST "http://localhost:3000/api/upload" \
  -F "file=@/path/to/file.pdf"
```

### 使用 JavaScript

```javascript
// 获取项目列表
const response = await fetch('/api/projects?page=1&pageSize=20');
const data = await response.json();

// 创建品牌
const brandResponse = await fetch('/api/brands', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: '新品牌',
    description: '品牌描述'
  })
});

// 文件上传
const formData = new FormData();
formData.append('file', fileInput.files[0]);
const uploadResponse = await fetch('/api/upload', {
  method: 'POST',
  body: formData
});
```

## 🔗 相关链接

- [完整API文档](api-documentation.md)
- [在线API测试](http://localhost:3000/api-docs.html)
- [TypeScript类型定义](api-types.ts)
- [JavaScript客户端](api-client.js)
- [Postman集合](postman-collection.json)
- [项目管理页面](http://localhost:3000/project-management.html)

## 📝 注意事项

1. **金额字段**: 所有金额字段使用数字类型，支持小数点后2位
2. **日期格式**: 日期字段使用 `YYYY-MM-DD` 格式
3. **用户ID**: 钉钉用户ID，字符串类型
4. **分页**: 默认页码从1开始，最大每页100条记录
5. **搜索**: 支持模糊搜索，不区分大小写
6. **排序**: 支持多字段排序，默认按创建时间倒序

## 🧪 测试建议

1. **先测试基础接口**: 从获取列表开始
2. **创建测试数据**: 先创建品牌，再创建项目
3. **验证关联关系**: 确保品牌和项目的关联正确
4. **测试边界情况**: 空数据、大数据量、特殊字符等
5. **检查错误处理**: 测试各种错误场景
