# 周预算审批验证功能测试

## 🎯 功能说明

为了防止重复创建审批，我们在周预算审批创建接口中添加了验证逻辑：
- **每个周预算只能创建一次审批**
- 如果周预算的 `approvalStatus` 不为 `NONE`，则不允许创建新的审批

## ✅ 已实现的验证逻辑

### 1. 验证步骤
1. 检查周预算是否存在
2. 检查周预算的审批状态
3. 如果状态不为 `NONE`，返回相应的错误信息

### 2. 审批状态说明
- `NONE`: 无审批（可以创建新审批）
- `PENDING`: 审批中（不能重复创建）
- `APPROVED`: 已通过（不能重复创建）
- `REJECTED`: 已拒绝（需要管理员处理）
- `CANCELLED`: 已撤销（需要管理员处理）

### 3. 错误响应格式
```json
{
  "success": false,
  "message": "该周预算已有审批在进行中，不能重复创建",
  "code": "APPROVAL_ALREADY_EXISTS",
  "data": {
    "currentApprovalStatus": "PENDING"
  }
}
```

## 🧪 测试用例

### 测试用例1：正常创建审批
**前置条件**: 周预算存在且 `approvalStatus` 为 `NONE`
**预期结果**: 成功创建审批

```bash
POST /api/weekly-budgets/approval
{
  "weeklyBudgetId": "budget_with_no_approval",
  "totalAmount": 10000,
  "paymentReason": "项目费用支付",
  "department": 1,
  "contractEntity": "company_a",
  "expectedPaymentDate": "2024-01-15",
  "paymentMethod": "bank_transfer",
  "receivingAccount": {
    "accountName": "收款方",
    "accountNumber": "*********",
    "bankName": "工商银行"
  }
}
```

### 测试用例2：重复创建审批（审批中）
**前置条件**: 周预算存在且 `approvalStatus` 为 `PENDING`
**预期结果**: 返回错误，提示审批进行中

```bash
POST /api/weekly-budgets/approval
{
  "weeklyBudgetId": "budget_with_pending_approval",
  "totalAmount": 10000,
  "paymentReason": "项目费用支付",
  // ... 其他参数
}

# 预期响应
{
  "success": false,
  "message": "该周预算已有审批在进行中，不能重复创建",
  "code": "APPROVAL_ALREADY_EXISTS",
  "data": {
    "currentApprovalStatus": "PENDING"
  }
}
```

### 测试用例3：已通过审批的周预算
**前置条件**: 周预算存在且 `approvalStatus` 为 `APPROVED`
**预期结果**: 返回错误，提示审批已通过

```bash
# 预期响应
{
  "success": false,
  "message": "该周预算的审批已通过，不能重复创建",
  "code": "APPROVAL_ALREADY_EXISTS",
  "data": {
    "currentApprovalStatus": "APPROVED"
  }
}
```

### 测试用例4：周预算不存在
**前置条件**: 提供不存在的周预算ID
**预期结果**: 返回404错误

```bash
# 预期响应
{
  "success": false,
  "message": "周预算不存在",
  "code": "WEEKLY_BUDGET_NOT_FOUND"
}
```

## 🔧 实现细节

### 代码位置
- 文件: `src/controllers/weeklyBudget.ts`
- 方法: `createPaymentApproval`
- 行数: 322-349

### 关键代码片段
```typescript
// 验证周预算是否已经创建过审批
const weeklyBudget = await this.projectService.getWeeklyBudget(approvalData.weeklyBudgetId);
if (!weeklyBudget) {
  return reply.status(404).send({
    success: false,
    message: '周预算不存在',
    code: 'WEEKLY_BUDGET_NOT_FOUND'
  });
}

// 检查是否已经有审批在进行中或已完成
if (weeklyBudget.approvalStatus !== 'NONE') {
  const statusMessages: Record<string, string> = {
    'PENDING': '该周预算已有审批在进行中，不能重复创建',
    'APPROVED': '该周预算的审批已通过，不能重复创建',
    'REJECTED': '该周预算的审批已被拒绝，如需重新申请请联系管理员',
    'CANCELLED': '该周预算的审批已取消，如需重新申请请联系管理员'
  };
  
  return reply.status(400).send({
    success: false,
    message: statusMessages[weeklyBudget.approvalStatus] || '该周预算已有审批记录，不能重复创建',
    code: 'APPROVAL_ALREADY_EXISTS',
    data: {
      currentApprovalStatus: weeklyBudget.approvalStatus
    }
  });
}
```

## 📋 测试检查清单

- [ ] 正常创建审批（状态为NONE）
- [ ] 重复创建审批被阻止（状态为PENDING）
- [ ] 已通过审批的周预算不能重复创建（状态为APPROVED）
- [ ] 已拒绝审批的周预算不能重复创建（状态为REJECTED）
- [ ] 已取消审批的周预算不能重复创建（状态为CANCELLED）
- [ ] 不存在的周预算返回404错误
- [ ] 错误响应包含正确的错误码和当前状态信息
- [ ] API文档已更新，包含验证说明和错误示例

## 🎉 总结

这个验证功能确保了：
1. **数据一致性**: 防止同一个周预算创建多个审批实例
2. **业务逻辑正确性**: 符合实际业务流程，一个周预算对应一个审批
3. **用户体验**: 提供清晰的错误信息，帮助用户理解为什么不能创建审批
4. **系统稳定性**: 避免重复数据和潜在的数据冲突

现在您的周预算审批系统已经具备了完善的重复创建验证功能！🚀
