import { DatabaseService } from './database.js';
import { Permission, Prisma } from '@prisma/client';

export interface CreatePermissionData {
  name: string;
  displayName: string;
  description?: string;
  module: string;
  action: string;
  resource?: string;
  isSystem?: boolean;
}

export interface UpdatePermissionData {
  displayName?: string;
  description?: string;
  module?: string;
  action?: string;
  resource?: string;
}

export interface PermissionModule {
  module: string;
  permissions: Permission[];
}

export class PermissionService {
  constructor(private databaseService: DatabaseService) {}

  /**
   * 创建权限
   */
  async createPermission(data: CreatePermissionData): Promise<Permission> {
    try {
      const permission = await this.databaseService.client.permission.create({
        data: {
          name: data.name,
          displayName: data.displayName,
          description: data.description,
          module: data.module,
          action: data.action,
          resource: data.resource,
          isSystem: data.isSystem || false,
        },
      });

      console.log(`权限创建成功: ${permission.name} (${permission.id})`);
      return permission;
    } catch (error) {
      console.error('创建权限失败:', error);
      throw error;
    }
  }

  /**
   * 更新权限
   */
  async updatePermission(permissionId: string, data: UpdatePermissionData): Promise<Permission> {
    try {
      // 检查权限是否存在
      const existingPermission = await this.getPermissionById(permissionId);
      if (!existingPermission) {
        throw new Error(`权限不存在: ${permissionId}`);
      }

      // 系统权限不允许修改核心字段
      if (existingPermission.isSystem && (data.module || data.action)) {
        throw new Error('系统权限的模块和操作不能被修改');
      }

      const permission = await this.databaseService.client.permission.update({
        where: { id: permissionId },
        data: {
          displayName: data.displayName,
          description: data.description,
          module: data.module,
          action: data.action,
          resource: data.resource,
          updatedAt: new Date(),
        },
      });

      console.log(`权限更新成功: ${permission.name} (${permission.id})`);
      return permission;
    } catch (error) {
      console.error('更新权限失败:', error);
      throw error;
    }
  }

  /**
   * 删除权限
   */
  async deletePermission(permissionId: string): Promise<void> {
    try {
      // 检查权限是否存在
      const existingPermission = await this.getPermissionById(permissionId);
      if (!existingPermission) {
        throw new Error(`权限不存在: ${permissionId}`);
      }

      // 系统权限不允许删除
      if (existingPermission.isSystem) {
        throw new Error('系统权限不能被删除');
      }

      // 检查是否有角色正在使用此权限
      const rolePermissionCount = await this.databaseService.client.rolePermission.count({
        where: { permissionId },
      });

      if (rolePermissionCount > 0) {
        throw new Error('权限正在被角色使用，无法删除');
      }

      await this.databaseService.client.permission.delete({
        where: { id: permissionId },
      });

      console.log(`权限删除成功: ${existingPermission.name} (${permissionId})`);
    } catch (error) {
      console.error('删除权限失败:', error);
      throw error;
    }
  }

  /**
   * 获取权限详情
   */
  async getPermissionById(permissionId: string): Promise<Permission | null> {
    try {
      return await this.databaseService.client.permission.findUnique({
        where: { id: permissionId },
      });
    } catch (error) {
      console.error('获取权限详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取权限列表
   */
  async getPermissions(options: {
    page?: number;
    pageSize?: number;
    module?: string;
    action?: string;
    isSystem?: boolean;
    search?: string;
  } = {}): Promise<{
    permissions: Permission[];
    total: number;
    page: number;
    pageSize: number;
  }> {
    try {
      const {
        page = 1,
        pageSize = 50,
        module,
        action,
        isSystem,
        search,
      } = options;

      const where: Prisma.PermissionWhereInput = {};

      if (module) {
        where.module = module;
      }

      if (action) {
        where.action = action;
      }

      if (isSystem !== undefined) {
        where.isSystem = isSystem;
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { displayName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [permissions, total] = await Promise.all([
        this.databaseService.client.permission.findMany({
          where,
          orderBy: [
            { module: 'asc' },
            { action: 'asc' },
            { name: 'asc' },
          ],
          skip: (page - 1) * pageSize,
          take: pageSize,
        }),
        this.databaseService.client.permission.count({ where }),
      ]);

      return {
        permissions,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取权限列表失败:', error);
      throw error;
    }
  }

  /**
   * 按模块分组获取权限
   */
  async getPermissionsByModule(): Promise<PermissionModule[]> {
    try {
      const permissions = await this.databaseService.client.permission.findMany({
        orderBy: [
          { module: 'asc' },
          { action: 'asc' },
          { name: 'asc' },
        ],
      });

      // 按模块分组
      const moduleMap = new Map<string, Permission[]>();
      
      permissions.forEach(permission => {
        if (!moduleMap.has(permission.module)) {
          moduleMap.set(permission.module, []);
        }
        moduleMap.get(permission.module)!.push(permission);
      });

      // 转换为数组格式
      return Array.from(moduleMap.entries()).map(([module, permissions]) => ({
        module,
        permissions,
      }));
    } catch (error) {
      console.error('按模块获取权限失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有模块列表
   */
  async getModules(): Promise<string[]> {
    try {
      const result = await this.databaseService.client.permission.findMany({
        select: { module: true },
        distinct: ['module'],
        orderBy: { module: 'asc' },
      });

      return result.map(item => item.module);
    } catch (error) {
      console.error('获取模块列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有操作类型列表
   */
  async getActions(): Promise<string[]> {
    try {
      const result = await this.databaseService.client.permission.findMany({
        select: { action: true },
        distinct: ['action'],
        orderBy: { action: 'asc' },
      });

      return result.map(item => item.action);
    } catch (error) {
      console.error('获取操作类型列表失败:', error);
      throw error;
    }
  }

  /**
   * 检查权限名称是否可用
   */
  async isPermissionNameAvailable(name: string, excludePermissionId?: string): Promise<boolean> {
    try {
      const where: Prisma.PermissionWhereInput = { name };
      
      if (excludePermissionId) {
        where.id = { not: excludePermissionId };
      }

      const existingPermission = await this.databaseService.client.permission.findFirst({
        where,
      });

      return !existingPermission;
    } catch (error) {
      console.error('检查权限名称可用性失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建权限
   */
  async createPermissionsBatch(permissions: CreatePermissionData[]): Promise<Permission[]> {
    try {
      const results: Permission[] = [];

      for (const permissionData of permissions) {
        // 检查权限是否已存在
        const existing = await this.databaseService.client.permission.findUnique({
          where: { name: permissionData.name },
        });

        if (!existing) {
          const permission = await this.createPermission(permissionData);
          results.push(permission);
        } else {
          console.log(`权限已存在，跳过创建: ${permissionData.name}`);
          results.push(existing);
        }
      }

      console.log(`批量创建权限完成，共处理 ${permissions.length} 个权限，新创建 ${results.length} 个`);
      return results;
    } catch (error) {
      console.error('批量创建权限失败:', error);
      throw error;
    }
  }
}
