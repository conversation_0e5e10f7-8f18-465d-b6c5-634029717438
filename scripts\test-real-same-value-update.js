// 测试真实的相同值更新场景
import { ProjectService } from '../dist/services/project.js';

async function testRealSameValueUpdate() {
  try {
    console.log('🧪 测试真实的相同值更新场景...');
    
    const projectService = new ProjectService();
    
    // 1. 创建测试项目
    console.log('\n📝 步骤1: 创建测试项目...');
    
    const createRequest = {
      documentType: 'PROJECT_INITIATION',
      brandId: 'cmc2xx4u80000krg8mlwx4kf3',
      projectName: `相同值更新测试-${Date.now()}`,
      period: {
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      },
      budget: {
        planningBudget: 100000,
        influencerBudget: 50000,
        adBudget: 30000,
        otherBudget: 20000
      },
      cost: {
        influencerCost: 45000,
        adCost: 25000,
        otherCost: 15000,
        estimatedInfluencerRebate: 5000
      },
      executorPM: 'test-user-001',
      contentMediaIds: ['test-user-002'],
      contractType: 'SINGLE',
      contractSigningStatus: 'NO_CONTRACT',
      settlementRules: '测试结算规则',
      kpi: '测试KPI'
    };
    
    const createdProject = await projectService.createProject(
      createRequest, 
      'test-user-001',
      { ip: '127.0.0.1', userAgent: 'Same Value Test' }
    );
    
    console.log(`✅ 项目创建成功: ${createdProject.id}`);
    
    // 等待变更记录写入
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 2. 第一次更新 - 真实变更
    console.log('\n🔄 步骤2: 第一次更新（真实变更）...');
    
    const updateRequest1 = {
      id: createdProject.id,
      projectName: `更新后的项目名称-${Date.now()}`,
      budget: {
        planningBudget: 150000, // 变更
        influencerBudget: 50000, // 保持不变
        adBudget: 30000, // 保持不变
        otherBudget: 20000 // 保持不变
      },
      contractSigningStatus: 'PENDING' // 变更
    };
    
    await projectService.updateProject(
      updateRequest1,
      'test-user-002',
      { ip: '127.0.0.1', userAgent: 'Same Value Test' }
    );
    
    console.log(`✅ 第一次更新完成`);
    
    // 等待变更记录写入
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 查询第一次更新的变更记录
    const changeLogs1 = await projectService.getProjectChangeLogsByProjectId(createdProject.id);
    const updateLog1 = changeLogs1.changeLogs.find(log => log.changeType === 'UPDATE');
    
    if (updateLog1) {
      console.log(`✅ 第一次更新变更记录:`);
      console.log(`   - 变更字段: [${updateLog1.changedFields.join(', ')}]`);
    }
    
    // 3. 第二次更新 - 相同值更新
    console.log('\n⚪ 步骤3: 第二次更新（相同值，应该不产生变更记录）...');
    
    const updateRequest2 = {
      id: createdProject.id,
      projectName: updateRequest1.projectName, // 与当前值相同
      budget: {
        planningBudget: 150000, // 与当前值相同
        influencerBudget: 50000, // 与当前值相同
        adBudget: 30000, // 与当前值相同
        otherBudget: 20000 // 与当前值相同
      },
      contractSigningStatus: 'PENDING', // 与当前值相同
      executorPM: 'test-user-001', // 与当前值相同
      settlementRules: '测试结算规则' // 与当前值相同
    };
    
    await projectService.updateProject(
      updateRequest2,
      'test-user-003',
      { ip: '127.0.0.1', userAgent: 'Same Value Test' }
    );
    
    console.log(`✅ 第二次更新（相同值）完成`);
    
    // 等待处理
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 4. 验证变更记录
    console.log('\n📊 步骤4: 验证变更记录...');
    
    const changeLogs2 = await projectService.getProjectChangeLogsByProjectId(createdProject.id);
    const updateLogs = changeLogs2.changeLogs.filter(log => log.changeType === 'UPDATE');
    
    console.log(`   总变更记录数: ${changeLogs2.changeLogs.length}`);
    console.log(`   UPDATE记录数: ${updateLogs.length}`);
    
    // 分析每个UPDATE记录
    updateLogs.forEach((log, index) => {
      console.log(`   UPDATE记录${index + 1}:`);
      console.log(`     - 操作人: ${log.operatorName}`);
      console.log(`     - 变更字段: [${log.changedFields.join(', ')}]`);
      console.log(`     - 时间: ${log.createdAt}`);
    });
    
    // 5. 验证结果
    console.log('\n✅ 结果验证:');
    
    if (updateLogs.length === 1) {
      console.log('✅ 相同值更新测试通过：');
      console.log('   - 第一次真实更新产生了变更记录');
      console.log('   - 第二次相同值更新没有产生新的变更记录');
      console.log('   - 智能字段对比正确工作');
    } else if (updateLogs.length === 2) {
      const secondUpdate = updateLogs[0]; // 最新的记录
      if (secondUpdate.changedFields.length === 0) {
        console.log('⚠️ 相同值更新产生了空变更记录（可以优化）');
      } else {
        console.log('❌ 相同值更新错误地产生了变更记录：');
        console.log(`   - 错误记录的变更字段: [${secondUpdate.changedFields.join(', ')}]`);
      }
    } else {
      console.log(`❌ 意外的UPDATE记录数量: ${updateLogs.length}`);
    }
    
    // 6. 清理测试数据
    console.log('\n🧹 步骤5: 清理测试数据...');
    await projectService.deleteProject(
      createdProject.id,
      'test-user-001',
      { ip: '127.0.0.1', userAgent: 'Same Value Test' }
    );
    console.log('✅ 测试数据清理完成');
    
    console.log('\n🎉 相同值更新测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testRealSameValueUpdate();
