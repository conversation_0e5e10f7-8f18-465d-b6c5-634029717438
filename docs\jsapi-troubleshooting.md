# JSAPI问题排查指南

## 🚨 常见错误

### 错误码 9: "jsapi ticket 读取失败"

这是最常见的JSAPI配置错误，通常由以下原因引起：

#### 🔍 问题诊断

使用我们的诊断工具快速定位问题：

```bash
npm run diagnose
```

#### 🛠️ 解决步骤

1. **检查应用配置**
   ```bash
   # 确认环境变量配置正确
   cat .env | grep DINGTALK
   ```
   
   必需的配置项：
   - `DINGTALK_APP_KEY`: 应用的AppKey
   - `DINGTALK_APP_SECRET`: 应用的AppSecret  
   - `DINGTALK_CORP_ID`: 企业的CorpId
   - `DINGTALK_AGENT_ID`: 应用的AgentId

2. **验证应用状态**
   - 登录钉钉开放平台
   - 确认应用已启用
   - 检查应用权限设置

3. **测试网络连接**
   ```bash
   curl -I https://oapi.dingtalk.com
   ```

4. **手动测试API调用**
   ```bash
   # 测试获取访问令牌
   curl "https://oapi.dingtalk.com/gettoken?appkey=YOUR_APP_KEY&appsecret=YOUR_APP_SECRET"
   
   # 测试获取JSAPI票据
   curl "https://oapi.dingtalk.com/get_jsapi_ticket?access_token=YOUR_ACCESS_TOKEN"
   ```

#### 🔧 代码修复

我们已经修复了代码中的问题：

1. **修正签名生成方法**：
   - 原来错误地使用了`appSecret`而不是`jsapi_ticket`
   - 现在正确使用`getJSAPITicket()`获取票据

2. **改进错误处理**：
   - 添加详细的错误日志
   - 区分不同类型的错误
   - 提供具体的解决建议

3. **异步方法调用**：
   - 修正了控制器中的异步调用
   - 确保正确等待票据获取完成

## 🔐 签名算法验证

### 正确的签名流程

1. **获取访问令牌**
   ```
   GET https://oapi.dingtalk.com/gettoken?appkey={appkey}&appsecret={appsecret}
   ```

2. **获取JSAPI票据**
   ```
   GET https://oapi.dingtalk.com/get_jsapi_ticket?access_token={access_token}
   ```

3. **生成签名**
   ```javascript
   const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`;
   const signature = crypto.createHash('sha1').update(string1, 'utf8').digest('hex');
   ```

### 签名参数说明

- **jsapi_ticket**: 从钉钉API获取的票据
- **noncestr**: 随机字符串
- **timestamp**: 时间戳（毫秒）
- **url**: 当前页面URL（不包含fragment）

⚠️ **重要**: 参数必须按字母顺序排列！

## 🌐 网络和权限问题

### 1. 网络连接问题

**症状**: 连接超时或无法访问钉钉API

**解决方案**:
- 检查防火墙设置
- 确认服务器出口IP
- 验证DNS解析

### 2. 应用权限问题

**症状**: 40001、40014等错误码

**解决方案**:
- 检查应用类型（必须是企业内部应用）
- 确认应用权限配置
- 验证AppKey和AppSecret

### 3. 域名白名单问题

**症状**: 在钉钉客户端中无法访问

**解决方案**:
- 在钉钉开放平台配置可信域名
- 确保使用HTTPS协议
- 验证域名格式正确

## 🧪 测试和验证

### 1. 使用诊断工具

```bash
# 运行完整诊断
npm run diagnose

# 查看详细帮助
npm run diagnose -- --help
```

### 2. 使用API测试工具

```bash
# 测试所有API接口
npm run test:api

# 测试特定接口
curl http://localhost:3000/api/auth/jsapi-signature?url=https://example.com
```

### 3. 使用网页测试

访问官方演示页面，点击"🔍 检查API状态"进行实时测试。

## 📋 检查清单

### 环境配置检查

- [ ] `.env`文件存在且配置正确
- [ ] 所有必需的环境变量已设置
- [ ] AppKey和AppSecret有效
- [ ] CorpId和AgentId正确

### 应用配置检查

- [ ] 应用已在钉钉开放平台创建
- [ ] 应用状态为"启用"
- [ ] 应用类型为"企业内部应用"
- [ ] 应用权限配置正确

### 网络配置检查

- [ ] 服务器可以访问钉钉API
- [ ] 防火墙允许HTTPS出站连接
- [ ] DNS解析正常
- [ ] 服务器出口IP已配置（如需要）

### 代码配置检查

- [ ] 使用正确的签名算法
- [ ] URL处理正确（清理调试参数）
- [ ] 错误处理完善
- [ ] 日志记录详细

## 🔧 高级调试

### 1. 启用详细日志

在`.env`文件中添加：
```env
DEBUG=dingtalk:*
LOG_LEVEL=debug
```

### 2. 使用网络抓包

```bash
# 使用tcpdump抓包
sudo tcpdump -i any -w dingtalk.pcap host oapi.dingtalk.com

# 使用Wireshark分析
wireshark dingtalk.pcap
```

### 3. 模拟钉钉环境

```javascript
// 在浏览器控制台中模拟钉钉环境
window.dd = {
  config: function(config) {
    console.log('DD Config:', config);
  },
  ready: function(callback) {
    setTimeout(callback, 100);
  },
  error: function(callback) {
    // 模拟错误
  }
};
```

## 📚 参考资料

### 官方文档

- [钉钉开放平台](https://open.dingtalk.com/)
- [JSAPI概述](https://open.dingtalk.com/document/orgapp/jsapi-overview)
- [免登流程](https://open.dingtalk.com/document/orgapp/logon-free-process)

### 错误码参考

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 9 | jsapi ticket读取失败 | 检查应用配置和网络连接 |
| 40001 | access_token无效 | 重新获取访问令牌 |
| 40014 | 不合法的access_token | 检查AppKey和AppSecret |
| 60011 | 无效的免登码 | 免登码只能使用一次 |
| 60012 | 免登码已过期 | 重新获取免登码 |

### 工具和脚本

- `npm run diagnose` - JSAPI问题诊断
- `npm run test:api` - API接口测试
- `npm run quick-start` - 一键启动和测试

## 💡 最佳实践

1. **开发阶段**
   - 使用诊断工具定期检查配置
   - 启用详细日志记录
   - 在多个环境中测试

2. **部署阶段**
   - 确认生产环境配置正确
   - 测试网络连接和权限
   - 配置监控和告警

3. **维护阶段**
   - 定期检查访问令牌有效性
   - 监控API调用成功率
   - 及时更新应用配置

## 🆘 获取帮助

如果问题仍然存在：

1. **运行诊断工具**获取详细信息
2. **查看服务器日志**了解具体错误
3. **检查钉钉开放平台**的应用状态
4. **联系钉钉技术支持**获取官方帮助

记住：大多数JSAPI问题都是配置问题，仔细检查每个配置项通常能解决问题。
