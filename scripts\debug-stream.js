#!/usr/bin/env node

/**
 * 调试钉钉Stream连接问题
 */

import fetch from 'node-fetch';

async function debugStreamConnection() {
  console.log('🔍 开始调试钉钉Stream连接...');
  
  const clientId = process.env.DINGTALK_APP_KEY;
  const clientSecret = process.env.DINGTALK_APP_SECRET;
  
  if (!clientId || !clientSecret) {
    console.error('❌ 缺少环境变量 DINGTALK_APP_KEY 或 DINGTALK_APP_SECRET');
    process.exit(1);
  }
  
  console.log('🔑 配置信息:');
  console.log('- ClientID:', clientId.substring(0, 8) + '...');
  console.log('- ClientSecret:', '***');
  
  try {
    // 1. 获取访问令牌
    console.log('\n📝 步骤1: 获取访问令牌...');
    const tokenResponse = await fetch('https://oapi.dingtalk.com/gettoken', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      params: new URLSearchParams({
        appkey: clientId,
        appsecret: clientSecret
      })
    });
    
    if (!tokenResponse.ok) {
      throw new Error(`获取令牌失败: ${tokenResponse.status} ${tokenResponse.statusText}`);
    }
    
    const tokenData = await tokenResponse.json();
    console.log('📥 令牌响应:', tokenData);
    
    if (tokenData.errcode !== 0) {
      throw new Error(`获取令牌失败: ${tokenData.errmsg}`);
    }
    
    const accessToken = tokenData.access_token;
    console.log('✅ 访问令牌获取成功');
    
    // 2. 尝试获取Stream端点
    console.log('\n📝 步骤2: 获取Stream连接端点...');
    
    const requestBody = {
      clientId: clientId,
      clientSecret: clientSecret,
      ua: 'DingTalkStream/2.0.0 Node.js Debug',
      subscriptions: [
        {
          type: 'CALLBACK',
          topic: '*'
        }
      ]
    };
    
    console.log('📤 请求体:', {
      ...requestBody,
      clientSecret: '***'
    });
    
    const streamResponse = await fetch('https://api.dingtalk.com/v1.0/gateway/connections/open', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-acs-dingtalk-access-token': accessToken
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('📥 Stream响应状态:', streamResponse.status, streamResponse.statusText);
    console.log('📥 Stream响应头:', Object.fromEntries(streamResponse.headers.entries()));
    
    const streamData = await streamResponse.text();
    console.log('📥 Stream响应内容:', streamData);
    
    if (!streamResponse.ok) {
      console.error('❌ 获取Stream端点失败');
      
      // 尝试解析错误信息
      try {
        const errorData = JSON.parse(streamData);
        console.error('❌ 错误详情:', errorData);
      } catch (e) {
        console.error('❌ 无法解析错误响应');
      }
      
      return;
    }
    
    const streamJson = JSON.parse(streamData);
    console.log('✅ Stream端点获取成功:', streamJson);
    
    // 3. 测试WebSocket连接
    if (streamJson.endpoint) {
      console.log('\n📝 步骤3: 测试WebSocket连接...');
      console.log('🔗 连接端点:', streamJson.endpoint);
      
      // 这里我们只是验证端点格式，不实际连接
      const url = new URL(streamJson.endpoint);
      console.log('✅ 端点格式验证通过:', {
        protocol: url.protocol,
        host: url.host,
        pathname: url.pathname
      });
    }
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
    
    if (error.cause) {
      console.error('❌ 错误原因:', error.cause);
    }
    
    if (error.response) {
      console.error('❌ 响应状态:', error.response.status);
      console.error('❌ 响应内容:', await error.response.text());
    }
  }
}

// 修复fetch URL参数问题
async function fetchWithParams(url, options = {}) {
  if (options.params) {
    const urlObj = new URL(url);
    Object.entries(options.params).forEach(([key, value]) => {
      urlObj.searchParams.append(key, value);
    });
    url = urlObj.toString();
    delete options.params;
  }
  return fetch(url, options);
}

// 重新实现获取令牌的部分
async function debugStreamConnection2() {
  console.log('🔍 开始调试钉钉Stream连接...');
  
  const clientId = process.env.DINGTALK_APP_KEY;
  const clientSecret = process.env.DINGTALK_APP_SECRET;
  
  if (!clientId || !clientSecret) {
    console.error('❌ 缺少环境变量 DINGTALK_APP_KEY 或 DINGTALK_APP_SECRET');
    process.exit(1);
  }
  
  console.log('🔑 配置信息:');
  console.log('- ClientID:', clientId.substring(0, 8) + '...');
  console.log('- ClientSecret:', '***');
  
  try {
    // 1. 获取访问令牌
    console.log('\n📝 步骤1: 获取访问令牌...');
    const tokenUrl = `https://oapi.dingtalk.com/gettoken?appkey=${clientId}&appsecret=${clientSecret}`;
    
    const tokenResponse = await fetch(tokenUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!tokenResponse.ok) {
      throw new Error(`获取令牌失败: ${tokenResponse.status} ${tokenResponse.statusText}`);
    }
    
    const tokenData = await tokenResponse.json();
    console.log('📥 令牌响应:', tokenData);
    
    if (tokenData.errcode !== 0) {
      throw new Error(`获取令牌失败: ${tokenData.errmsg}`);
    }
    
    const accessToken = tokenData.access_token;
    console.log('✅ 访问令牌获取成功');
    
    // 2. 尝试获取Stream端点
    console.log('\n📝 步骤2: 获取Stream连接端点...');
    
    const requestBody = {
      clientId: clientId,
      clientSecret: clientSecret,
      ua: 'DingTalkStream/2.0.0 Node.js Debug',
      subscriptions: [
        {
          type: 'CALLBACK',
          topic: '*'
        }
      ]
    };
    
    console.log('📤 请求体:', {
      ...requestBody,
      clientSecret: '***'
    });
    
    const streamResponse = await fetch('https://api.dingtalk.com/v1.0/gateway/connections/open', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-acs-dingtalk-access-token': accessToken
      },
      body: JSON.stringify(requestBody)
    });
    
    console.log('📥 Stream响应状态:', streamResponse.status, streamResponse.statusText);
    
    const streamData = await streamResponse.text();
    console.log('📥 Stream响应内容:', streamData);
    
    if (!streamResponse.ok) {
      console.error('❌ 获取Stream端点失败');
      return;
    }
    
    const streamJson = JSON.parse(streamData);
    console.log('✅ Stream端点获取成功:', streamJson);
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  }
}

// 运行调试
debugStreamConnection2()
  .then(() => {
    console.log('\n✅ 调试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 调试失败:', error);
    process.exit(1);
  });
