-- CreateTable
CREATE TABLE "departments" (
    "deptId" INTEGER NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "parentId" INTEGER NOT NULL DEFAULT 1,
    "createDeptGroup" BOOLEAN NOT NULL DEFAULT false,
    "autoAddUser" BOOLEAN NOT NULL DEFAULT false,
    "fromUnionOrg" BOOLEAN NOT NULL DEFAULT false,
    "tags" VARCHAR(500),
    "order" INTEGER NOT NULL DEFAULT 0,
    "deptManagerUseridList" TEXT[],
    "outerDept" BOOLEAN NOT NULL DEFAULT false,
    "outerPermitDepts" INTEGER[],
    "outerPermitUsers" TEXT[],
    "orgDeptOwner" VARCHAR(50),
    "deptPerimits" INTEGER NOT NULL DEFAULT 0,
    "userPerimits" INTEGER NOT NULL DEFAULT 0,
    "outerDeptOnlySelf" BOOLEAN NOT NULL DEFAULT false,
    "sourceIdentifier" VARCHAR(100),
    "ext" TEXT,
    "hideSceneConfig" JSONB,
    "lastSyncAt" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "departments_pkey" PRIMARY KEY ("deptId")
);

-- CreateIndex
CREATE INDEX "departments_name_idx" ON "departments"("name");

-- CreateIndex
CREATE INDEX "departments_parentId_idx" ON "departments"("parentId");

-- CreateIndex
CREATE INDEX "departments_order_idx" ON "departments"("order");

-- CreateIndex
CREATE INDEX "departments_lastSyncAt_idx" ON "departments"("lastSyncAt");
