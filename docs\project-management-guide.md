# 项目管理系统使用指南

## 📋 概述

项目管理系统是一个完整的项目立项、品牌管理、预算控制一体化平台，支持项目全生命周期管理。

## 🚀 功能特性

### 核心功能
- ✅ 项目立项管理
- ✅ 品牌库维护
- ✅ 预算成本控制
- ✅ 利润自动计算
- ✅ 用户权限管理
- ✅ 文件附件支持
- ✅ 富文本编辑
- ✅ 数据统计分析

### 字段说明

#### 项目基本信息
- **单据类型**: 项目立项表（可扩展）
- **品牌**: 从品牌库选择，支持品牌管理
- **项目名称**: 项目的唯一标识名称
- **执行周期**: 项目开始和结束时间

#### 预算信息
- **项目规划预算**: 项目总预算金额
- **达人预算**: 分配给达人的预算
- **投流预算**: 广告投放预算
- **其他预算**: 其他费用预算

#### 成本信息
- **达人成本**: 实际达人费用
- **投流成本**: 实际广告投放费用
- **其他成本**: 其他实际费用
- **预估达人返点**: 达人返点金额

#### 利润计算（自动）
- **项目利润**: 规划预算 - 总成本 + 返点
- **项目毛利率**: 项目利润 / 规划预算 × 100%

#### 人员管理
- **执行PM**: 项目负责人（单选）
- **内容媒介**: 内容制作人员（多选）

#### 合同信息
- **合同类型**: 年框、季框、单次、PO单、京任务
- **项目结算规则**: 富文本格式的结算说明
- **KPI**: 富文本格式的关键指标

#### 附件管理
- **附件**: 支持多文件上传，包含文件名、大小、类型等信息

## 🎯 使用流程

### 1. 品牌管理

#### 创建品牌
1. 点击"🏷️ 品牌管理"按钮
2. 在品牌管理窗口中点击"➕ 新建品牌"
3. 填写品牌信息：
   - 品牌名称（必填）
   - 品牌描述（可选）
   - 品牌Logo URL（可选）
   - 状态（启用/禁用）
4. 点击"保存"完成创建

#### 管理品牌
- **编辑**: 点击品牌列表中的"编辑"按钮
- **删除**: 点击"删除"按钮（注意：有关联项目的品牌无法删除）
- **状态管理**: 可以启用或禁用品牌

### 2. 项目管理

#### 创建项目
1. 点击"➕ 新建项目"按钮
2. 填写项目基本信息：
   - 选择单据类型
   - 选择品牌
   - 输入项目名称
   - 设置执行周期

3. 填写预算信息：
   - 项目规划预算
   - 达人预算
   - 投流预算
   - 其他预算

4. 填写成本信息：
   - 达人成本
   - 投流成本
   - 其他成本
   - 预估达人返点

5. 设置人员和合同：
   - 选择执行PM
   - 选择内容媒介（可多选）
   - 选择合同类型
   - 填写结算规则和KPI

6. 点击"保存"完成创建

#### 项目查询和筛选
- **关键字搜索**: 在项目名称搜索框中输入关键字
- **品牌筛选**: 选择特定品牌查看相关项目
- **合同类型筛选**: 按合同类型筛选项目
- **状态筛选**: 按项目状态筛选
- **时间范围**: 按项目执行时间筛选

#### 项目操作
- **查看详情**: 点击项目名称查看详细信息
- **编辑项目**: 点击"编辑"按钮修改项目信息
- **删除项目**: 点击"删除"按钮（需确认）

## 📊 数据统计

### 统计卡片
系统首页显示关键统计信息：
- **总项目数**: 系统中所有项目的数量
- **活跃项目**: 状态为"进行中"的项目数量
- **总预算**: 所有项目的规划预算总和
- **总利润**: 所有项目的利润总和
- **平均毛利率**: 所有项目的平均毛利率

### 详细统计
通过API可以获取更详细的统计信息：
- 按品牌分组的项目统计
- 按合同类型分组的项目统计
- 项目状态分布
- 预算和利润趋势

## 🔧 API接口

### 项目管理接口

#### 获取项目列表
```http
GET /api/projects?page=1&pageSize=20&brandId=xxx&status=active
```

#### 创建项目
```http
POST /api/projects
Content-Type: application/json

{
  "documentType": "project_initiation",
  "brandId": "brand-001",
  "projectName": "春节营销活动",
  "period": {
    "startDate": "2024-02-01",
    "endDate": "2024-02-29"
  },
  "budget": {
    "planningBudget": 100000,
    "influencerBudget": 40000,
    "adBudget": 30000,
    "otherBudget": 10000
  },
  "cost": {
    "influencerCost": 35000,
    "adCost": 28000,
    "otherCost": 8000,
    "estimatedInfluencerRebate": 2000
  },
  "executorPM": "user-001",
  "contentMediaIds": ["user-002", "user-003"],
  "contractType": "single",
  "settlementRules": "按月结算",
  "kpi": "目标曝光量1000万"
}
```

#### 更新项目
```http
PUT /api/projects
Content-Type: application/json

{
  "id": "project-001",
  "projectName": "更新后的项目名称",
  ...
}
```

#### 删除项目
```http
DELETE /api/projects/{id}
```

### 品牌管理接口

#### 获取品牌列表
```http
GET /api/brands?status=active
```

#### 创建品牌
```http
POST /api/brands
Content-Type: application/json

{
  "name": "新品牌",
  "description": "品牌描述",
  "logo": "https://example.com/logo.png"
}
```

#### 更新品牌
```http
PUT /api/brands
Content-Type: application/json

{
  "id": "brand-001",
  "name": "更新后的品牌名称",
  "status": "active"
}
```

#### 删除品牌
```http
DELETE /api/brands/{id}
```

### 统计接口

#### 获取项目统计
```http
GET /api/projects/stats
```

### 文件上传接口

#### 上传文件
```http
POST /api/upload
Content-Type: multipart/form-data

file: [文件数据]
```

## 🧪 测试

### 运行API测试
```bash
# 测试项目管理API
npm run test:project

# 测试所有API
npm run test:api

# 指定服务器地址测试
API_BASE_URL=http://localhost:8080 npm run test:project
```

### 测试覆盖
- ✅ 项目CRUD操作
- ✅ 品牌CRUD操作
- ✅ 数据验证和错误处理
- ✅ 查询和筛选功能
- ✅ 统计数据计算
- ✅ 文件上传功能

## 🔒 权限管理

### 用户角色
- **管理员**: 所有权限
- **项目经理**: 项目管理权限
- **普通用户**: 查看权限

### 权限控制
- 创建/编辑项目需要相应权限
- 品牌管理需要管理员权限
- 删除操作需要确认和权限验证

## 📱 移动端支持

### 响应式设计
- 支持手机、平板等移动设备
- 自适应布局和交互
- 触摸友好的操作界面

### 钉钉集成
- 与钉钉用户系统集成
- 支持钉钉免登录
- 获取钉钉用户和部门信息

## 🚀 部署和配置

### 环境要求
- Node.js 18+
- 钉钉企业内部应用
- 数据库（可选，当前使用内存存储）

### 配置步骤
1. 配置钉钉应用信息
2. 设置环境变量
3. 启动服务
4. 访问项目管理页面

### 生产环境
- 配置数据库持久化
- 设置文件存储服务
- 配置用户权限系统
- 启用HTTPS和安全策略

## 🔧 扩展开发

### 添加新字段
1. 更新类型定义 (`src/types/project.ts`)
2. 修改服务层逻辑 (`src/services/project.ts`)
3. 更新API接口 (`src/controllers/project.ts`)
4. 修改前端表单 (`public/project-management.html`)

### 自定义业务逻辑
- 修改利润计算公式
- 添加审批流程
- 集成外部系统
- 自定义报表功能

## 📚 相关文档

- [钉钉集成指南](dingtalk-login-guide.md)
- [API测试指南](api-testing-guide.md)
- [JSAPI问题排查](jsapi-troubleshooting.md)
- [项目实现总结](../DINGTALK_IMPLEMENTATION.md)

## 💡 最佳实践

1. **数据备份**: 定期备份项目和品牌数据
2. **权限控制**: 严格控制用户操作权限
3. **数据验证**: 前后端双重数据验证
4. **性能优化**: 大数据量时使用分页和索引
5. **用户体验**: 提供清晰的操作反馈和错误提示

## 🆘 常见问题

### Q: 如何修改利润计算公式？
A: 修改 `src/services/project.ts` 中的 `calculateProfit` 方法。

### Q: 如何添加新的合同类型？
A: 在 `src/types/project.ts` 的 `ContractType` 枚举中添加新类型。

### Q: 如何集成真实的用户系统？
A: 修改 `src/services/project.ts` 中的 `getUserInfo` 方法，调用钉钉API。

### Q: 如何启用数据库持久化？
A: 替换内存存储为数据库操作，推荐使用 PostgreSQL 或 MongoDB。
