version: '3.8'

services:
  cantv-ding-backend:
    image: registry.cn-hangzhou.aliyuncs.com/cantv-ding/cantv-ding-backend:${IMAGE_TAG:-latest}
    container_name: cantv-ding-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=${DATABASE_URL}
      - DINGTALK_APP_KEY=${DINGTALK_APP_KEY}
      - DINGTALK_APP_SECRET=${DINGTALK_APP_SECRET}
      - DINGTALK_CORP_ID=${DINGTALK_CORP_ID}
      - DINGTALK_AGENT_ID=${DINGTALK_AGENT_ID}
      - JWT_SECRET=${JWT_SECRET}
      - DINGTALK_API_BASE_URL=https://oapi.dingtalk.com
      - DINGTALK_NEW_API_BASE_URL=https://api.dingtalk.com
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    networks:
      - cantv-ding-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx反向代理和负载均衡
  nginx:
    image: nginx:alpine
    container_name: cantv-ding-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - cantv-ding-backend
    restart: unless-stopped
    networks:
      - cantv-ding-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: cantv-ding-postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-cantv_ding}
      - POSTGRES_USER=${POSTGRES_USER:-cantv_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "127.0.0.1:5432:5432"  # 只绑定到本地
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    networks:
      - cantv-ding-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-cantv_user} -d ${POSTGRES_DB:-cantv_ding}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: cantv-ding-redis
    ports:
      - "127.0.0.1:6379:6379"  # 只绑定到本地
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    networks:
      - cantv-ding-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 日志收集服务（可选）
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: cantv-ding-filebeat
    user: root
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/app:ro
      - ./nginx/logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    networks:
      - cantv-ding-network
    depends_on:
      - cantv-ding-backend
    profiles:
      - monitoring

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: cantv-ding-prometheus
    ports:
      - "127.0.0.1:9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    networks:
      - cantv-ding-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  cantv-ding-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
